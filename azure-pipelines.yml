trigger:
  - master

pool:
  vmImage: 'ubuntu-latest'

variables:
  - name: tag
    value: '1.0.$(Build.BuildId)'
  - name: imageName
    value: 'wexledu-nonprod/retail-service'
  - name: fullImageName
    value: 'dcr.wexledu.com/$(imageName)'
  - name: repositoryName
    value: 'retail-service'
  - name: M2_CACHE_FOLDER
    value: '$HOME/.m2/repository'

steps:
- task: Cache@2  # ##Not required, but <PERSON><PERSON><PERSON> will open projects with cache faster.
  inputs:
      key: '"$(Build.Repository.Name)" | "$(Build.SourceBranchName)" | "$(Build.SourceVersion)"'
      path: '$(Agent.TempDirectory)/qodana/cache'
      restoreKeys: |
        "$(Build.Repository.Name)" | "$(Build.SourceBranchName)"
        "$(Build.Repository.Name)"

- bash: 'wget https://api.adoptium.net/v3/binary/latest/21/ga/linux/x64/jdk/hotspot/normal/eclipse -O jdk-21_linux-x64_bin.tar.gz'
  displayName: 'Bash Script'

- task: JavaToolInstaller@0
  displayName: 'Use Java 21'
  inputs:
    versionSpec: 21
    jdkArchitectureOption: x64
    jdkSourceOption: LocalDirectory
    jdkFile: '$(build.sourcesdirectory)/jdk-21_linux-x64_bin.tar.gz'
    jdkDestinationDirectory: '$(agent.toolsDirectory)/jdk21'

- task: MavenAuthenticate@0
  inputs:
    artifactsFeeds: 'wexl-artifactory'

- task: Maven@4
  inputs:
    mavenPomFile: 'pom.xml'
    goals: 'package'
    publishJUnitResults: false
    javaHomeOption: 'JDKVersion'
    jdkVersionOption: default
    mavenVersionOption: 'Default'
    mavenAuthenticateFeed: true
    effectivePomSkip: false
    sonarQubeRunAnalysis: false
    sqMavenPluginVersionChoice: 'latest'
  env:
    JAVA_OPTS: $(java_opts)

- task: Docker@2
  displayName: Build the image
  inputs:
    repository: $(fullImageName)
    command: build
    Dockerfile: Dockerfile
    tags: |
      $(tag)

- script: |
    set -x
    nslookup dcr.wexledu.com
    curl -v --connect-timeout 10 https://dcr.wexledu.com/v2/ || true
    openssl s_client -connect dcr.wexledu.com:443 -servername dcr.wexledu.com -showcerts </dev/null || true
  displayName: 'Smoke-test registry connectivity'      

- task: Docker@2
  displayName: Push the docker image to private registry
  inputs:
    command: push
    repository: $(imageName)
    tags: |
      $(tag)
    containerRegistry: 'WexlDockerRegistry'

- task: HelmInstaller@1
  inputs:
    helmVersionToInstall: 'latest'

- task: HelmDeploy@0
  displayName: Helm package Retail
  inputs:
    command: 'package'
    chartPath: 'charts/retail-service'
    chartVersion: '$(tag)'
    save: false

- task: Bash@3
  inputs:
    targetType: 'inline'
    script: |
      cp -rf $(Build.ArtifactStagingDirectory)/retail-service-*.tgz $(Build.ArtifactStagingDirectory)/chart.tgz

- task: PublishBuildArtifacts@1
  inputs:
    pathToPublish: $(Build.ArtifactStagingDirectory)
    artifactName: Source
