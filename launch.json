{"configurations": [{"type": "java", "name": "Spring Boot-CloudApplication<cloud-lms>", "request": "launch", "cwd": "${workspaceFolder}", "mainClass": "com.wexl.CloudApplication", "projectName": "cloud-lms", "args": "--spring.profiles.active=local", "envFile": "${workspaceFolder}/.env"}], "inputs": [{"type": "promptString", "id": "mainClass", "description": "Enter the fully qualified class name (e.g. com.xyz.MainApp) or the java file path of the program entry"}, {"type": "promptString", "id": "projectName", "description": "Enter the preferred project name"}, {"type": "promptString", "id": "args", "description": "Enter the command line arguments passed to the program"}, {"type": "promptString", "id": "vmArgs", "description": "Enter the extra options and system properties for the JVM"}, {"type": "promptString", "id": "modulePaths", "description": "Enter the modulepaths for launching the JVM"}, {"type": "promptString", "id": "classPaths", "description": "Enter the classpaths for launching the JVM"}, {"type": "promptString", "id": "sourcePaths", "description": "Enter the extra source directories of the program"}, {"type": "promptString", "id": "encoding", "description": "Enter the file.encoding setting for the JVM"}]}