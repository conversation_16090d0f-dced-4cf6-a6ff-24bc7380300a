trigger:
  - implementation

pool:
  vmImage: 'ubuntu-latest'

variables:
  - name: tag
    value: '1.0.$(Build.BuildId)'
  - name: imageName
    value: 'retail-service'
  - name: repositoryName
    value: 'retail-service'
  - name: M2_CACHE_FOLDER
    value: '$HOME/.m2/repository'

steps:
- bash: 'wget https://api.adoptium.net/v3/binary/latest/21/ga/linux/x64/jdk/hotspot/normal/eclipse -O jdk-21_linux-x64_bin.tar.gz'
  displayName: 'Bash Script'

- task: JavaToolInstaller@0
  displayName: 'Use Java 21'
  inputs:
    versionSpec: 21
    jdkArchitectureOption: x64
    jdkSourceOption: LocalDirectory
    jdkFile: '$(build.sourcesdirectory)/jdk-21_linux-x64_bin.tar.gz'
    jdkDestinationDirectory: '$(agent.toolsDirectory)/jdk21'

- task: MavenAuthenticate@0
  inputs:
    artifactsFeeds: 'wexl-artifactory'

- task: Maven@4
  inputs:
    mavenPomFile: 'pom.xml'
    goals: 'package deploy'
    publishJUnitResults: false
    javaHomeOption: 'JDKVersion'
    jdkVersionOption: default
    mavenVersionOption: 'Default'
    mavenAuthenticateFeed: true
    effectivePomSkip: false
    sonarQubeRunAnalysis: false
    sqMavenPluginVersionChoice: 'latest'
  env:
    JAVA_OPTS: $(java_opts)

- task: Bash@3
  inputs:
    targetType: 'inline'
    script: |
      # Trigger Implementation Builds
      
      
      curl --location 'https://dev.azure.com/wexl/implementations/_apis/build/builds?api-version=7.1-preview.7' \
      --header 'Content-Type: application/json' \
      --header 'Authorization: Basic $(ENCODEDPAT)' \
      --data '{ 
              "definition": {
                  "id": "85"
              }
      }'
      
      curl --location 'https://dev.azure.com/wexl/implementations/_apis/build/builds?api-version=7.1-preview.7' \
      --header 'Content-Type: application/json' \
      --header 'Authorization: Basic $(ENCODEDPAT)' \
      --data '{ 
              "definition": {
                  "id": "86"
              }
      }'
      
      curl --location 'https://dev.azure.com/wexl/implementations/_apis/build/builds?api-version=7.1-preview.7' \
      --header 'Content-Type: application/json' \
      --header 'Authorization: Basic $(ENCODEDPAT)' \
      --data '{ 
              "definition": {
                  "id": "90"
              }
      }'