package com.wexl.chatbot;

import com.wexl.chatbot.dto.WhatsAppBotDto;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
@Slf4j
public class Msg91Sender {
  @Value("${app.msg91.whatsAppOutBoundUrl}")
  private String whatsAppOutBoundUrl;

  @Value("${app.msg91.integratedNumber}")
  private String integratedNumber;

  @Value("${app.msg91.authkey}")
  private String msg91Authkey;

  public void sendWhatsAppBotMessage(String message, String recipientNumber) {
    RestTemplate restTemplate = new RestTemplate();
    String url =
        whatsAppOutBoundUrl
            + "?integrated_number="
            + integratedNumber
            + "&recipient_number="
            + recipientNumber
            + "&content_type=text";

    HttpHeaders headers = new HttpHeaders();
    headers.set("accept", "application/json");
    headers.set("authkey", msg91Authkey);
    headers.set("content-type", "application/json");

    Map<String, String> body = new HashMap<>();
    body.put("text", message);

    HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(body, headers);

    var response =
        restTemplate.exchange(url, HttpMethod.POST, requestEntity, WhatsAppBotDto.Response.class);
    if ("success".equals(response.getBody().status())) {
      log.info("Successfully sent message {}", response);
    } else {
      log.error("Failed to send message {}", response);
    }
  }
}
