package com.wexl.pallavi.preprimary.controller;

import com.wexl.holisticreportcards.ProgressCardService;
import com.wexl.holisticreportcards.dto.ProgressCardDto;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}/pallavi-primary-progress-card")
@RequiredArgsConstructor
public class ProgressCardController {

  private final ProgressCardService pallaviPrimaryProgressCardService;

  @PostMapping()
  public ProgressCardDto.ProgressCardResponse saveProgressCard(
      @PathVariable("orgSlug") String orgSlug, @RequestBody ProgressCardDto.Request request) {
    return pallaviPrimaryProgressCardService.saveProgressCard(orgSlug, request);
  }

  @GetMapping("/students/{studentAuthUserId}")
  public ProgressCardDto.Response getProgressCardById(
      @PathVariable("orgSlug") String orgSlug,
      @PathVariable("studentAuthUserId") String studentAuthUserId) {
    return pallaviPrimaryProgressCardService.getProgressCardById(orgSlug, studentAuthUserId);
  }
}
