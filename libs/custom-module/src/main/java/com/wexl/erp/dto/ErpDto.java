package com.wexl.erp.dto;

import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Builder;

public class ErpDto {
  @Builder
  public record ErpTeacherResponse(
      int id,
      String teacherName,
      @NotNull String teacherCode,
      String schoolId,
      String email,
      String phone,
      String gender,
      String orgSlug) {}

  @Builder
  public record ErpStudentResponse(
      int id,
      String firstName,
      String lastName,
      @NotNull String studentCode,
      String schoolId,
      String email,
      String phone,
      String branchCode,
      String branchName,
      String rollNumber,
      String classRollNo,
      String grade,
      String sectionName,
      String sectionUuid,
      String gender,
      String fatherName,
      String fatherPhone,
      String motherName,
      String motherPhone,
      String fatherEmail,
      String motherEmail,
      String orgSlug) {}

  @Builder
  public record ErpEntityChange(
      String changeType,
      String commitId,
      Integer id,
      String dateTime,
      String employeeCode,
      String type,
      ErpTeacherResponse teacherResponse,
      ErpStudentResponse studentResponse) {}

  @Builder
  public record ErpEntityChangeResponse(List<ErpEntityChange> erpEntityChanges) {}
}
