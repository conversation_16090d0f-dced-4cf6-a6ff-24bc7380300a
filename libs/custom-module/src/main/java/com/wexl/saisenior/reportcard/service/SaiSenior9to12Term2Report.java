package com.wexl.saisenior.reportcard.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleStudentAttendanceRepository;
import com.wexl.retail.offlinetest.service.pointscale.PointScaleEvaluator;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import com.wexl.retail.term.repository.TermRepository;
import com.wexl.saisenior.SaiSeniorBaseReportCardDefinition;
import com.wexl.saisenior.reportcard.dto.ReportCardDto;
import com.wexl.saisenior.reportcard.dto.SaiSenior9to12Term2ReportDto;
import com.wexl.saisenior.reportcard.repository.SaiSeniorRepository;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class SaiSenior9to12Term2Report extends SaiSeniorBaseReportCardDefinition {
  private final List<String> terms = List.of("t1", "t2");
  private final ReportCardService reportCardService;
  private final SaiSeniorRepository saiSeniorRepository;
  private final TermAssessmentRepository termAssessmentRepository;
  private final OfflineTestScheduleStudentAttendanceRepository
      offlineTestScheduleStudentAttendanceRepository;
  private final TermRepository termRepository;
  private final PointScaleEvaluator pointScaleEvaluator;

  @Override
  public Map<String, Object> build(
      User user, Organization org, com.wexl.retail.offlinetest.dto.ReportCardDto.Request request) {
    var header = buildHeader(user.getStudentInfo(), org);
    var body = buildBody(user);
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("sai-senior-9th-12th-final-progress-report.xml");
  }

  public SaiSenior9to12Term2ReportDto.Body buildBody(User user) {
    var student = user.getStudentInfo();
    var guardians = student.getGuardians();
    var father =
        guardians.stream().filter(x -> x.getRelationType().equals(GuardianRole.FATHER)).findAny();
    var mother =
        guardians.stream().filter(x -> x.getRelationType().equals(GuardianRole.MOTHER)).findAny();
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("dd-MM-yyyy");
    String formattedDateOfBirth =
        dateOfBirth
            .map(StudentAttributeValueModel::getValue)
            .map(LocalDate::parse)
            .map(date -> date.format(outputFormatter))
            .orElse(null);
    Optional<StudentAttributeValueModel> admissionNo =
        reportCardService.getStudentAttributeValue(student, "admission_no");
    var gradeSlug = student.getSection().getGradeSlug();
    var studentAttendance = getAttendance(student.getId());
    var marks =
        saiSeniorRepository.getStudentT2ReportByStudentAndAssessments(
            student.getId(), student.getSection().getGradeSlug(), terms);
    if (Objects.isNull(marks) || marks.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report has not been generated");
    }
    var firstTable = buildFirstTable(marks);
    return SaiSenior9to12Term2ReportDto.Body.builder()
        .name((user.getFirstName() + " " + user.getLastName()).toUpperCase())
        .dateOfBirth(formattedDateOfBirth)
        .className(student.getSection().getName())
        .rollNumber(student.getRollNumber())
        .admissionNumber(admissionNo.map(StudentAttributeValueModel::getValue).orElse(null))
        .fathersName(
            father
                .map(guardian -> guardian.getFirstName() + " " + guardian.getLastName())
                .orElse(null))
        .mothersName(
            mother
                .map(guardian -> guardian.getFirstName() + " " + guardian.getLastName())
                .orElse(null))
        .firstTable(firstTable)
        .secondTable(buildSecondTable(marks))
        .thirdTable(thirdTable(firstTable, gradeSlug, studentAttendance))
        .generalRemark(studentAttendance.remarks())
        .build();
  }

  private SaiSenior9to12Term2ReportDto.SecondTable buildSecondTable(
      List<LowerGradeReportCardData> marks) {
    List<LowerGradeReportCardData> data;
    data =
        marks.stream()
            .filter(
                x ->
                    x.getCategory().equals(SubjectsCategoryEnum.CO_SCHOLASTIC.name())
                        && x.getType().equals(SubjectsTypeEnum.CURRICULAR_ACTIVITIES.name()))
            .sorted(
                Comparator.comparing(LowerGradeReportCardData::getSeqNo, Comparator.naturalOrder()))
            .toList();
    if (data.isEmpty()) {
      return SaiSenior9to12Term2ReportDto.SecondTable.builder().build();
    }
    return SaiSenior9to12Term2ReportDto.SecondTable.builder()
        .marks(buildSecondTableMarks(data))
        .build();
  }

  private List<SaiSenior9to12Term2ReportDto.SecondTableMarks> buildSecondTableMarks(
      List<LowerGradeReportCardData> data) {
    List<SaiSenior9to12Term2ReportDto.SecondTableMarks> marksList = new ArrayList<>();
    List<String> subjects;
    subjects = data.stream().map(LowerGradeReportCardData::getSubjectName).distinct().toList();
    subjects.forEach(
        subject -> {
          var subjectData = data.stream().filter(x -> x.getSubjectName().equals(subject)).toList();

          var optionalTerm2 =
              subjectData.stream().filter(x -> x.getAssessmentSlug().equals("s.a")).findAny();

          String term2Grade =
              optionalTerm2
                  .map(
                      term2 -> {
                        if ("false".equalsIgnoreCase(term2.getIsAttended())
                            && term2.getRemarks() == null) {
                          return "AB";
                        }
                        return calculateGrade(
                            term2.getMarks(), term2.getTotalMarks(), term2.getRemarks());
                      })
                  .orElse("");
          marksList.add(
              SaiSenior9to12Term2ReportDto.SecondTableMarks.builder()
                  .name(subject)
                  .grade(term2Grade)
                  .build());
        });
    return marksList;
  }

  private String calculateGrade(Double marks, Double totalMarks, String remarks) {
    return marks == null || marks == 0
        ? (remarks == null ? null : remarks.substring(0, 2).toUpperCase())
        : pointScaleEvaluator.evaluate("5point", BigDecimal.valueOf(((marks / totalMarks) * 100)));
  }

  private SaiSenior9to12Term2ReportDto.ThirdTable thirdTable(
      SaiSenior9to12Term2ReportDto.FirstTable firstTable,
      String gradeSlug,
      ReportCardDto.Attendance studentAttendance) {
    var percentage = calculatePercentage(firstTable, gradeSlug);
    return SaiSenior9to12Term2ReportDto.ThirdTable.builder()
        .result(calculateOverAllGrade(firstTable, gradeSlug))
        .percentage(percentage)
        .attendance(studentAttendance.attendancePercentage())
        .build();
  }

  private SaiSenior9to12Term2ReportDto.FirstTable buildFirstTable(
      List<LowerGradeReportCardData> marks) {

    return SaiSenior9to12Term2ReportDto.FirstTable.builder()
        .subject(buildFirstTableMarks(marks))
        .build();
  }

  private List<SaiSenior9to12Term2ReportDto.Marks> buildFirstTableMarks(
      List<LowerGradeReportCardData> marks) {
    List<SaiSenior9to12Term2ReportDto.Marks> marksList = new ArrayList<>();

    var data =
        marks.stream()
            .filter(x -> x.getCategory().equals(SubjectsCategoryEnum.SCHOLASTIC.name()))
            .toList();

    var subjectsList =
        data.stream()
            .sorted(Comparator.comparing(LowerGradeReportCardData::getSeqNo))
            .map(LowerGradeReportCardData::getSubjectName)
            .distinct()
            .toList();
    subjectsList.forEach(
        subject -> {
          var subjectData = data.stream().filter(x -> x.getSubjectName().equals(subject)).toList();
          var fa = buildFa(subjectData, subject, "t1");
          var fa2 = buildFa(subjectData, subject, "t2");
          var faTotalMarks = getTotalMarks(subjectData, subject, "i.a", "t1");
          var fa2TotalMarks = getTotalMarks(subjectData, subject, "i.a", "t2");
          var sa = buildMarks(subjectData, "s.a", subject, "t1");
          var sa2 = buildMarks(subjectData, "s.a", subject, "t2");
          var saTotalMarks = getTotalMarks(subjectData, subject, "s.a", "t1");
          var sa2TotalMarks = getTotalMarks(subjectData, subject, "s.a", "t2");

          marksList.add(
              SaiSenior9to12Term2ReportDto.Marks.builder()
                  .name(subject)
                  .saTotalMarks(saTotalMarks)
                  .sa2TotalMarks(sa2TotalMarks)
                  .faTotalMarks(faTotalMarks)
                  .fa2TotalMarks(fa2TotalMarks)
                  .term1IA(fa)
                  .term2IA(fa2)
                  .term1SA(sa)
                  .term2SA(sa2)
                  .build());
        });

    var sortedMarks = sortMarks(marksList);
    return updatedMarks(marksList, sortedMarks);
  }

  private List<SaiSenior9to12Term2ReportDto.Marks> updatedMarks(
      List<SaiSenior9to12Term2ReportDto.Marks> marksList,
      List<SaiSenior9to12Term2ReportDto.Marks> sortedMarks) {
    List<SaiSenior9to12Term2ReportDto.Marks> response = new ArrayList<>();

    marksList.forEach(
        marks -> {
          var subjectName =
              sortedMarks.stream().filter(x -> x.name().equals(marks.name())).findAny();

          if (subjectName.isPresent()) {
            var data = subjectName.get();
            boolean isValidDataInternalAssessment =
                !"AB".equals(data.term1IA())
                    && !"PL".equals(data.term1IA())
                    && !"ML".equals(data.term1IA());

            boolean isValidDataSummativeAssessment =
                !"AB".equals(data.term1SA())
                    && !"PL".equals(data.term1SA())
                    && !"ML".equals(data.term1SA());
            boolean isValidDataInternalAssessment2 =
                !"AB".equals(data.term2IA())
                    && !"PL".equals(data.term2IA())
                    && !"ML".equals(data.term2IA());

            boolean isValidDataSummativeAssessment2 =
                !"AB".equals(data.term2SA())
                    && !"PL".equals(data.term2SA())
                    && !"ML".equals(data.term2SA());
            response.add(
                SaiSenior9to12Term2ReportDto.Marks.builder()
                    .seqNo(marks.seqNo())
                    .name(marks.name())
                    .colSpan(data.colSpan() == null ? 0 : data.colSpan())
                    .term1IA(
                        isValidDataInternalAssessment
                            ? formatScore(Double.parseDouble(data.term1IA()), marks.faTotalMarks())
                            : marks.term1IA())
                    .term1SA(
                        isValidDataSummativeAssessment
                            ? formatScore(Double.parseDouble(data.term1SA()), marks.saTotalMarks())
                            : marks.term1SA())
                    .term2IA(
                        isValidDataInternalAssessment2
                            ? formatScore(Double.parseDouble(data.term2IA()), marks.fa2TotalMarks())
                            : marks.term2IA())
                    .term2SA(
                        isValidDataSummativeAssessment2
                            ? formatScore(Double.parseDouble(data.term2SA()), marks.sa2TotalMarks())
                            : marks.term2SA())
                    .percentage(data.percentage() == null ? null : data.percentage())
                    .build());
          } else {

            boolean isValidInternalAssessment =
                isValidAssessment(marks.term1IA()) && isNumeric(marks.term1IA());
            boolean isValidSummativeAssessment =
                isValidAssessment(marks.term1SA()) && isNumeric(marks.term1SA());
            boolean isValidDataInternalAssessment2 =
                isValidAssessment(marks.term2IA()) && isNumeric(marks.term2IA());
            boolean isValidDataSummativeAssessment2 =
                isValidAssessment(marks.term2SA()) && isNumeric(marks.term2SA());

            var totalMarks =
                marks.faTotalMarks()
                    + marks.fa2TotalMarks()
                    + marks.saTotalMarks()
                    + marks.sa2TotalMarks();
            var total = totalMarks == 200 ? 2 : totalMarks;
            String percentage = null;
            double validMarksSum = 0.0;

            if (isValidInternalAssessment) {
              validMarksSum += Double.parseDouble(marks.term1IA());
            }
            if (isValidSummativeAssessment) {
              validMarksSum += Double.parseDouble(marks.term1SA());
            }
            if (isValidDataInternalAssessment2) {
              validMarksSum += Double.parseDouble(marks.term2IA());
            }
            if (isValidDataSummativeAssessment2) {
              validMarksSum += Double.parseDouble(marks.term2SA());
            }

            if (validMarksSum > 0) {
              var ratio = (validMarksSum / total);
              percentage =
                  String.format(
                      "%.2f", String.valueOf(ratio).startsWith("0.") ? ratio * 100 : ratio);
            }

            response.add(
                SaiSenior9to12Term2ReportDto.Marks.builder()
                    .seqNo(marks.seqNo())
                    .name(marks.name())
                    .colSpan(marks.colSpan() == null ? 1 : marks.colSpan())
                    .term1IA(
                        isValidInternalAssessment
                            ? formatScore(Double.parseDouble(marks.term1IA()), marks.faTotalMarks())
                            : marks.term1IA())
                    .term1SA(
                        isValidSummativeAssessment
                            ? formatScore(Double.parseDouble(marks.term1SA()), marks.saTotalMarks())
                            : marks.term1SA())
                    .term2IA(
                        isValidDataInternalAssessment2
                            ? formatScore(
                                Double.parseDouble(marks.term2IA()), marks.fa2TotalMarks())
                            : marks.term2IA())
                    .term2SA(
                        isValidDataSummativeAssessment2
                            ? formatScore(
                                Double.parseDouble(marks.term2SA()), marks.sa2TotalMarks())
                            : marks.term2SA())
                    .percentage(String.valueOf(percentage == null ? "0" : percentage))
                    .build());
          }
        });

    return response;
  }

  private boolean isValidAssessment(String mark) {
    return !("AB".equals(mark) || "PL".equals(mark) || "ML".equals(mark));
  }

  private boolean isNumeric(String mark) {
    try {
      Double.parseDouble(mark);
      return true;
    } catch (NumberFormatException e) {
      return false;
    }
  }

  private List<SaiSenior9to12Term2ReportDto.Marks> sortMarks(
      List<SaiSenior9to12Term2ReportDto.Marks> subjectData) {
    List<String> commonSubjects = Arrays.asList("english", "social", "science");
    List<SaiSenior9to12Term2ReportDto.Marks> sortedMarks = new ArrayList<>();

    commonSubjects.forEach(
        subject -> {
          var filteredSubjects =
              subjectData.stream()
                  .filter(x -> !x.name().toLowerCase().contains("home"))
                  .filter(x -> x.name().toLowerCase().contains(subject))
                  .toList();

          if (!filteredSubjects.isEmpty()) {
            double totalSummative = getTotalMarksForAssessment(filteredSubjects, "summative");
            double totalInternal = getTotalMarksForAssessment(filteredSubjects, "internal");
            String percentage =
                String.format(
                    "%.2f",
                    formatMarks((totalInternal + totalSummative) / (filteredSubjects.size() * 2)));

            for (int i = 0; i < filteredSubjects.size(); i++) {
              SaiSenior9to12Term2ReportDto.Marks mark = filteredSubjects.get(i);
              long colSpan = 0L;

              if (i == filteredSubjects.size() - 1) {
                colSpan = filteredSubjects.size();
              }

              sortedMarks.add(
                  SaiSenior9to12Term2ReportDto.Marks.builder()
                      .name(mark.name())
                      .term1IA(mark.term1IA())
                      .term2IA(mark.term2IA())
                      .faTotalMarks(mark.faTotalMarks())
                      .saTotalMarks(mark.saTotalMarks())
                      .term1SA(String.valueOf(mark.term1SA()))
                      .term2SA(String.valueOf(mark.term2SA()))
                      .percentage(i == 0 ? percentage : null)
                      .seqNo(mark.seqNo())
                      .colSpan(colSpan)
                      .build());
            }
          }
        });

    return sortedMarks;
  }

  private double getTotalMarksForAssessment(
      List<SaiSenior9to12Term2ReportDto.Marks> marks, String type) {
    return marks.stream()
        .flatMap(
            mark ->
                Stream.of(
                    type.equals("summative") ? mark.term1SA() : mark.term1IA(),
                    type.equals("summative") ? mark.term2SA() : mark.term2IA()))
        .filter(assessment -> isValidAssessment(assessment) && isNumeric(assessment))
        .mapToDouble(Double::parseDouble)
        .sum();
  }

  private String formatScore(double score, double total) {
    if (score == (long) score) {
      return String.format("%d/%d", (long) score, (long) total);
    } else {
      return String.format("%.2f/%.0f", score, total);
    }
  }

  private Double getTotalMarks(
      List<LowerGradeReportCardData> subjectData,
      String subject,
      String assessmentSlug,
      String termSlug) {
    return subjectData.stream()
        .filter(
            x ->
                x.getSubjectName().equals(subject)
                    && x.getAssessmentSlug().equals(assessmentSlug)
                    && x.getTermSlug().equals(termSlug))
        .map(
            x ->
                x.getIsAttended() == null || x.getIsAttended().equals("False")
                    ? "A"
                    : String.valueOf(x.getTotalMarks()))
        .reduce(
            (mark1, mark2) -> {
              if ("A".equals(mark1) || "A".equals(mark2)) return "A";
              return String.valueOf(Math.max(Double.parseDouble(mark1), Double.parseDouble(mark2)));
            })
        .map(mark -> "A".equals(mark) ? 0.0 : Double.parseDouble(mark))
        .orElse(0.0);
  }

  private String buildMarks(
      List<LowerGradeReportCardData> subjectData,
      String assessmentSlug,
      String subjectName,
      String termSlug) {
    return subjectData.stream()
        .filter(
            x ->
                x.getSubjectName().equals(subjectName)
                    && x.getAssessmentSlug().equals(assessmentSlug)
                    && x.getTermSlug().equals(termSlug))
        .findAny()
        .map(
            fa -> {
              if (fa.getIsAttended() == null || "false".equals(fa.getIsAttended())) {
                if (fa.getRemarks() != null) {
                  if (fa.getRemarks().contains("ML")) {
                    return "ML";
                  } else if (fa.getRemarks().contains("PL")) {
                    return "PL";
                  } else if (fa.getRemarks().contains("Absent")) {
                    return "AB";
                  }
                }
                return "AB";
              } else {
                return fa.getMarks() != null ? fa.getMarks().toString() : "-";
              }
            })
        .orElse("-");
  }

  private String buildFa(
      List<LowerGradeReportCardData> subjectData, String subject, String termSlug) {
    boolean anyValidMarks =
        subjectData.stream()
            .filter(
                x ->
                    x.getSubjectName().equals(subject)
                        && x.getAssessmentSlug().equals("i.a")
                        && x.getTermSlug().equals(termSlug)
                        && x.getMarks() != null
                        && x.getIsAttended().equals("true")
                        && x.getRemarks() == null)
            .anyMatch(x -> true);

    if (anyValidMarks) {
      double highestMark =
          subjectData.stream()
              .filter(
                  x ->
                      x.getSubjectName().equals(subject)
                          && x.getAssessmentSlug().equals("i.a")
                          && x.getTermSlug().equals(termSlug)
                          && x.getMarks() != null)
              .map(LowerGradeReportCardData::getMarks)
              .max(Double::compareTo)
              .orElse(0.0);

      return String.valueOf(highestMark);
    } else {
      return subjectData.stream()
          .filter(x -> x.getSubjectName().equals(subject) && x.getAssessmentSlug().equals("i.a"))
          .map(
              x -> {
                String remarks = x.getRemarks();
                if (remarks != null) {
                  if (remarks.contains("ML")) return "ML";
                  if (remarks.contains("PL")) return "PL";
                  if (remarks.contains("Absent")) return "AB";
                }
                return (x.getIsAttended().equals("false") && remarks == null) ? "AB" : null;
              })
          .filter(Objects::nonNull)
          .findFirst()
          .orElse("0");
    }
  }

  private Map<String, Double> calculateTotalPercentage(
      SaiSenior9to12Term2ReportDto.FirstTable firstTable, String gradeSlug) {
    int topSubjectsCount = 0;
    if ("x".equals(gradeSlug) || "ix".equals(gradeSlug)) {
      topSubjectsCount = 4;
    } else if ("xi".equals(gradeSlug) || "xii".equals(gradeSlug)) {
      topSubjectsCount = 3;
    }

    Optional<Double> englishPercentage =
        firstTable.subject().stream()
            .filter(
                mark -> mark.name().toLowerCase().contains("english") && mark.percentage() != null)
            .map(x -> Double.parseDouble(x.percentage()))
            .findFirst();

    List<Double> otherPercentages =
        firstTable.subject().stream()
            .filter(
                mark -> !mark.name().toLowerCase().contains("english") && mark.percentage() != null)
            .map(x -> Double.parseDouble(x.percentage()))
            .filter(Objects::nonNull)
            .sorted(Comparator.reverseOrder())
            .limit(topSubjectsCount)
            .toList();

    double totalPercentage =
        englishPercentage.orElse(0.0)
            + otherPercentages.stream().mapToDouble(Double::doubleValue).sum();

    int subjectCount =
        englishPercentage.isPresent() ? otherPercentages.size() + 1 : otherPercentages.size();

    Map<String, Double> result = new HashMap<>();
    result.put("totalPercentage", totalPercentage);
    result.put("subjectCount", (double) subjectCount);

    return result;
  }

  private double calculatePercentage(
      SaiSenior9to12Term2ReportDto.FirstTable firstTable, String gradeSlug) {
    Map<String, Double> result = calculateTotalPercentage(firstTable, gradeSlug);
    double totalPercentage = result.get("totalPercentage");
    int subjectCount = result.get("subjectCount").intValue();

    return subjectCount == 0 ? 0.0 : formatMarks((totalPercentage / subjectCount) * 100.0 / 100.0);
  }

  private String calculateOverAllGrade(
      SaiSenior9to12Term2ReportDto.FirstTable firstTable, String gradeSlug) {
    boolean isEnglishFailing =
        firstTable.subject().stream()
            .filter(
                mark -> mark.name().toLowerCase().contains("english") && mark.percentage() != null)
            .anyMatch(
                mark -> determinePassOrFail(Double.parseDouble(mark.percentage())).equals("Fail"));

    if (isEnglishFailing) {
      return "Not Clear";
    }
    long failingSubjectsCount =
        firstTable.subject().stream()
            .filter(
                mark -> !mark.name().toLowerCase().contains("english") && mark.percentage() != null)
            .filter(
                mark -> determinePassOrFail(Double.parseDouble(mark.percentage())).equals("Fail"))
            .count();

    if (failingSubjectsCount == 1) {
      return "Pass";
    } else if (failingSubjectsCount > 1) {
      return "Not Clear";
    }

    double calculatedPercentage = calculatePercentage(firstTable, gradeSlug);
    return String.format("Pass", calculatedPercentage);
  }

  private ReportCardDto.Attendance getAttendance(long studentId) {
    var term = termRepository.findBySlug("t2").orElseThrow();
    var termAssessment = termAssessmentRepository.findBySlugAndTerm("s.a", term);
    if (termAssessment.isEmpty()) {
      return ReportCardDto.Attendance.builder().build();
    }
    var studentAttendance =
        offlineTestScheduleStudentAttendanceRepository.getOfflineTestStudentAttendance(
            studentId, termAssessment.get().getId());
    return buildAttendance(studentId, studentAttendance);
  }
}
