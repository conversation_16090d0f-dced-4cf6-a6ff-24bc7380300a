package com.wexl.registry.service;

import com.wexl.registry.dto.HarborDto;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import java.util.Comparator;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.support.BasicAuthenticationInterceptor;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

@Service
public class HarborRegistryService implements InitializingBean {
  private static final Logger log = LoggerFactory.getLogger(HarborRegistryService.class);
  private static final String PROJECT_NAME = "wexledu-nonprod";
  private static final int MAX_IMAGES_TO_KEEP = 3;
  private static final int DAYS_TO_KEEP = 7;

  @Value("${harbor.api.url:https://dcr.wexledu.com/api/v2.0}")
  private String harborApiUrl;

  @Value("${harbor.api.username:admin}")
  private String harborUsername;

  @Value("${harbor.api.password:Harbor12345}")
  private String harborPassword;

  private final RestTemplate restTemplate = new RestTemplate();

  /**
   * Cleans up old images from all repositories in the wexledu-nonprod project. Keeps only the most
   * recent 3 images per repository.
   */
  public void cleanupOldImages() {
    log.info("Starting cleanup of old images in project: {}", PROJECT_NAME);

    try {
      // Get all repositories in the project
      List<HarborDto.Repository> repositories = getRepositories(PROJECT_NAME);
      log.info("Found {} repositories in project {}", repositories.size(), PROJECT_NAME);

      // Process each repository
      for (HarborDto.Repository repository : repositories) {
        cleanupRepositoryImages(trimProjectNameInRepository(PROJECT_NAME, repository.name()));
      }

      log.info("Completed cleanup of old images in project: {}", PROJECT_NAME);
    } catch (Exception e) {
      log.error("Error during cleanup of old images", e);
      throw new RuntimeException("Failed to cleanup old images", e);
    }
  }

  private String trimProjectNameInRepository(String projectName, String name) {
    if (name.startsWith(projectName + "/")) {
      return name.substring(projectName.length() + 1);
    }
    return name;
  }

  /** Gets all repositories in the specified project with pagination support. */
  private List<HarborDto.Repository> getRepositories(String projectName) {
    String url =
        UriComponentsBuilder.fromHttpUrl(harborApiUrl)
            .path("/projects/{projectName}/repositories")
            .queryParam("page_size", 30) // Set page size to 30
            .buildAndExpand(projectName)
            .toUriString();

    log.debug("Getting repositories from: {}", url);

    ResponseEntity<List<HarborDto.Repository>> response =
        restTemplate.exchange(url, HttpMethod.GET, null, new ParameterizedTypeReference<>() {});

    return response.getBody();
  }

  /** Cleans up old images in the specified repository. Keeps only the most recent 3 images. */
  private void cleanupRepositoryImages(String repositoryName) {
    log.info("Cleaning up repository: {}", repositoryName);
    try {
      // Get all artifacts in the repository
      List<HarborDto.Artifact> artifacts = getArtifacts(repositoryName);
      log.info("Found {} artifacts in repository {}", artifacts.size(), repositoryName);

      if (artifacts.isEmpty()) {
        return;
      }

      log.info("Found {} artifacts in repository {}", artifacts.size(), repositoryName);

      // Sort artifacts by push time (newest first)
      artifacts.sort(Comparator.comparing(HarborDto.Artifact::pushTime).reversed());

      // Keep only the most recent MAX_IMAGES_TO_KEEP artifacts
      List<HarborDto.Artifact> artifactsToKeep =
          artifacts.stream().limit(MAX_IMAGES_TO_KEEP).toList();

      // Identify artifacts to delete
      List<HarborDto.Artifact> artifactsToDelete =
          artifacts.stream().filter(artifact -> !artifactsToKeep.contains(artifact)).toList();

      log.info(
          "Keeping {} artifacts and deleting {} artifacts in repository {}",
          artifactsToKeep.size(),
          artifactsToDelete.size(),
          repositoryName);

      // Delete the old artifacts
      for (HarborDto.Artifact artifact : artifactsToDelete) {
        deleteArtifact(repositoryName, artifact.digest());
      }

    } catch (Exception e) {
      log.error("Error cleaning up repository: {}", repositoryName, e);
      // Continue with other repositories even if one fails
    }
  }

  /** Gets all artifacts in the specified repository with pagination support. */
  private List<HarborDto.Artifact> getArtifacts(String repositoryName) {
    String url =
        UriComponentsBuilder.fromHttpUrl(harborApiUrl)
            .path("/projects/{projectName}/repositories/{repositoryName}/artifacts")
            .queryParam("with_tag", true)
            .queryParam("page_size", 100) // Set page size to 100
            .buildAndExpand(PROJECT_NAME, repositoryName)
            .toUriString();

    log.debug("Getting artifacts from: {}", url);

    ResponseEntity<List<HarborDto.Artifact>> response =
        restTemplate.exchange(url, HttpMethod.GET, null, new ParameterizedTypeReference<>() {});

    return response.getBody();
  }

  /** Deletes the specified artifact. */
  private void deleteArtifact(String repositoryName, String digest) {
    String url =
        harborApiUrl
            + "/projects/"
            + HarborRegistryService.PROJECT_NAME
            + "/repositories/"
            + repositoryName
            + "/artifacts/"
            + digest;
    log.debug("Deleting artifact: {}", url);

    restTemplate.delete(url);
    log.info("Deleted artifact with digest {} from repository {}", digest, repositoryName);
  }

  /**
   * Retags an image from one repository to another. For example, from
   * wexledu-nonprod/texmath:1.0.61386 to wexledu-prod/texmath:1.0.61386
   *
   * @param image The name of the image (e.g., texmath)
   * @param version The version of the image (e.g., 1.0.61386)
   * @throws IllegalArgumentException if image or version is null or empty
   */
  public void retagImage(String image, String version) {
    if (image == null || image.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "Image name cannot be null or empty");
    }
    if (version == null || version.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Version cannot be null or empty");
    }

    log.info("Retagging image: {} with version: {}", image, version);

    try {
      // Source project is always wexledu-nonprod
      String sourceProject = "wexledu-nonprod";
      // Target project is always wexledu-prod
      String targetProject = "wexledu-prod";

      // Construct the from parameter: sourceProject/image@digest or sourceProject/image:tag
      String fromParam = String.format("%s/%s:%s", sourceProject, image, version);

      // Build the URL for the API call
      String url =
          UriComponentsBuilder.fromHttpUrl(harborApiUrl)
              .path("/projects/{project_name}/repositories/{repository_name}/artifacts")
              .queryParam("from", fromParam)
              .buildAndExpand(targetProject, image)
              .toUriString();

      log.info("Making API call to: {}", url);

      // Make the POST request to copy the artifact
      ResponseEntity<Void> response = restTemplate.postForEntity(url, null, Void.class);

      log.info("Retagging completed with status: {}", response.getStatusCode());
    } catch (Exception e) {
      log.error("Error retagging image: {}", image, e);
      throw new RuntimeException("Failed to retag image: " + image, e);
    }
  }

  @Override
  public void afterPropertiesSet() {
    restTemplate
        .getInterceptors()
        .add(new BasicAuthenticationInterceptor(harborUsername, harborPassword));
  }
}
