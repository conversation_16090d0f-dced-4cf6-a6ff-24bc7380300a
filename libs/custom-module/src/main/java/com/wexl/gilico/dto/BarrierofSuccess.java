package com.wexl.gilico.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BarrierofSuccess {
  @JsonProperty("lack_of_attention")
  private Boolean lackOfAttention;

  @JsonProperty("lack_of_motivation")
  private Boolean lackOfMotivation;

  @JsonProperty("lack_of_preparation")
  private Boolean lackOfPreparation;

  @JsonProperty("peer_pressure")
  private Boolean peerPressure;

  @JsonProperty("undefined_goals")
  private Boolean undefinedGoals;

  @JsonProperty("domestic_issues")
  private Boolean domesticIssues;

  @JsonProperty("inappropriate_behaviour_in_class_room")
  private Boolean inappropriateBehaviourInClassRoom;

  @JsonProperty("severe_illness_of_injury")
  private Boolean severeIllnessOfInjury;

  @JsonProperty("none")
  private Boolean none;

  @JsonProperty("any_other")
  private Boolean anyOther;
}
