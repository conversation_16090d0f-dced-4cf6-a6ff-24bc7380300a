package com.wexl.gilico.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChildSupport {
  @JsonProperty("english_reading")
  private Boolean englishReading;

  @JsonProperty("hindi_reading")
  private Boolean hindiReading;

  @JsonProperty("punjabi_reading")
  private Boolean punjabiReading;

  @JsonProperty("reading_support")
  private Boolean readingSupport;

  @JsonProperty("self_confidence")
  private Boolean selfConfidence;

  @JsonProperty("working_independently_at_home")
  private Boolean workingIndependentlyAtHome;

  @JsonProperty("english_oral_communication")
  private Boolean englishOralCommunication;

  @JsonProperty("hindi_oral_communication")
  private Boolean hindiOralCommunication;

  @JsonProperty("punjabi_oral_communication")
  private Boolean punjabiOralCommunication;

  @JsonProperty("numbers_and_math")
  private Boolean numbersAndMath;

  @JsonProperty("working_with_other_children")
  private Boolean workingWithOtherChildren;

  @JsonProperty("other_subject_areas")
  private String otherSubjectAreas;
}
