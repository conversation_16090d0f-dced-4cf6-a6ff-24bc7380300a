package com.wexl.gilico.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class parentFacilitator {
  @JsonProperty("books")
  private Boolean books;

  @JsonProperty("magazine")
  private Boolean magazine;

  @JsonProperty("toy_and_games")
  private Boolean toyAndGames;

  @JsonProperty("tablet_or_mobile")
  private Boolean tabletOrMobile;

  @JsonProperty("laptop")
  private Boolean laptop;

  @JsonProperty("internet")
  private Boolean internet;
}
