package com.wexl.gilico.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AreaofStrength {
  @JsonProperty("take_feedback_positively")
  private boolean takeFeedbackPositively;

  @JsonProperty("work_independently")
  private boolean workIndependently;

  @JsonProperty("effective_communication")
  private boolean effectiveCommunication;

  @JsonProperty("solutions_focused_thinking")
  private boolean solutionsFocusedThinking;

  @JsonProperty("empathetic")
  private boolean empathetic;

  @JsonProperty("organization_and_prioritization")
  private boolean organizationAndPrioritization;

  @JsonProperty("collaborative_skills")
  private boolean collaborativeSkills;

  @JsonProperty("responsible")
  private boolean responsible;

  @JsonProperty("creative")
  private boolean creative;

  @JsonProperty("concentration")
  private boolean concentration;

  @JsonProperty("any_other")
  private boolean anyOther;
}
