package com.wexl.holisticreportcards.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "pallavi_pre_primary_competencies")
public class Competencies extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "grade_slug")
  private String gradeSlug;

  private String name;

  private String skill;

  @Column(name = "org_slug")
  private String orgSlug;

  @Column(name = "subject_slug")
  private String subjectSlug;

  @Column(name = "subject_name")
  private String subjectName;

  @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  @JoinColumn(name = "pallavi_pre_primary_competencies_id")
  private List<CompetenciesStudents> students;
}
