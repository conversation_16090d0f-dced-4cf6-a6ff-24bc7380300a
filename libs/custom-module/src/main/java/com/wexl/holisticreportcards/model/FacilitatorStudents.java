package com.wexl.holisticreportcards.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "gillco_pre_primary_facilitator_students")
public class FacilitatorStudents extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "student_id")
  private Long studentId;

  private FacilitatorTypes term1;
  private FacilitatorTypes term2;

  @ManyToOne
  @JoinColumn(name = "gillco_pre_primary_facilitator_id")
  private Facilitator facilitator;
}
