package com.wexl.holisticreportcards.repository;

import com.wexl.holisticreportcards.model.Facilitator;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface FacilitatorRepository extends JpaRepository<Facilitator, Long> {

  List<Facilitator> findByOrgSlugAndGradeSlug(String orgSlug, String gradeSlug);
}
