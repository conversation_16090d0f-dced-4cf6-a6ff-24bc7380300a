package com.wexl.holisticreportcards.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.wexl.holisticreportcards.dto.ProgressCardDto;
import com.wexl.retail.model.Model;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "gillco_pre_primary_facilitator")
public class Facilitator extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "grade_slug")
  private String gradeSlug;

  private String name;

  private String skill;

  @Column(name = "org_slug")
  private String orgSlug;

  @Column(name = "student_progress")
  private String studentProgress;

  @Column(name = "future_steps")
  private String futureSteps;

  @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  @JoinColumn(name = "gillco_pre_primary_facilitator_id")
  private List<FacilitatorStudents> students;

  @Type(JsonType.class)
  @JsonIgnore
  @Column(name = "area_of_strength", columnDefinition = "jsonb")
  private ProgressCardDto.AreaofStrength areaOfStrength;

  @Type(JsonType.class)
  @JsonIgnore
  @Column(name = "barrier_of_success", columnDefinition = "jsonb")
  private ProgressCardDto.BarrierofSuccess barrierOfSuccess;

  @Column(name = "participation")
  private String participation;

  @Column(name = "achievements")
  private String achievements;

  @Column(name = "summary_domain_name")
  private String summaryDomainName;

  @Column(name = "class_facilitator_remarks")
  private String classFacilitatorRemarks;

  @Column(name = "principal_remarks")
  private String principalRemarks;
}
