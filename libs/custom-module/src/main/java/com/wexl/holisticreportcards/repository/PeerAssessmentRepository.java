package com.wexl.holisticreportcards.repository;

import com.wexl.holisticreportcards.model.PeerAssessments;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface PeerAssessmentRepository extends JpaRepository<PeerAssessments, Long> {

  List<PeerAssessments> findByOrgSlugAndGradeSlug(String orgSlug, String gradeSlug);
}
