package com.wexl.holisticreportcards.repository;

import com.wexl.holisticreportcards.model.ParentsFeedBack;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ParentsFeedBackRepository extends JpaRepository<ParentsFeedBack, Long> {

  List<ParentsFeedBack> findByOrgSlugAndGradeSlug(String orgSlug, String gradeSlug);
}
