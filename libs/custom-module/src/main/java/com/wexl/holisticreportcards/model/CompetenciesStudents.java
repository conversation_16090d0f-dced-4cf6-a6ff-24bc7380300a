package com.wexl.holisticreportcards.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "pallavi_pre_primary_competency_students")
public class CompetenciesStudents extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @ManyToOne
  @JoinColumn(name = "pallavi_pre_primary_competencies_id")
  private Competencies pallaviPrePrimaryCompetencies;

  @Column(name = "student_id")
  private Long studentId;

  private CompetencyTypes term1;
  private CompetencyTypes term2;
}
