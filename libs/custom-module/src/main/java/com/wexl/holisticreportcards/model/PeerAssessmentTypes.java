package com.wexl.holisticreportcards.model;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;

public enum PeerAssessmentTypes {
  DISAPPOINTED,
  UNSATISFACTORY,
  SATISFIED;

  public static PeerAssessmentTypes fromString(String value) {
    return switch (value.toUpperCase()) {
      case "1", "DISAPPOINTED" -> DISAPPOINTED;
      case "2", "UNSATISFACTORY" -> UNSATISFACTORY;
      case "3", "SATISFIED" -> SATISFIED;
      default ->
          throw new ApiException(
              InternalErrorCodes.INVALID_REQUEST, "Invalid PeerAssessmentType: " + value);
    };
  }
}
