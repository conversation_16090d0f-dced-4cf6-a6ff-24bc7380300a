package com.wexl.dps.reportcard;

import com.wexl.dps.dto.HallticketReportDto;
import com.wexl.dps.managereportcard.repository.ReportCardConfigRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.OfflineTestDefinition;
import com.wexl.retail.offlinetest.model.OfflineTestSchedule;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.service.OfflineTestScheduleService;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsMetaData;
import com.wexl.retail.subjects.model.SubjectsMetadataStudents;
import com.wexl.retail.subjects.repository.SubjectsMetadataStudentsRepository;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class HallticketReportCard extends BaseReportCardDefinition {

  private final ReportCardService reportCardService;
  private final ReportCardConfigRepository reportCardConfigRepository;
  private final OfflineTestScheduleService offlineTestScheduleService;
  private final SubjectsMetadataStudentsRepository subjectsMetadataStudentsRepository;
  private final DateTimeUtil dateTimeUtil;

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, key);
  }

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = buildHeader(user.getStudentInfo(), org);
    var body = buildBody(user, request.offlineTestDefinitionId(), org);
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("admit-card.xml");
  }

  public HallticketReportDto.Body buildBody(
      User user, Long offlineTestDefinitionId, Organization organization) {
    var student = user.getStudentInfo();
    var studentName =
        student.getUserInfo().getFirstName() + " " + student.getUserInfo().getLastName();
    var testDefinition =
        offlineTestScheduleService.validateOfflineTestDefinition(offlineTestDefinitionId);
    Optional<StudentAttributeValueModel> mother =
        reportCardService.getStudentAttributeValue(student, "mother_name");
    Optional<StudentAttributeValueModel> father =
        reportCardService.getStudentAttributeValue(student, "father_name");
    var tableMarks = buildTableMarks(testDefinition, student, organization);
    if (tableMarks.marks().isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.Invalid.HallTicket",
          new String[] {studentName});
    }
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    String abbreviation = organization.getAbbreviation();
    abbreviation = (abbreviation != null) ? abbreviation : "";
    LocalDateTime now = LocalDateTime.now();
    LocalDateTime adjustedTime = now.plusHours(5).plusMinutes(30);
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMddHHmmss");
    String formattedDate = adjustedTime.format(formatter);
    String eCardData = abbreviation + "25" + formattedDate;
    String sectionName = student.getSection().getGradeSlug();
    sectionName =
        student.getSection().getGradeSlug().contains("xi")
            ? student
                .getSection()
                .getGradeSlug()
                .substring(0, sectionName.length() - 1)
                .toUpperCase()
            : sectionName.toUpperCase();

    return HallticketReportDto.Body.builder()
        .name((user.getFirstName() + " " + user.getLastName()).toUpperCase())
        .dateOfBirth(dateOfBirth.map(StudentAttributeValueModel::getValue).orElse(null))
        .admissionNumber(student.getRollNumber())
        .className(student.getSection().getName())
        .sectionName(sectionName)
        .rollNumber(student.getClassRollNumber())
        .mothersName(mother.map(StudentAttributeValueModel::getValue).orElse(null))
        .fathersName(father.map(StudentAttributeValueModel::getValue).orElse(null))
        .examinationName(testDefinition.getTitle())
        .eCard(eCardData)
        .orgSlug(organization.getSlug())
        .firstTable(buildFirstTable(testDefinition, tableMarks.marks(), student))
        .build();
  }

  private HallticketReportDto.FirstTable buildFirstTable(
      OfflineTestDefinition testDefinition,
      List<HallticketReportDto.Marks> marks,
      Student student) {
    return HallticketReportDto.FirstTable.builder().marks(marks).build();
  }

  private HallticketReportDto.TableMarks buildTableMarks(
      OfflineTestDefinition testDefinition, Student student, Organization org) {

    var testSchedules =
        testDefinition.getOfflineTestScheduleSchedule().stream()
            .filter(offlineTestSchedule -> offlineTestSchedule.getDeletedAt() == null)
            .toList()
            .stream()
            .sorted(Comparator.comparing(OfflineTestSchedule::getScheduledAt))
            .toList();

    List<HallticketReportDto.Marks> firstTableMarks = new ArrayList<>();
    for (var testSchedule : testSchedules) {

      var studentDataOptional =
          testSchedule.getOfflineTestScheduleStudents().stream()
              .filter(x -> x.getStudentId().equals(student.getId()))
              .findFirst();

      var studentsSubjectMetaData =
          subjectsMetadataStudentsRepository.findByStudentId(student.getId());

      if (studentDataOptional.isPresent()) {

        var subjectsMetaDataOptional =
            getSubjectMetaData(
                studentsSubjectMetaData, testSchedule.getSubjectsMetaData().getName());

        LocalDateTime dateTime = testSchedule.getScheduledAt();
        String formattedDate =
            Objects.nonNull(dateTime)
                ? dateTime.format(DateTimeFormatter.ofPattern("dd-MM-yyyy"))
                : "";
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("h:mm a");
        var startTime =
            testSchedule.getExamStartTime() != null
                ? testSchedule
                    .getExamStartTime()
                    .atOffset(ZoneOffset.UTC)
                    .plusHours(5)
                    .plusMinutes(30)
                    .format(timeFormatter)
                    .toUpperCase()
                : null;
        var endTime =
            testSchedule.getExamEndTime() != null
                ? testSchedule
                    .getExamEndTime()
                    .atOffset(ZoneOffset.UTC)
                    .plusHours(5)
                    .plusMinutes(30)
                    .format(timeFormatter)
                    .toUpperCase()
                : null;

        if (subjectsMetaDataOptional.isPresent()) {
          var subjectsMetaData = subjectsMetaDataOptional.get();
          if (subjectsMetaData.getCategoryEnum().equals(SubjectsCategoryEnum.SCHOLASTIC)) {
            var marksBuilder =
                HallticketReportDto.Marks.builder()
                    .date(formattedDate)
                    .subjectName(subjectsMetaData.getName())
                    .startTime(startTime)
                    .endTime(endTime)
                    .invigilator("")
                    .build();

            firstTableMarks.add(marksBuilder);
          }
        }
      }
    }

    return HallticketReportDto.TableMarks.builder().marks(firstTableMarks).build();
  }

  private Optional<SubjectsMetaData> getSubjectMetaData(
      List<SubjectsMetadataStudents> studentsSubjectMetaData, String subjectName) {
    return studentsSubjectMetaData.stream()
        .map(SubjectsMetadataStudents::getSubjectsMetaData)
        .filter(subjectMetaData -> subjectMetaData.getName().equals(subjectName))
        .findFirst();
  }
}
