package com.wexl.dps.assesmentobjectives.model;

import com.wexl.retail.model.Model;
import com.wexl.retail.term.model.Term;
import jakarta.persistence.*;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@RequiredArgsConstructor
@Builder
@Entity
@AllArgsConstructor
@Table(name = "assessment_objectives")
public class AssessmentObjective extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private long id;

  private String board;

  private String boardName;

  private String grade;

  private String gradeName;

  private String subject;

  private String subjectName;

  @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
  private Term term;

  private String orgSlug;

  private Long seqNo;

  @OneToMany(mappedBy = "assessmentObjective", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  private List<AssessmentObjectiveDetail> assessmentObjectiveDetails;
}
