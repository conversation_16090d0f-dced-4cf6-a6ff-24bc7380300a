package com.wexl.dps.managereportcard.service;

import com.wexl.dps.managereportcard.repository.ReportCardJobRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.reportcards.dto.ReportCardConfigDto;
import com.wexl.retail.reportcards.dto.ReportCardConfigDto.ReportCardJobRequest;
import com.wexl.retail.reportcards.dto.ReportCardConfigDto.ReportCardJobResponse;
import com.wexl.retail.reportcards.dto.ReportCardConfigDto.ReportCardJobStatus;
import com.wexl.retail.reportcards.model.ReportCardConfig;
import com.wexl.retail.reportcards.model.ReportCardJob;
import com.wexl.retail.reportcards.repository.StudentReportCardRepository;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class ReportCardJobService {

  private final ReportCardJobRepository reportCardJobRepository;
  private final ManageReportCardService manageReportCardService;
  private final ReportCardJobWorkerService reportCardJobWorkerService;
  private final StudentReportCardRepository studentReportCardRepository;

  public ReportCardJobResponse generateReportCardData(
      String orgSlug, ReportCardJobRequest reportCardJobRequest) {
    final ReportCardConfig reportCardConfig =
        manageReportCardService.validateReportCardConfig(reportCardJobRequest.reportCardConfigId());

    final List<ReportCardJob> reportCardJobs =
        reportCardJobRepository.findAllByOrgSlugAndReportCardConfigAndStatus(
            orgSlug, reportCardConfig, ReportCardJobStatus.IN_PROGRESS);

    if (!reportCardJobs.isEmpty()) {
      updateReportCardJobs(reportCardJobs);
    }
    var currentJob =
        ReportCardJob.builder()
            .reportCardConfig(reportCardConfig)
            .status(ReportCardJobStatus.IN_PROGRESS)
            .orgSlug(orgSlug)
            .build();
    var reportCardJobUpdated = reportCardJobRepository.save(currentJob);
    try {
      reportCardJobWorkerService.triggerReportCardJob(reportCardJobUpdated.getId());
    } catch (Exception e) {
      log.error(e.getMessage(), e);
      updateReportCard(reportCardJobUpdated.getId(), e);
    }
    return ReportCardJobResponse.builder().refId(reportCardJobUpdated.getId()).build();
  }

  private void updateReportCard(Long reportCardId, Exception e) {
    var reportCard = reportCardJobRepository.findById(reportCardId);
    if (reportCard.isEmpty()) {
      return;
    }
    final ReportCardJob reportCardJob = reportCard.get();
    reportCardJob.setStatus(ReportCardJobStatus.FAILED);
    reportCardJob.setErrorMessage(reportCardJobWorkerService.getStackTrace(e));
    reportCardJobRepository.save(reportCardJob);
  }

  private void updateReportCardJobs(List<ReportCardJob> reportCardJobs) {
    reportCardJobs.forEach(
        reportCardJob -> {
          reportCardJob.setStatus(ReportCardJobStatus.FAILED);
          reportCardJob.setFailureReason("Terminated due to new job request");
          reportCardJob.setErrorMessage("Terminated due to new job request");
        });
    reportCardJobRepository.saveAll(reportCardJobs);
  }

  public void generateReportByJob(long jobId) {
    var reportCardJob = reportCardJobWorkerService.validateReportCardJob(jobId);
    if (!ReportCardConfigDto.ReportCardJobStatus.COMPLETED.equals(reportCardJob.getStatus())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Could not store report cards");
    }
    manageReportCardService.updateReportCardJob(
        reportCardJob.getId(), ReportCardConfigDto.ReportCardJobStatus.UPLOADING, null);
    reportCardJobWorkerService.generateStudentReportCard(reportCardJob.getId());
  }
}
