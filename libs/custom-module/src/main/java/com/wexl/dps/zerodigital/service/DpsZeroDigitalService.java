package com.wexl.dps.zerodigital.service;

import com.wexl.dps.mlp.dto.MlpDto;
import com.wexl.dps.zerodigital.dto.DpsZeroDigitalData;
import com.wexl.dps.zerodigital.repository.DpsZeroDigitalRepository;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.UserRoleHelper;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.curriculum.service.CurriculumService;
import com.wexl.retail.model.EduBoard;
import com.wexl.retail.model.Grade;
import com.wexl.retail.model.User;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.section.dto.response.SectionEntityDto;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.teacher.orgs.TeacherOrgsService;
import com.wexl.retail.util.ValidationUtils;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class DpsZeroDigitalService {

  private final String[] subjectSlugs = {"mathematics", "science", "social", "english"};

  private static final List<String> GRADES_ORDER =
      Arrays.asList("i", "ii", "iii", "iv", "v", "vi", "vii", "viii", "ix", "x");

  private final DpsZeroDigitalRepository dpsZeroDigitalRepository;
  private final CurriculumService curriculumService;
  private final SectionService sectionService;
  private final AuthService authService;
  private final TeacherOrgsService teacherOrgsService;
  private final ValidationUtils validationUtils;
  private final UserRoleHelper userRoleHelper;

  public List<MlpDto.Subject> getZeroDigital(
      String orgSlug, String boardSlug, String gradeSlug, String childOrg) {
    User user = authService.getUserDetails();
    if (Boolean.TRUE.equals(userRoleHelper.isManager(user))) {
      if (childOrg != null) {
        return getAdminZeroDigitalData(childOrg, boardSlug, gradeSlug);
      }
      return getManagerZeroDigitalData(boardSlug, gradeSlug);
    }
    return getAdminZeroDigitalData(orgSlug, boardSlug, gradeSlug);
  }

  public List<MlpDto.Subject> getAdminZeroDigitalData(
      String orgSlug, String boardSlug, String gradeSlug) {
    if (gradeSlug == null) {
      return buildZeroDigitalResponse(boardSlug, orgSlug);
    }
    return buildZeroDigitalByGradeResponse(orgSlug, gradeSlug, boardSlug);
  }

  private List<MlpDto.Subject> buildZeroDigitalResponse(String boardSlug, String orgSlug) {
    List<MlpDto.Subject> response = new ArrayList<>();
    List<DpsZeroDigitalData> zeroDigitalData =
        dpsZeroDigitalRepository.getDpsZeroDigitalData(orgSlug, boardSlug);
    if (zeroDigitalData.isEmpty()) {
      return response;
    }

    var grades = getGradesByBoard(orgSlug, boardSlug);
    Map<String, List<DpsZeroDigitalData>> groupedBySubject =
        zeroDigitalData.stream().collect(Collectors.groupingBy(DpsZeroDigitalData::getSubjectSlug));

    Map<String, String> subjectMap =
        Arrays.stream(subjectSlugs).collect(Collectors.toMap(slug -> slug, this::getSubjectName));

    Map<String, String> gradeMap =
        grades.stream().collect(Collectors.toMap(Grade::getSlug, Grade::getName));

    for (String subjectSlug : subjectSlugs) {
      String subjectName = subjectMap.get(subjectSlug);

      List<MlpDto.Grades> gradeList =
          grades.stream()
              .map(
                  grade -> {
                    double knowledgePercentage =
                        groupedBySubject.getOrDefault(subjectSlug, Collections.emptyList()).stream()
                            .filter(data -> data.getGradeSlug().equals(grade.getSlug()))
                            .mapToDouble(DpsZeroDigitalData::getKnowledgePercentage)
                            .findFirst()
                            .orElse(0.0);

                    return MlpDto.Grades.builder()
                        .gradeName(gradeMap.getOrDefault(grade.getSlug(), "Unknown"))
                        .gradeSlug(grade.getSlug())
                        .knowledgePercentage(
                            knowledgePercentage == 0.0
                                ? 0.0
                                : Math.round(knowledgePercentage * 100.0) / 100.0)
                        .build();
                  })
              .toList();

      response.add(
          MlpDto.Subject.builder().name(subjectName).slug(subjectSlug).grades(gradeList).build());
    }

    return response;
  }

  private List<Grade> getGradesByBoard(String orgSlug, String boardSlug) {
    List<EduBoard> boardsHierarchy = curriculumService.getBoardsHierarchy(orgSlug);
    Optional<EduBoard> boardData =
        boardsHierarchy.stream().filter(x -> x.getSlug().equals(boardSlug)).findFirst();
    if (boardData.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.EduboardFind.Organization",
          new String[] {boardSlug});
    }

    List<Grade> grades = boardData.get().getGrades();
    return sortGrades(grades);
  }

  private String getSubjectName(String slug) {
    return switch (slug) {
      case "science" -> "Science";
      case "mathematics" -> "Mathematics";
      case "english" -> "English";
      case "social" -> "Social";
      default -> slug;
    };
  }

  public static List<Grade> sortGrades(List<Grade> grades) {
    grades.sort(
        Comparator.comparingInt(grade -> GRADES_ORDER.indexOf(grade.getName().toLowerCase())));
    return grades;
  }

  public List<MlpDto.Subject> buildZeroDigitalByGradeResponse(
      String orgSlug, String gradeSlug, String boardSlug) {
    return buildZeroDigitalResponseData(
        dpsZeroDigitalRepository.getDpsZeroDigitalDataByGrade(orgSlug, boardSlug, gradeSlug),
        orgSlug,
        gradeSlug,
        boardSlug);
  }

  private List<MlpDto.Subject> buildZeroDigitalResponseData(
      List<DpsZeroDigitalData> mlpData, String orgSlug, String gradeSlug, String boardSlug) {
    List<MlpDto.Subject> response = new ArrayList<>();

    List<SectionEntityDto.Response> sections =
        sectionService.getSectionsByGrade(orgSlug, gradeSlug);
    Map<String, List<DpsZeroDigitalData>> groupedBySubject =
        mlpData.stream().collect(Collectors.groupingBy(DpsZeroDigitalData::getSubjectSlug));

    Map<String, String> subjectMap =
        Arrays.stream(subjectSlugs).collect(Collectors.toMap(slug -> slug, this::getSubjectName));

    Map<String, String> sectionMap =
        sections.stream()
            .collect(
                Collectors.toMap(SectionEntityDto.Response::name, SectionEntityDto.Response::name));

    for (String subjectSlug : subjectSlugs) {
      String subjectName = subjectMap.get(subjectSlug);

      List<MlpDto.Sections> sectionsList =
          sections.stream()
              .filter(x -> x.boardSlug().equals(boardSlug))
              .map(
                  section -> {
                    double knowledgePercentage =
                        groupedBySubject.getOrDefault(subjectSlug, Collections.emptyList()).stream()
                            .filter(data -> data.getSectionSlug().equals(section.name()))
                            .mapToDouble(DpsZeroDigitalData::getKnowledgePercentage)
                            .findFirst()
                            .orElse(0.0);

                    return MlpDto.Sections.builder()
                        .sectionName(sectionMap.getOrDefault(section.name(), "Unknown"))
                        .sectionUuid(section.uuid().toString())
                        .knowledgePercentage(
                            knowledgePercentage == 0.0
                                ? 0.0
                                : Math.round(knowledgePercentage * 100.0) / 100.0)
                        .build();
                  })
              .toList();

      response.add(
          MlpDto.Subject.builder()
              .name(subjectName)
              .slug(subjectSlug)
              .sections(sectionsList)
              .build());
    }
    return response;
  }

  public List<MlpDto.Subject> getManagerZeroDigitalData(String boardSlug, String gradeSlug) {
    return (gradeSlug == null)
        ? buildManagerMlpResponse(boardSlug)
        : buildManagerMlpByGradeResponse(gradeSlug, boardSlug);
  }

  private List<MlpDto.Subject> buildManagerMlpByGradeResponse(String gradeSlug, String boardSlug) {
    List<MlpDto.Subject> response = new ArrayList<>();
    List<String> teacherOrgSlugs = getManagerOrgs(boardSlug);
    List<DpsZeroDigitalData> mlpData =
        dpsZeroDigitalRepository.getDpsManagerSectionMlpData(teacherOrgSlugs, gradeSlug, boardSlug);
    response.add(
        MlpDto.Subject.builder()
            .orgResponses(buildOrgResponse(mlpData, teacherOrgSlugs, null))
            .build());
    return (response);
  }

  private List<MlpDto.Subject> buildManagerMlpResponse(String boardSlug) {
    List<MlpDto.Subject> responeList = new ArrayList<>();
    List<String> teacherOrgSlugs = getManagerOrgs(boardSlug);

    List<DpsZeroDigitalData> mlpData =
        dpsZeroDigitalRepository.getDpsManagerMlpData(teacherOrgSlugs, boardSlug);

    for (String subjectSlug : subjectSlugs) {
      responeList.add(
          MlpDto.Subject.builder()
              .name(getSubjectName(subjectSlug))
              .slug(subjectSlug)
              .orgResponses(buildOrgResponse(mlpData, teacherOrgSlugs, subjectSlug))
              .build());
    }
    return responeList;
  }

  private List<MlpDto.OrgResponse> buildOrgResponse(
      List<DpsZeroDigitalData> mlpData, List<String> teacherOrgSlugs, String subjectSlug) {
    List<MlpDto.OrgResponse> orgResponseList = new ArrayList<>();

    teacherOrgSlugs.forEach(
        orgSlug -> {
          var org = validationUtils.isOrgValid(orgSlug);

          Optional<DpsZeroDigitalData> orgMlp =
              subjectSlug != null
                  ? mlpData.stream()
                      .filter(
                          x ->
                              x.getSubjectSlug().equals(subjectSlug)
                                  && x.getOrgSlug().equals(orgSlug))
                      .findAny()
                  : mlpData.stream().filter(x -> x.getOrgSlug().equals(orgSlug)).findAny();

          orgResponseList.add(
              MlpDto.OrgResponse.builder()
                  .orgSlug(org.getSlug())
                  .orgName(org.getName())
                  .knowledgePercentage(
                      orgMlp
                          .map(
                              dpsMlpData ->
                                  roundToTwoDecimalPlaces(dpsMlpData.getKnowledgePercentage()))
                          .orElse(0.0))
                  .build());
        });

    return orgResponseList;
  }

  private double roundToTwoDecimalPlaces(double value) {
    return Math.round(value * 100.0) / 100.0;
  }

  private List<String> getManagerOrgs(String boardSlug) {
    User user = authService.getUserDetails();
    return teacherOrgsService.getChildOrgs(user.getAuthUserId()).stream()
        .filter(
            org ->
                org.getCurriculum().getBoards().stream()
                    .anyMatch(board -> board.getSlug().equals(boardSlug)))
        .map(Organization::getSlug)
        .distinct()
        .toList();
  }
}
