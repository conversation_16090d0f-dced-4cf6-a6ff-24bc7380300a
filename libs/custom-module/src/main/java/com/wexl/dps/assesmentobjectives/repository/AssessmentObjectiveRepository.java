package com.wexl.dps.assesmentobjectives.repository;

import com.wexl.dps.assesmentobjectives.model.AssessmentObjective;
import com.wexl.retail.term.model.Term;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface AssessmentObjectiveRepository extends JpaRepository<AssessmentObjective, Long> {

  Optional<AssessmentObjective> findByOrgSlugAndBoardAndGradeAndSubjectAndTermAndDeletedAtIsNull(
      String orgSlug, String boardSlug, String gradeSlug, String subject, Term term);

  List<AssessmentObjective> findByOrgSlugAndDeletedAtIsNullOrderByCreatedAtDesc(String orgSlug);

  Optional<AssessmentObjective> findByIdAndOrgSlugAndDeletedAtIsNull(Long id, String orgSlug);

  List<AssessmentObjective> findByOrgSlugAndBoardAndGradeAndTermAndDeletedAtIsNullOrderBySeqNo(
      String orgSlug, String boardSlug, String gradeSlug, Term term);

  List<AssessmentObjective> findAllByIdInAndTermAndDeletedAtIsNull(List<Long> aoIds, Term term);

  List<AssessmentObjective> findAllByIdInAndTermIdAndDeletedAtIsNull(List<Long> aoIds, Long termId);
}
