package com.wexl.dps.dto;

import java.util.List;
import lombok.Builder;

public record NinthTenthGradeReportCardDto() {
  @Builder
  public record Response(Header header, Body body) {}

  @Builder
  public record Header(
      String schoolName,
      String academicYear,
      String admissionNumber,
      String address,
      String isoData,
      Long studentId) {}

  @Builder
  public record Body(
      String name,
      String rollNumber,
      String className,
      String mothersName,
      String fathersName,
      String boardSlug,
      String dateOfBirth,
      String orgSlug,
      String gradeSlug,
      FirstTable firstTable,
      FirstTable graphResponse,
      SecondTable secondTable,
      ThirdTable thirdTable,
      List<FourthTable> fourthTable,
      Attendance attendance,
      GradeTable gradeTable,
      String gradingScale) {}

  @Builder
  public record GradeTable(String title) {}

  @Builder
  public record FirstTable(String title, List<Marks> marks, List<Marks> external, Totals totals) {}

  @Builder
  public record SecondTable(String title, List<SecondTableMarks> marks) {}

  @Builder
  public record SecondTableMarks(<PERSON> sno, String subjectName, String termGrade) {}

  @Builder
  public record ThirdTable(String title, List<ThirdTableMarks> marks) {}

  @Builder
  public record ThirdTableMarks(Long sno, String subjectName, String termGrade) {}

  @Builder
  public record FourthTable(String subjectName, String term1Grade, String term2Grade) {}

  @Builder
  public record Marks(
      Long sno,
      String subject,
      String ptsMarks,
      int pt1,
      int pt2,
      int pt3,
      String ma,
      String se,
      String port,
      Double internalAssessmentMarks,
      Double marksObtained1,
      String annualMarks,
      int annualMarksInPercentage,
      String isAttended,
      String totalMarksScored,
      String total,
      String grade,
      Long seqNo,
      Long otdId) {}

  @Builder
  public record Totals(
      Long annualExam,
      Long internalAssessmentMarks,
      Double marksTotal,
      Double overallPercentage,
      Double percentage,
      String grade) {}

  @Builder
  public record Attendance(
      Long workingDays, Long daysPresent, Double attendancePercentage, String remarks) {}

  @Builder
  public record TableMarks(
      List<Marks> firstTableMarks,
      List<Marks> externalMarks,
      List<SecondTableMarks> secondTableMarks,
      List<ThirdTableMarks> thirdTableMarks,
      List<Marks> graphResponse) {}
}
