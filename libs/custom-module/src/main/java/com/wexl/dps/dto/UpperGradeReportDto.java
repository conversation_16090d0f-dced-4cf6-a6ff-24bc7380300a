package com.wexl.dps.dto;

import java.util.List;
import lombok.Builder;

public record UpperGradeReportDto() {
  @Builder
  public record Response(Header header, Body body) {}

  @Builder
  public record Header(
      String schoolName,
      String academicYear,
      String admissionNumber,
      String address,
      String isoData,
      Long studentId) {}

  @Builder
  public record Body(
      String name,
      String rollNumber,
      String className,
      String mothersName,
      String fathersName,
      String boardSlug,
      String dateOfBirth,
      String orgSlug,
      String gradeSlug,
      FirstTable firstTable,
      SecondTable secondTable,
      FirstTable graphTableMarks,
      Attendance attendance,
      GradeTable gradeTable,
      String gradingScale) {}

  @Builder
  public record GradeTable(String title) {}

  @Builder
  public record FirstTable(
      String title,
      String column1,
      String column2,
      String column3,
      String column4,
      String column5,
      String column6,
      String column7,
      String column8,
      List<UpperGradeReportDto.Marks> marks,
      List<UpperGradeReportDto.Marks> external,
      UpperGradeReportDto.Totals totals) {}

  @Builder
  public record SecondTable(String title, List<SecondTableMarks> marks) {}

  @Builder
  public record SecondTableMarks(String subjectName, String term1Grade, String term2Grade) {}

  @Builder
  public record Marks(
      Long sno,
      String subject,
      String pa1,
      String pt,
      String nb1,
      String se1,
      String hye,
      String hye1,
      Double marksObtained1,
      Double term1TotalMarks,
      Double term2TotalMarks,
      String grade1,
      String hyeGrade,
      String grade2,
      String yeGrade,
      Double hyeGradeAndYeMarks,
      String hyeGradeAndYeGrade,
      String pa2,
      String nb2,
      String se2,
      String ye,
      String ye1,
      Double t1t2Total,
      Double marksObtained2,
      Double overAllMarks,
      String overAllGrade,
      Double overAllScored,
      Long overAllExamMarks,
      Long seqNo,
      Long otdId,
      Boolean considerPercentage) {}

  @Builder
  public record Totals(
      Long annualExam,
      Double total,
      String grade,
      String overallPercentage,
      Double marksTotal,
      String term2OverallPercentage,
      String term2Grade,
      String totalT1AndT2,
      String averageT1AndT2,
      String totalGrade) {}

  @Builder
  public record Attendance(
      Long workingDays, Long daysPresent, Double attendancePercentage, String remarks) {}

  @Builder
  public record TableMarks(
      List<Marks> firstTableMarks,
      List<Marks> externalMarks,
      List<SecondTableMarks> secondTableMarks,
      List<Marks> graphTableMarks) {}
}
