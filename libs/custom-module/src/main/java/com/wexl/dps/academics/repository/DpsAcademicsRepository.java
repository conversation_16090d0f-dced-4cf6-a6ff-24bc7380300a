package com.wexl.dps.academics.repository;

import com.wexl.dps.academics.dto.DpsAcademicsData;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleRepository;
import java.util.List;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface DpsAcademicsRepository extends OfflineTestScheduleRepository {

  @Query(
      value =
          """
                  SELECT sm.name as subjectName,sm.wexl_subject_slug as subjectSlug,SUM(otss.marks) AS marks_scored,SUM(ots.marks) AS totalMarks,
                  ((SUM(otss.marks) * 1.0 / SUM(ots.marks)) * 100) AS knowledgePercentage,otd.grade_name,otd.grade_slug AS gradeSlug
                  FROM offline_test_definition otd JOIN offline_test_schedule ots ON otd.id = ots.offline_test_definition_id
                  JOIN subject_metadata sm ON ots.subject_metadata_id = sm.id
                  JOIN offline_test_schedule_student otss ON otss.offline_test_schedule_id = ots.id
                  WHERE otd.org_slug =:orgSlug AND sm."type" = 'MANDATORY' AND sm.category = 'SCHOLASTIC'
                  AND published_at IS NOT NULL AND is_attended = 'true' AND otd.board_slug = :boardSlug
                  GROUP BY otd.grade_name,otd.grade_slug,sm.name,sm.wexl_subject_slug;
                            """,
      nativeQuery = true)
  List<DpsAcademicsData> getDpsAcademicsData(String orgSlug, String boardSlug);

  @Query(
      value =
          """
                  select otd.section_uuid as sectionUuid,s.name,sm.name as subjectName,sm.wexl_subject_slug as subjectSlug,
                   sum(otss.marks) as marks_scored, sum(ots.marks) as totalMarks,
                   (sum(otss.marks)* 1.0 / sum(ots.marks)) * 100 AS knowledgePercentage
                   from offline_test_definition otd join offline_test_schedule ots on otd.id = ots.offline_test_definition_id
                   join subject_metadata sm on ots.subject_metadata_id  = sm.id
                   join offline_test_schedule_student otss on otss.offline_test_schedule_id  = ots.id
                   join sections s on  CAST(s."uuid" AS VARCHAR) = otd.section_uuid
                   where otd.grade_slug = :gradeSlug and otd.org_slug  = :orgSlug and sm."type"  = 'MANDATORY' and sm.category  = 'SCHOLASTIC'
                   and published_at is not null and is_attended = 'true' AND otd.board_slug = :boardSlug
                   group by otd.section_uuid,sm.name,s.name,sm.wexl_subject_slug
                            """,
      nativeQuery = true)
  List<DpsAcademicsData> getDpsAcademicsDataByGrade(
      String orgSlug, String boardSlug, String gradeSlug);

  @Query(
      value =
          """
                      SELECT  otd.org_slug as orgSlug,sm.name as subjectName,sm.wexl_subject_slug as subjectSlug,SUM(otss.marks) AS marks_scored,SUM(ots.marks) AS totalMarks,
                       ((SUM(otss.marks) * 1.0 / SUM(ots.marks)) * 100) AS knowledgePercentage
                       FROM offline_test_definition otd JOIN offline_test_schedule ots ON otd.id = ots.offline_test_definition_id
                       JOIN subject_metadata sm ON ots.subject_metadata_id = sm.id
                       JOIN offline_test_schedule_student otss ON otss.offline_test_schedule_id = ots.id
                       WHERE otd.org_slug in (:orgSlugs) AND sm."type" = 'MANDATORY' AND sm.category = 'SCHOLASTIC'
                       AND published_at IS NOT NULL AND is_attended = 'true' AND otd.board_slug = :boardSlug
                       GROUP BY  otd.org_slug,sm.name,sm.wexl_subject_slug;
                                    """,
      nativeQuery = true)
  List<DpsAcademicsData> getDpsManagerAcademics(List<String> orgSlugs, String boardSlug);

  @Query(
      value =
          """
                     SELECT otd.org_slug as orgSlug,otd.grade_slug as gradeSlug,SUM(otss.marks) AS marks_scored,SUM(ots.marks) AS totalMarks,
                      ((SUM(otss.marks) * 1.0 / SUM(ots.marks)) * 100) AS knowledgePercentage
                      FROM offline_test_definition otd JOIN offline_test_schedule ots ON otd.id = ots.offline_test_definition_id
                      JOIN subject_metadata sm ON ots.subject_metadata_id = sm.id
                      JOIN offline_test_schedule_student otss ON otss.offline_test_schedule_id = ots.id
                      WHERE otd.org_slug in (:orgSlugs) AND sm."type" = 'MANDATORY' AND sm.category = 'SCHOLASTIC'
                      AND published_at IS NOT NULL AND is_attended = 'true' AND otd.board_slug = :boardSlug
                      and otd.grade_slug = :gradeSlug
                      GROUP BY otd.grade_slug,otd.org_slug;
                                            """,
      nativeQuery = true)
  List<DpsAcademicsData> getDpsManagerAcademicsByGrade(
      List<String> orgSlugs, String boardSlug, String gradeSlug);
}
