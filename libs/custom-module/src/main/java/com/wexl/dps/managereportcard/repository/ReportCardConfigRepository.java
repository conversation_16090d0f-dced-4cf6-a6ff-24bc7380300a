package com.wexl.dps.managereportcard.repository;

import com.wexl.retail.reportcards.model.ReportCardConfig;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ReportCardConfigRepository extends JpaRepository<ReportCardConfig, Long> {
  List<ReportCardConfig> findAllByOrgSlugOrderByIdDesc(String orgSlug);

  List<ReportCardConfig> findAllByOrgSlugAndBoardSlugAndGradeSlug(
      String orgSlug, String boardSlug, String gradeSlug);

  Optional<ReportCardConfig> findByBoardSlugAndGradeSlugAndOrgSlugAndTitle(
      String boardSlug, String gradeSlug, String orgSlug, String title);

  List<ReportCardConfig> findAllByOrgSlugAndBoardSlugAndGradeSlugAndStudentViewEnabled(
      String orgSlug, String boardSlug, String gradeSlug, Boolean studentViewEnabled);

  Optional<ReportCardConfig> findByOrgSlugAndBoardSlugAndGradeSlugAndTemplateId(
      String orgSlug, String boardSlug, String gradeSlug, Long templateId);

  List<ReportCardConfig> findAllByOrgSlugAndBoardSlugAndGradeSlugAndTemplateId(
      String orgSlug, String boardSlug, String gradeSlug, Long templateId);
}
