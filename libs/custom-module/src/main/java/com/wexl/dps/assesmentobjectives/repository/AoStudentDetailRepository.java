package com.wexl.dps.assesmentobjectives.repository;

import com.wexl.dps.assesmentobjectives.model.AoStudent;
import com.wexl.dps.assesmentobjectives.model.AoStudentDetail;
import com.wexl.dps.assesmentobjectives.model.AssessmentObjectiveDetail;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface AoStudentDetailRepository extends JpaRepository<AoStudentDetail, Long> {
  Optional<AoStudentDetail> findByAoStudentAndAssessmentObjectiveDetail(
      AoStudent student, AssessmentObjectiveDetail aod);
}
