package com.wexl.dps.reportcard;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wexl.dps.dto.Gillco3rd5thHolisticReportDto;
import com.wexl.dps.managereportcard.repository.ReportCardConfigDataRepository;
import com.wexl.gilico.service.GillcoHolisticReportCard;
import com.wexl.holisticreportcards.ProgressCardService;
import com.wexl.holisticreportcards.dto.ProgressCardDto;
import com.wexl.holisticreportcards.model.Facilitator;
import com.wexl.holisticreportcards.model.ProgressCard;
import com.wexl.holisticreportcards.repository.FacilitatorRepository;
import com.wexl.holisticreportcards.repository.FacilitatorStudentsRepository;
import com.wexl.holisticreportcards.repository.ProgressCardRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.commons.util.ResourceUtils;
import com.wexl.retail.erp.attendance.dto.StudentsAttendanceReport;
import com.wexl.retail.erp.attendance.repository.SectionAttendanceRepository;
import com.wexl.retail.erp.attendance.service.ErpAttendanceService;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.student.profile.ProfileService;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import java.time.*;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.antlr.v4.runtime.misc.MultiMap;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class Gillco3rd5thHolisticReportCard extends BaseReportCardDefinition {

  private final ReportCardService reportCardService;
  private final ProfileService profileService;
  private final ProgressCardRepository progressCardRepository;
  private final GillcoHolisticReportCard gillcoHolisticReportCard;
  private final UserService userService;
  private final TermAssessmentRepository termAssessmentRepository;
  private final ReportCardConfigDataRepository reportCardConfigDataRepository;
  private final ProgressCardService progressCardService;
  private final FacilitatorStudentsRepository facilitatorStudentsRepository;
  private final FacilitatorRepository facilitatorRepository;
  private final ErpAttendanceService erpAttendanceService;
  private final SectionAttendanceRepository sectionAttendanceRepository;
  private final DateTimeUtil dateTimeUtil;

  public static final List<Long> boardId = List.of(1L);

  @Value("classpath:gillco-school-subjects.json")
  private Resource gillcoSubjectsResource;

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, key);
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("gillco-holistic-3rd-5th-term1-report.xml");
  }

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = buildHolisticHeader(user);
    var body = buildBody(user, org.getSlug());
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  private Gillco3rd5thHolisticReportDto.Header buildHolisticHeader(User user) {
    return Gillco3rd5thHolisticReportDto.Header.builder()
        .imageUrl(
            Objects.isNull(user.getProfileImage())
                ? null
                : profileService.getProfileImageUrl(user.getProfileImage()))
        .build();
  }

  private Gillco3rd5thHolisticReportDto.Body buildBody(User user, String orgSlug) {
    var student = user.getStudentInfo();
    var guardians = student.getGuardians();
    var section = student.getSection();
    var mother =
        guardians.isEmpty()
            ? null
            : guardians.stream()
                .filter(x -> x.getRelationType().equals(GuardianRole.MOTHER))
                .findFirst()
                .map(reportCardService::getGuardianName)
                .orElse(null);
    var father =
        guardians.isEmpty()
            ? null
            : guardians.stream()
                .filter(x -> x.getRelationType().equals(GuardianRole.FATHER))
                .findFirst()
                .map(reportCardService::getGuardianName)
                .orElse(null);
    Optional<StudentAttributeValueModel> dateOfBirthOpt =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    var dateOfBirth = dateOfBirthOpt.map(StudentAttributeValueModel::getValue).orElse(null);
    Optional<StudentAttributeValueModel> house =
        reportCardService.getStudentAttributeValue(student, "house");
    Optional<StudentAttributeValueModel> address =
        reportCardService.getStudentAttributeValue(student, "residential_address");
    Integer age = gillcoHolisticReportCard.getAge(dateOfBirth);
    var studentData = progressCardRepository.findByStudentId(student.getId());
    List<ProgressCardDto.Competencies> competencies =
        progressCardService.getCompetencyDetails(section.getGradeSlug(), orgSlug, student);

    var data = buildTableMarks(student, orgSlug);
    Map<String, Integer> range = getConvertedMarchToFebRange();
    var convertedFromDate = range.get("convertedFromDate");
    var convertedToDate = range.get("convertedToDate");

    var facilitatorStudentOpt = facilitatorStudentsRepository.findByStudentId(student.getId());
    var facilitator =
        facilitatorStudentOpt.isPresent() ? facilitatorStudentOpt.get().getFacilitator() : null;

    return Gillco3rd5thHolisticReportDto.Body.builder()
        .name(userService.getNameByUserInfo(student.getUserInfo()))
        .className(student.getSection().getGradeName())
        .sectionName(student.getSection().getName())
        .rollNo(student.getClassRollNumber())
        .admissionNumber(student.getRollNumber())
        .house(house.map(StudentAttributeValueModel::getValue).orElse(null))
        .dateOfBirth(dateOfBirth)
        .age(age)
        .address(address.map(StudentAttributeValueModel::getValue).orElse(null))
        .fatherName(father)
        .motherName(mother)
        .allAboutMe(gillcoHolisticReportCard.buildAllAboutMe(studentData))
        .attendance(buildAttendance(student, orgSlug, convertedFromDate, convertedToDate))
        .interests(buildInterests(studentData))
        .medicalProfile(buildMedicalProfile(studentData))
        .selfAndPeerAssessments(buildSelfAndPeerAssessments(student, orgSlug))
        .resources(buildResouces(studentData))
        .childBetter(buildChildBetter(student, orgSlug))
        .support(buildSupport(studentData))
        .scholosticMandatory(data.scholosticMandatory())
        .scholosticOptional(data.scholosticOptional())
        .scholosticSkillDetails(buildScholosticSkillDetails(competencies, student))
        .coScholosticSkillDetails(buildCoScolosticSkillDetails(competencies, student))
        .summarySheetDetails(buildSummarySheetDetails(student, orgSlug))
        .classFacilitatorGroupedSkills(buildClassFacilitatorGroupedSkills(student, orgSlug))
        .participationAchievements(buildParticipationAchievements(student))
        .facilitators(buildFacilitators(student))
        .classRemarks(facilitator != null ? facilitator.getClassFacilitatorRemarks() : null)
        .principleRemarks(facilitator != null ? facilitator.getPrincipalRemarks() : null)
        .build();
  }

  public Map<String, Integer> getConvertedMarchToFebRange() {

    ZoneId zone = ZoneId.of("Asia/Kolkata");

    // Current and next calendar year in IST
    int currentYear = Year.now(zone).getValue();
    int nextYear = currentYear + 1;

    // From: 1 Mar currentYear 00:00:00.000 IST
    ZonedDateTime from =
        ZonedDateTime.of(LocalDate.of(currentYear, Month.MARCH, 1), LocalTime.MIDNIGHT, zone);
    long fromEpoch = from.toInstant().toEpochMilli();

    // To: last day of Feb nextYear 23:59:59.999 IST (handles leap years)
    int lastDayOfFeb = YearMonth.of(nextYear, Month.FEBRUARY).lengthOfMonth();
    ZonedDateTime to =
        ZonedDateTime.of(
            LocalDate.of(nextYear, Month.FEBRUARY, lastDayOfFeb),
            LocalTime.of(23, 59, 59, 999_000_000),
            zone);
    long toEpoch = to.toInstant().toEpochMilli();

    int convertedFromDate = dateTimeUtil.convertEpocToIntegerFormat(fromEpoch);
    int convertedToDate = dateTimeUtil.convertEpocToIntegerFormat(toEpoch);

    Map<String, Integer> result = new HashMap<>();
    result.put("convertedFromDate", convertedFromDate);
    result.put("convertedToDate", convertedToDate);

    return result;
  }

  private List<Gillco3rd5thHolisticReportDto.ChildBetter> buildChildBetter(
      Student student, String orgSlug) {

    Section section = student.getSection();

    List<ProgressCardDto.PeerAssessment> peerAssessments =
        progressCardService.getPeerAssessmentDetails(section.getGradeSlug(), orgSlug, student);

    if (peerAssessments == null || peerAssessments.isEmpty()) {
      return Collections.emptyList();
    }

    return peerAssessments.stream()
        .map(
            peerAssessment -> {
              String statement = peerAssessment.childBetterStatement();
              String term1value = peerAssessment.childBetterTerm1().getValue();
              String term2value = peerAssessment.childBetterTerm2().getValue();
              return Gillco3rd5thHolisticReportDto.ChildBetter.builder()
                  .statement(statement)
                  .term1Value(term1value)
                  .term2Value(term2value)
                  .build();
            })
        .collect(Collectors.toList());
  }

  private Gillco3rd5thHolisticReportDto.Resource buildResouces(ProgressCard studentData) {

    if (studentData == null || studentData.getParentFacilitator() == null) {
      return Gillco3rd5thHolisticReportDto.Resource.builder().build();
    }

    var parentFacilitator = studentData.getParentFacilitator();
    return Gillco3rd5thHolisticReportDto.Resource.builder()
        .books(parentFacilitator.getBooks())
        .magazine(parentFacilitator.getMagazine())
        .toysAndGames(parentFacilitator.getToyAndGames())
        .tableOrMobile(parentFacilitator.getTabletOrMobile())
        .laptop(parentFacilitator.getLaptop())
        .internet(parentFacilitator.getInternet())
        .build();
  }

  private Gillco3rd5thHolisticReportDto.TableMarks buildTableMarks(
      Student student, String orgSlug) {

    var termAssessments =
        termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
            Arrays.asList("t1"), student.getSection().getGradeSlug());

    var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();

    var data =
        reportCardConfigDataRepository.getStudentReportByStudentAndAssessments(
            student.getId(), termAssessmentIds);
    if (Objects.isNull(data) || data.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report has not been generated");
    }

    var scholasticDataList =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.MANDATORY.name().equals(x.getType()))
            .toList();

    var optionalData =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.OPTIONAL.name().equals(x.getType()))
            .toList();

    var sortedData =
        sortTable(
            scholosticMandatory(scholasticDataList, orgSlug),
            scholosticOptional(optionalData, orgSlug));
    return Gillco3rd5thHolisticReportDto.TableMarks.builder()
        .scholosticMandatory(sortedData.scholosticMandatory())
        .scholosticOptional(sortedData.scholosticOptional())
        .build();
  }

  private Gillco3rd5thHolisticReportDto.TableMarks sortTable(
      List<Gillco3rd5thHolisticReportDto.ScholosticMandatory> scholosticMandatory,
      List<Gillco3rd5thHolisticReportDto.ScholosticOptional> scholosticOptional) {
    return Gillco3rd5thHolisticReportDto.TableMarks.builder()
        .scholosticMandatory(scholosticMandatory)
        .scholosticOptional(scholosticOptional)
        .build();
  }

  private List<Gillco3rd5thHolisticReportDto.ScholosticOptional> scholosticOptional(
      List<LowerGradeReportCardData> optionalData, String orgSlug) {

    List<Gillco3rd5thHolisticReportDto.ScholosticOptional> marksList = new ArrayList<>();

    var scholasticOptDataMap =
        optionalData.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));

    scholasticOptDataMap.forEach(
        (subject, scholasticData) -> {
          var term1Pa = getTermMarks("pa1", scholasticData);
          var theoryMarks = getTermMarks("theory", scholasticData);
          var practicalMarks = getTermMarks("practical", scholasticData);

          marksList.add(
              Gillco3rd5thHolisticReportDto.ScholosticOptional.builder()
                  .subject(subject)
                  .term1Pa(term1Pa)
                  .term1Theory(theoryMarks)
                  .term1Practical(practicalMarks)
                  .term2Pa(null)
                  .term2Theory(null)
                  .term2Practical(null)
                  .build());
        });

    return marksList;
  }

  private List<Gillco3rd5thHolisticReportDto.ScholosticMandatory> scholosticMandatory(
      List<LowerGradeReportCardData> reportCardData, String orgSlug) {

    List<Gillco3rd5thHolisticReportDto.ScholosticMandatory> marksList = new ArrayList<>();

    var scholasticDataMap =
        reportCardData.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));

    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          var term1Pa = getBestPeriodicAssessment("pa1", scholasticData);
          var term1NAP = getBestScoreOutOfFive("portfolio", scholasticData);
          var term1Sa = getBestScoreOutOfFive("se", scholasticData);
          var term1Marks = getTermMarks("hye", scholasticData);

          var term2Pa = getBestPeriodicAssessment("pa2", scholasticData);
          var term2NAP = getBestScoreOutOfFive("portfolio2", scholasticData);
          var term2Sa = getBestScoreOutOfFive("se2", scholasticData);
          var term2Marks = getTermMarks("ye", scholasticData);

          var term1Total = sumMarks(term1Pa, term1NAP, term1Sa, term1Marks);
          var term2Total = sumMarks(term2Pa, term2NAP, term2Sa, term2Marks);

          marksList.add(
              Gillco3rd5thHolisticReportDto.ScholosticMandatory.builder()
                  .subject(subject)
                  .term1Pa(term1Pa)
                  .term1NAP(term1NAP)
                  .term1Sa(term1Sa)
                  .term1Marks(term1Marks)
                  .term1Total(term1Total)
                  .term2Pa(term2Pa)
                  .term2NAP(term2NAP)
                  .term2Sa(term2Sa)
                  .term2Marks(term2Marks)
                  .term2Total(term2Total)
                  .build());
        });

    return marksList;
  }

  private String sumMarks(String... marks) {
    double total =
        Arrays.stream(marks)
            .filter(mark -> mark != null && !mark.isEmpty() && mark.matches("\\d+(\\.\\d+)?"))
            .mapToDouble(Double::parseDouble)
            .sum();

    return String.format("%.1f", total);
  }

  private String getTermMarks(String assessmentSlug, List<LowerGradeReportCardData> dataList) {
    if (dataList == null || dataList.isEmpty() || assessmentSlug == null) {
      return null;
    }

    var matched =
        dataList.stream()
            .filter(x -> assessmentSlug.equalsIgnoreCase(x.getAssessmentSlug()))
            .findFirst();

    if (matched.isEmpty()) {
      return null;
    }

    var data = matched.get();

    if ("true".equalsIgnoreCase(data.getIsAttended())) {
      Double marks = data.getMarks();
      return marks != null ? String.format("%.1f", marks) : null;
    } else {
      String remarks = data.getRemarks();
      return (remarks == null || remarks.isBlank()) ? "AB" : null;
    }
  }

  private String getBestScoreOutOfFive(
      String assessmentSlug, List<LowerGradeReportCardData> dataList) {
    if (assessmentSlug == null || dataList == null || dataList.isEmpty()) {
      return null;
    }

    double bestScoreOutOf10 = 0.0;

    for (LowerGradeReportCardData data : dataList) {
      if (!assessmentSlug.equalsIgnoreCase(data.getAssessmentSlug())) {
        continue;
      }

      if (!"true".equalsIgnoreCase(data.getIsAttended())) {
        continue;
      }

      Double marks = data.getMarks();
      Long subjectMarks = data.getSubjectMarks();

      if (marks != null && subjectMarks != null && subjectMarks > 0) {
        double normalized = (marks / subjectMarks) * 5.0;
        bestScoreOutOf10 = Math.max(bestScoreOutOf10, normalized);
      }
    }

    return String.format("%.1f", bestScoreOutOf10);
  }

  private String getBestPeriodicAssessment(
      String assessmentSlug, List<LowerGradeReportCardData> dataList) {
    if (assessmentSlug == null || dataList == null || dataList.isEmpty()) {
      return null;
    }

    double bestScoreOutOf10 = 0.0;

    for (LowerGradeReportCardData data : dataList) {
      if (!assessmentSlug.equalsIgnoreCase(data.getAssessmentSlug())) {
        continue;
      }

      if (!"true".equalsIgnoreCase(data.getIsAttended())) {
        continue;
      }

      Double marks = data.getMarks();
      Long subjectMarks = data.getSubjectMarks();

      if (marks != null && subjectMarks != null && subjectMarks > 0) {
        double normalized = (marks / subjectMarks) * 10.0;
        bestScoreOutOf10 = Math.max(bestScoreOutOf10, normalized);
      }
    }

    return String.format("%.1f", bestScoreOutOf10);
  }

  private Gillco3rd5thHolisticReportDto.Facilitators buildFacilitators(Student student) {

    var facilitatorStudentOpt = facilitatorStudentsRepository.findByStudentId(student.getId());
    if (facilitatorStudentOpt.isEmpty()) {
      return Gillco3rd5thHolisticReportDto.Facilitators.builder().build();
    }
    var facilitator = facilitatorStudentOpt.get().getFacilitator();

    return Gillco3rd5thHolisticReportDto.Facilitators.builder()
        .areaOfStrength(buildAreaOfStrength(facilitator))
        .barrierOfSuccess(buildBarrierOfSuccess(facilitator))
        .studentProgress(buildStudentProgress(facilitator))
        .build();
  }

  private Gillco3rd5thHolisticReportDto.StudentProgress buildStudentProgress(
      Facilitator facilitator) {
    if (facilitator == null || facilitator.getStudentProgress() == null) {
      return Gillco3rd5thHolisticReportDto.StudentProgress.builder().build();
    }

    return Gillco3rd5thHolisticReportDto.StudentProgress.builder()
        .help(facilitator.getStudentProgress())
        .steps(facilitator.getFutureSteps())
        .build();
  }

  private Gillco3rd5thHolisticReportDto.BarrierOfSuccess buildBarrierOfSuccess(
      Facilitator facilitator) {
    if (facilitator == null || facilitator.getBarrierOfSuccess() == null) {
      return Gillco3rd5thHolisticReportDto.BarrierOfSuccess.builder().build();
    }
    ProgressCardDto.BarrierofSuccess barrierOfSuccess = facilitator.getBarrierOfSuccess();
    return Gillco3rd5thHolisticReportDto.BarrierOfSuccess.builder()
        .loa(barrierOfSuccess.lackOfAttention())
        .lom(barrierOfSuccess.lackOfMotivation())
        .lop(barrierOfSuccess.lackOfPreparation())
        .pp(barrierOfSuccess.peerPressure())
        .ug(barrierOfSuccess.undefinedGoals())
        .di(barrierOfSuccess.domesticIssues())
        .ibc(barrierOfSuccess.inappropriateBehaviourInClassRoom())
        .sii(barrierOfSuccess.severeIllnessOfInjury())
        .none(barrierOfSuccess.none())
        .ao(barrierOfSuccess.anyOther())
        .aoValue(barrierOfSuccess.otherValue())
        .build();
  }

  private Gillco3rd5thHolisticReportDto.AreaOfStrength buildAreaOfStrength(
      Facilitator facilitator) {
    if (facilitator == null) {
      return Gillco3rd5thHolisticReportDto.AreaOfStrength.builder().build();
    }
    var areaOfStrength = facilitator.getAreaOfStrength();
    return Gillco3rd5thHolisticReportDto.AreaOfStrength.builder()
        .fb(areaOfStrength.takeFeedbackPositively())
        .wi(areaOfStrength.workIndependently())
        .ec(areaOfStrength.effectiveCommunication())
        .sft(areaOfStrength.solutionsFocusedThinking())
        .em(areaOfStrength.empathetic())
        .op(areaOfStrength.organizationAndPrioritization())
        .cs(areaOfStrength.collaborativeSkills())
        .re(areaOfStrength.responsible())
        .cre(areaOfStrength.creative())
        .con(areaOfStrength.concentration())
        .ao(areaOfStrength.anyOther())
        .aoValue(areaOfStrength.otherValue())
        .build();
  }

  private Gillco3rd5thHolisticReportDto.ParticipationAchievements buildParticipationAchievements(
      Student student) {

    var facilitatorStudentOpt = facilitatorStudentsRepository.findByStudentId(student.getId());
    if (facilitatorStudentOpt.isEmpty()) {
      return Gillco3rd5thHolisticReportDto.ParticipationAchievements.builder().build();
    }
    var facilitator = facilitatorStudentOpt.get().getFacilitator();

    return Gillco3rd5thHolisticReportDto.ParticipationAchievements.builder()
        .specificAchievements(facilitator.getAchievements())
        .specificParticipation(facilitator.getParticipation())
        .build();
  }

  private List<Gillco3rd5thHolisticReportDto.ScholosticSkillDetails>
      buildClassFacilitatorGroupedSkills(Student student, String orgSlug) {
    List<Facilitator> facilitators =
        facilitatorRepository.findByOrgSlugAndGradeSlug(
            orgSlug, student.getSection().getGradeSlug());
    if (facilitators == null || facilitators.isEmpty()) {
      return Collections.emptyList();
    }
    List<Gillco3rd5thHolisticReportDto.ScholosticSkillDetails> skillDetailsList = new ArrayList<>();
    facilitators.forEach(
        facilitator -> {
          List<Gillco3rd5thHolisticReportDto.Skills> skills =
              facilitator.getStudents().stream()
                  .map(
                      skill ->
                          Gillco3rd5thHolisticReportDto.Skills.builder()
                              .skillName(facilitator.getName())
                              .term1Value(skill.getTerm1().getValue())
                              .term2Value(skill.getTerm2().getValue())
                              .build())
                  .collect(Collectors.toList());

          skillDetailsList.add(
              Gillco3rd5thHolisticReportDto.ScholosticSkillDetails.builder()
                  .subject(facilitator.getSkill())
                  .skills(skills)
                  .build());
        });
    return skillDetailsList;
  }

  private List<Gillco3rd5thHolisticReportDto.SummarySheetDetails> buildSummarySheetDetails(
      Student student, String orgSlug) {

    List<Facilitator> facilitators =
        facilitatorRepository.findByOrgSlugAndGradeSlug(
            orgSlug, student.getSection().getGradeSlug());
    if (facilitators == null || facilitators.isEmpty()) {
      return Collections.emptyList();
    }

    return facilitators.stream()
        .filter(facilitator -> facilitator.getSummaryDomainName() != null)
        .flatMap(
            facilitator ->
                facilitator.getStudents().stream()
                    .map(
                        descriptor ->
                            Gillco3rd5thHolisticReportDto.SummarySheetDetails.builder()
                                .domainName(facilitator.getSummaryDomainName())
                                .term1Value(descriptor.getSummaryDescriptorTerm1().getValue())
                                .term2Value(descriptor.getSummaryDescriptorTerm2().getValue())
                                .build()))
        .collect(Collectors.toList());
  }

  private List<Gillco3rd5thHolisticReportDto.ScholosticSkillDetails> buildCoScolosticSkillDetails(
      List<ProgressCardDto.Competencies> competencies, Student student) {

    if (competencies == null || competencies.isEmpty()) {
      return Collections.emptyList();
    }

    MultiMap<String, String> subjectMap = getReportResource(gillcoSubjectsResource);
    Collection<String> subjectSlugs = subjectMap.get("scholostic-optional");

    return competencies.stream()
        .filter(competency -> subjectSlugs.contains(competency.subjectSlug()))
        .map(
            competency -> {
              var firstSkill = competency.skills().getFirst();
              var skills =
                  firstSkill.competencyDetails().stream()
                      .map(
                          detail ->
                              Gillco3rd5thHolisticReportDto.Skills.builder()
                                  .skillName(detail.name())
                                  .term1Value(
                                      detail.term1() != null ? detail.term1().getValue() : null)
                                  .term2Value(
                                      detail.term2() != null ? detail.term2().getValue() : null)
                                  .build())
                      .toList();

              return Gillco3rd5thHolisticReportDto.ScholosticSkillDetails.builder()
                  .subject(competency.subjectName())
                  .skills(skills)
                  .build();
            })
        .toList();
  }

  public MultiMap<String, String> getReportResource(Resource resource) {
    try {
      var objectMapper = new ObjectMapper();
      return objectMapper.readValue(ResourceUtils.asString(resource), new TypeReference<>() {});
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report config not found", e);
    }
  }

  private List<Gillco3rd5thHolisticReportDto.ScholosticSkillDetails> buildScholosticSkillDetails(
      List<ProgressCardDto.Competencies> competencies, Student student) {

    if (competencies == null || competencies.isEmpty()) {
      return Collections.emptyList();
    }

    MultiMap<String, String> subjectMap = getReportResource(gillcoSubjectsResource);
    Collection<String> subjectSlugs = subjectMap.get("scholostic-mandatory");

    return competencies.stream()
        .filter(competency -> subjectSlugs.contains(competency.subjectSlug()))
        .map(
            competency -> {
              var firstSkill = competency.skills().getFirst();
              var skills =
                  firstSkill.competencyDetails().stream()
                      .map(
                          detail ->
                              Gillco3rd5thHolisticReportDto.Skills.builder()
                                  .skillName(detail.name())
                                  .term1Value(
                                      detail.term1() != null ? detail.term1().getValue() : null)
                                  .term2Value(
                                      detail.term2() != null ? detail.term2().getValue() : null)
                                  .build())
                      .toList();

              return Gillco3rd5thHolisticReportDto.ScholosticSkillDetails.builder()
                  .subject(competency.subjectName())
                  .skills(skills)
                  .build();
            })
        .toList();
  }

  private Gillco3rd5thHolisticReportDto.Support buildSupport(ProgressCard progressCard) {

    if (progressCard == null || progressCard.getChildSupport() == null) {
      return Gillco3rd5thHolisticReportDto.Support.builder().build();
    }
    var childSupport = progressCard.getChildSupport();

    return Gillco3rd5thHolisticReportDto.Support.builder()
        .englishReading(childSupport.getEnglishReading())
        .englishCommunication(childSupport.getEnglishOralCommunication())
        .hindiReading(childSupport.getHindiReading())
        .hindiCommunication(childSupport.getHindiOralCommunication())
        .punjabiReading(childSupport.getPunjabiReading())
        .punjabiCommunication(childSupport.getPunjabiOralCommunication())
        .reading(childSupport.getReadingSupport())
        .numberAndMath(childSupport.getNumbersAndMath())
        .selfConfidence(childSupport.getSelfConfidence())
        .workingWithChildren(childSupport.getWorkingWithOtherChildren())
        .workingIndependently(childSupport.getWorkingIndependentlyAtHome())
        .subjectAreas(childSupport.getOtherSubjectAreas())
        .build();
  }

  private List<Gillco3rd5thHolisticReportDto.SelfAndPeerAssessments> buildSelfAndPeerAssessments(
      Student student, String orgSlug) {

    var section = student.getSection();

    List<ProgressCardDto.SelfAssessment> selfAssessments =
        progressCardService.getSelfAssessmentDetails(section.getGradeSlug(), orgSlug, student);

    List<ProgressCardDto.PeerAssessment> peerAssessments =
        progressCardService.getPeerAssessmentDetails(section.getGradeSlug(), orgSlug, student);

    Map<String, ProgressCardDto.SelfAssessment> selfMap =
        selfAssessments.stream()
            .collect(Collectors.toMap(ProgressCardDto.SelfAssessment::descriptionName, sa -> sa));

    Map<String, ProgressCardDto.PeerAssessment> peerMap =
        peerAssessments.stream()
            .collect(Collectors.toMap(ProgressCardDto.PeerAssessment::descriptionName, pa -> pa));

    Set<String> allSkills = new LinkedHashSet<>();
    allSkills.addAll(selfMap.keySet());
    allSkills.addAll(peerMap.keySet());

    List<Gillco3rd5thHolisticReportDto.SelfAndPeerAssessments> responseList = new ArrayList<>();

    for (String skill : allSkills) {
      ProgressCardDto.SelfAssessment sa = selfMap.get(skill);
      ProgressCardDto.PeerAssessment pa = peerMap.get(skill);

      responseList.add(
          Gillco3rd5thHolisticReportDto.SelfAndPeerAssessments.builder()
              .skillName(skill)
              .saTerm1(sa != null ? sa.descriptorsTerm1().name() : null)
              .saTerm2(sa != null ? sa.descriptorsTerm2().name() : null)
              .paTerm1(
                  pa != null && pa.descriptorsTerm1() != null ? pa.descriptorsTerm1().name() : null)
              .paTerm2(
                  pa != null && pa.descriptorsTerm2() != null ? pa.descriptorsTerm2().name() : null)
              .build());
    }

    return responseList;
  }

  private Gillco3rd5thHolisticReportDto.MedicalProfile buildMedicalProfile(
      ProgressCard progressCard) {
    if (progressCard == null) {
      return Gillco3rd5thHolisticReportDto.MedicalProfile.builder().build();
    }

    return Gillco3rd5thHolisticReportDto.MedicalProfile.builder()
        .bloodGroup(progressCard.getBloodGroup())
        .height(progressCard.getTerm1Height())
        .weight(progressCard.getTerm1Weight())
        .dental(progressCard.getDental())
        .rightEyeSight(progressCard.getEyesightR())
        .leftEyeSight(progressCard.getEyesightL())
        .build();
  }

  private Gillco3rd5thHolisticReportDto.Interests buildInterests(ProgressCard progressCard) {
    if (progressCard == null || progressCard.getInterestedActivities() == null) {
      return Gillco3rd5thHolisticReportDto.Interests.builder().build();
    }
    var interestedActivities = progressCard.getInterestedActivities();
    return Gillco3rd5thHolisticReportDto.Interests.builder()
        .reading(interestedActivities.getReading())
        .dancing(interestedActivities.getDancing())
        .singing(interestedActivities.getSinging())
        .musicalInstrument(interestedActivities.getPlayingMusicalInstrument())
        .sportsOrGames(interestedActivities.getSportOrGames())
        .writing(interestedActivities.getCreativeWriting())
        .gardening(interestedActivities.getGardening())
        .yoga(interestedActivities.getYoga())
        .art(interestedActivities.getArt())
        .craft(interestedActivities.getCraft())
        .cooking(interestedActivities.getCooking())
        .others(interestedActivities.getOther())
        .specify(interestedActivities.getOtherSpecify())
        .build();
  }

  private Gillco3rd5thHolisticReportDto.Attendance buildAttendance(
      Student student, String orgSlug, Integer fromDate, Integer toDate) {
    Section section = student.getSection();
    User user = student.getUserInfo();

    List<StudentsAttendanceReport> attendanceReports =
        sectionAttendanceRepository.getStudentMonthlyAttendance(
            orgSlug, section.getId(), user.getAuthUserId(), fromDate, toDate);

    Map<Integer, Long> presentMap = new HashMap<>();
    Map<Integer, Long> workingMap = new HashMap<>();
    Map<Integer, Long> percentageMap = new HashMap<>();

    for (StudentsAttendanceReport report : attendanceReports) {
      Integer month = report.getMonthNo();
      presentMap.put(month, report.getPresentDays());
      workingMap.put(month, report.getTotalWorkingDays());
      percentageMap.put(
          month,
          Math.round((double) report.getPresentDays() / report.getTotalWorkingDays() * 100.0));
    }

    return Gillco3rd5thHolisticReportDto.Attendance.builder()
        .attendingDays(buildDays("No.of Days Attended", presentMap))
        .workingDays(buildDays("No.of Working Days", workingMap))
        .percentage(buildDays("No.of Working Days", percentageMap))
        .build();
  }

  private Gillco3rd5thHolisticReportDto.Days buildDays(String title, Map<Integer, Long> dataMap) {
    return Gillco3rd5thHolisticReportDto.Days.builder()
        .title(title)
        .apr(dataMap.getOrDefault(4, 0L))
        .may(dataMap.getOrDefault(5, 0L))
        .jun(dataMap.getOrDefault(6, 0L))
        .july(dataMap.getOrDefault(7, 0L))
        .aug(dataMap.getOrDefault(8, 0L))
        .sep(dataMap.getOrDefault(9, 0L))
        .oct(dataMap.getOrDefault(10, 0L))
        .nov(dataMap.getOrDefault(11, 0L))
        .dec(dataMap.getOrDefault(12, 0L))
        .jan(dataMap.getOrDefault(1, 0L))
        .feb(dataMap.getOrDefault(2, 0L))
        .mar(dataMap.getOrDefault(3, 0L))
        .build();
  }
}
