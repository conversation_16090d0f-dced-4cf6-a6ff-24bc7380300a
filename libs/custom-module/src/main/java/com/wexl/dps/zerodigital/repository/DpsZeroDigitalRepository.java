package com.wexl.dps.zerodigital.repository;

import com.wexl.dps.zerodigital.dto.DpsZeroDigitalData;
import com.wexl.retail.zerodigital.repository.ZeroDigitalRepository;
import java.util.List;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface DpsZeroDigitalRepository extends ZeroDigitalRepository {

  @Query(
      value =
          """
                          select grade_slug as gradeSlug,sum(knowledge_percentage) as totalKnowledge,count(tskd.*) as totalCount,
                           SUM(knowledge_percentage) / CAST(COUNT(*) AS FLOAT) AS knowledgePercentage,
                           CASE WHEN subject_slug LIKE 'science%' THEN 'science'  WHEN subject_slug LIKE 'mathematics%' THEN 'mathematics'
                           WHEN subject_slug LIKE 'english%' THEN 'english' WHEN subject_slug LIKE 'social%' THEN 'social'
                           ELSE subject_slug END AS subjectSlug,
                           CASE WHEN subject_slug LIKE 'science%' THEN 'Science' WHEN subject_slug LIKE 'mathematics%' THEN 'Mathematics'
                           WHEN subject_slug LIKE 'english%' THEN 'English' WHEN subject_slug LIKE 'social%' THEN 'Social'
                           ELSE subject_slug END AS subjectName
                           from test_schedule_knowledge tsk join test_schedule_knowledge_details tskd  on tsk.id = tskd.test_schedule_knowledge_id
                           where board  = :boardSlug and org_slug  = :orgSlug
                           and (subject_slug LIKE 'english%' OR subject_slug LIKE 'social%' OR subject_slug LIKE 'science%' OR subject_slug LIKE 'mathematics%')
                           group by grade_slug ,subjectSlug,subjectName
            """,
      nativeQuery = true)
  List<DpsZeroDigitalData> getDpsZeroDigitalData(String orgSlug, String boardSlug);

  @Query(
      value =
          """
                  select se."name" as sectionSlug ,sum(knowledge_percentage) as totalKnowledge ,count(tskd.*) as totalCount,
                   SUM(knowledge_percentage) / CAST(COUNT(*) AS FLOAT) AS knowledgePercentage,
                   CASE WHEN subject_slug LIKE 'science%' THEN 'science'  WHEN subject_slug LIKE 'mathematics%' THEN 'mathematics'
                   WHEN subject_slug LIKE 'english%' THEN 'english' WHEN subject_slug LIKE 'social%' THEN 'social'
                   ELSE subject_slug END AS subjectSlug, CASE WHEN subject_slug LIKE 'science%' THEN 'Science' WHEN subject_slug LIKE 'mathematics%' THEN 'Mathematics'
                   WHEN subject_slug LIKE 'english%' THEN 'English' WHEN subject_slug LIKE 'social%' THEN 'Social'
                   ELSE subject_slug END AS subjectName from test_schedule_knowledge tsk join test_schedule_knowledge_details tskd  on tsk.id = tskd.test_schedule_knowledge_id                   \s
                   join students s on s.id = tsk.student_id
                   join sections se on se.id = s.section_id where board = :boardSlug and tsk.org_slug = :orgSlug
                   and tsk.grade_slug = :gradeSlug and (subject_slug LIKE 'english%' OR subject_slug LIKE 'social%' OR subject_slug LIKE 'science%' OR subject_slug LIKE 'mathematics%')
                   group by sectionSlug,subjectSlug,subjectName
                                     """,
      nativeQuery = true)
  List<DpsZeroDigitalData> getDpsZeroDigitalDataByGrade(
      String orgSlug, String boardSlug, String gradeSlug);

  @Query(
      value =
          """
                         select org_slug as orgSlug,sum(knowledge_percentage) ,count(tskd.*), SUM(knowledge_percentage) / CAST(COUNT(*) AS FLOAT) AS knowledgePercentage,
                          CASE WHEN subject_slug LIKE 'science%' THEN 'science'  WHEN subject_slug LIKE 'mathematics%' THEN 'mathematics'
                          WHEN subject_slug LIKE 'english%' THEN 'english' WHEN subject_slug LIKE 'social%' THEN 'social'
                          ELSE subject_slug END AS subjectSlug,
                          CASE WHEN subject_slug LIKE 'science%' THEN 'Science' WHEN subject_slug LIKE 'mathematics%' THEN 'Mathematics'
                          WHEN subject_slug LIKE 'english%' THEN 'English' WHEN subject_slug LIKE 'social%' THEN 'Social'
                          ELSE subject_slug END AS subjectName from test_schedule_knowledge tsk join test_schedule_knowledge_details tskd
                          on tsk.id = tskd.test_schedule_knowledge_id and org_slug in (:orgSlugs) and board = :boardSlug
                          and (subject_slug LIKE 'english%' OR subject_slug LIKE 'social%' OR subject_slug LIKE 'science%' OR subject_slug LIKE 'mathematics%')
                          group by subjectSlug,subjectName,org_slug
                          order by org_slug,subjectSlug,subjectName

                                          """,
      nativeQuery = true)
  List<DpsZeroDigitalData> getDpsManagerMlpData(List<String> orgSlugs, String boardSlug);

  @Query(
      value =
          """
                         select org_slug as orgSlug,sum(knowledge_percentage) ,count(tskd.*),SUM(knowledge_percentage) / CAST(COUNT(*) AS FLOAT) AS knowledgePercentage
                          from test_schedule_knowledge tsk join test_schedule_knowledge_details tskd
                          on tsk.id = tskd.test_schedule_knowledge_id and org_slug in (:orgSlugs) and grade_slug = :gradeSlug and board = :boardSlug
                          and (subject_slug LIKE 'english%' OR subject_slug LIKE 'social%' OR subject_slug LIKE 'science%' OR subject_slug LIKE 'mathematics%')
                          group by org_slug
                          order by org_slug
                                """,
      nativeQuery = true)
  List<DpsZeroDigitalData> getDpsManagerSectionMlpData(
      List<String> orgSlugs, String gradeSlug, String boardSlug);
}
