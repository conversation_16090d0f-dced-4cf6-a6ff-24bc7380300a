package com.wexl.dps.dto;

import java.util.List;
import lombok.Builder;

public record HallticketReportDto() {

  @Builder
  public record Response(Header header, Body body) {}

  @Builder
  public record Header(
      String schoolName, String academicYear, String address, String isoData, Long studentId) {}

  @Builder
  public record Body(
      String name,
      String className,
      String rollNumber,
      String sectionName,
      String admissionNumber,
      String mothersName,
      String fathersName,
      String dateOfBirth,
      String date,
      String eCard,
      String orgSlug,
      FirstTable firstTable,
      String examinationName,
      List<HallticketReportDto.Marks> marks) {}

  @Builder
  public record FirstTable(List<Marks> marks) {}

  @Builder
  public record Marks(
      String date,
      List<Subjects> subjects,
      String subjectName,
      String startTime,
      String endTime,
      String invigilator) {}

  @Builder
  public record Subjects(String subjectNames) {}

  @Builder
  public record TableMarks(List<Marks> marks) {}
}
