package com.wexl.dps.managereportcard.repository;

import com.wexl.retail.reportcards.model.ReportCardConfig;
import com.wexl.retail.reportcards.model.ReportCardConfigDetail;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface ReportCardConfigDetailRepository
    extends JpaRepository<ReportCardConfigDetail, Long> {

  List<ReportCardConfigDetail> findAllByReportCard(ReportCardConfig reportCard);

  @Query(
      value =
          """
          select * from report_card_config_details where report_card_config_id = :reportCardConfigId and term_assessment_id = :termAssessmentId
          """,
      nativeQuery = true)
  Optional<ReportCardConfigDetail> getSelectedCategoriesByReportCardConfigAndTermAssessment(
      Long reportCardConfigId, Long termAssessmentId);

  @Query(
      value =
          """
                  select * from report_card_config_details where report_card_config_id =:reportCardConfigId
                  """,
      nativeQuery = true)
  List<ReportCardConfigDetail> getReportCardConfigDetailsByConfigId(Long reportCardConfigId);
}
