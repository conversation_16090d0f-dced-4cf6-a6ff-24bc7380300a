package com.wexl.dps.preprimary.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.dps.dto.LearningLevel;
import java.util.List;
import lombok.Builder;

@Builder
public record DpsPrePrimaryAprDto() {
  @Builder
  public record SubjectRequest(
      @JsonProperty("math") LearningLevel mathematics,
      @JsonProperty("cll_listening") LearningLevel listening,
      @JsonProperty("cll_reading") LearningLevel reading,
      @JsonProperty("cll_speaking") LearningLevel speaking,
      @JsonProperty("cll_writing") LearningLevel writing,
      @JsonProperty("telugu_hindi") LearningLevel teluguOrHindi,
      @JsonProperty("understanding_of_the_world") LearningLevel untw,
      @JsonProperty("personal_social_phys_dev") LearningLevel psew,
      @JsonProperty("phy_dev") LearningLevel phyDev,
      @JsonProperty("term_id") Long termId) {}

  @Builder
  public record Attendance(
      @JsonProperty("total_attendance") Long totalAttendance,
      @JsonProperty("term_id") Long termId,
      @JsonProperty("present_attendance") Long attendancePresent) {}

  public record Remarks(
      @JsonProperty("comments") String remarks, @JsonProperty("term_id") Long termId) {}

  @Builder
  public record Summary(
      @JsonProperty("grade_facilitator") String teacherName,
      @JsonProperty("teacher_id") Long teacherId,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("assessment_id") Long assessmentId,
      @JsonProperty("assessment_name") String assessmentName,
      @JsonProperty("total_attendance") Long totalAttendance,
      @JsonProperty("term_id") Long termId,
      @JsonProperty("student_details") List<StudentDetails> studentDetails) {}

  @Builder
  public record StudentDetails(
      @JsonProperty("apr_id") Long aprId,
      @JsonProperty("math") LearningLevel mathematics,
      @JsonProperty("cll_listening") LearningLevel listening,
      @JsonProperty("cll_reading") LearningLevel reading,
      @JsonProperty("cll_speaking") LearningLevel speaking,
      @JsonProperty("cll_writing") LearningLevel writing,
      @JsonProperty("understanding_of_the_world") LearningLevel untw,
      @JsonProperty("personal_social_phys_dev") LearningLevel psew,
      @JsonProperty("phy_dev") LearningLevel phyDev,
      @JsonProperty("telugu_hindi") LearningLevel teluguOrHindi,
      @JsonProperty("candidate_name") String studentName,
      @JsonProperty("student_id") Long studentId,
      @JsonProperty("comments") String remarks,
      @JsonProperty("present_attendance") Long attendancePresent) {}
}
