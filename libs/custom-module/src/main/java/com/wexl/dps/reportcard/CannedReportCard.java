package com.wexl.dps.reportcard;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.OfflineTestDefinition;
import com.wexl.retail.offlinetest.model.OfflineTestScheduleStudent;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleStudentAttendanceRepository;
import com.wexl.retail.offlinetest.repository.OfflineTestScheduleStudentRepository;
import com.wexl.retail.offlinetest.service.OfflineTestScheduleService;
import com.wexl.retail.offlinetest.service.pointscale.PointScaleEvaluator;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.student.attributes.service.StudentAttributeService;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsMetaData;
import com.wexl.retail.subjects.model.SubjectsMetadataStudents;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.subjects.repository.SubjectsMetadataStudentsRepository;
import java.math.BigDecimal;
import java.util.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class CannedReportCard extends BaseReportCardDefinition {

  private final OfflineTestScheduleService offlineTestScheduleService;
  private final OfflineTestScheduleStudentRepository offlineTestScheduleStudentRepository;
  private final ReportCardService reportCardService;
  private final OfflineTestScheduleStudentAttendanceRepository
      offlineTestScheduleStudentAttendanceRepository;
  private final PointScaleEvaluator pointScaleEvaluator;
  private final SubjectsMetadataStudentsRepository subjectsMetadataStudentsRepository;
  private final StudentAttributeService studentAttributeService;

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = buildHeader(user.getStudentInfo(), org);
    var body = buildBody(user, request.offlineTestDefinitionId(), org);
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return List.of(
            "1st-2nd-report-card.xml",
            "3th-4th-7th-to-12th-report-card.xml",
            "5th-6th-report-card.xml",
            "canned-report.xml")
        .contains(config);
  }

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, "admission_no");
  }

  public ReportCardDto.Body buildBody(
      User user, Long offlineTestDefinitionId, Organization organization) {
    var student = user.getStudentInfo();
    var teacher = student.getSection().getClassTeacher();
    var guardians = student.getGuardians();
    var mother =
        guardians.stream().filter(x -> x.getRelationType().equals(GuardianRole.MOTHER)).findAny();
    var father =
        guardians.stream().filter(x -> x.getRelationType().equals(GuardianRole.FATHER)).findAny();
    var testDefinition =
        offlineTestScheduleService.validateOfflineTestDefinition(offlineTestDefinitionId);
    Optional<StudentAttributeValueModel> dateOfBirth =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    var tableMarks = buildTableMarks(testDefinition, student);
    return ReportCardDto.Body.builder()
        .name((user.getFirstName() + " " + user.getLastName()).toUpperCase())
        .dateOfBirth(dateOfBirth.map(StudentAttributeValueModel::getValue).orElse(null))
        .gradingScale(
            testDefinition.getGradeScaleSlug() != null
                ? testDefinition.getGradeScaleSlug()
                : "8point")
        .className(student.getSection().getName())
        .orgSlug(organization.getSlug())
        .testName(testDefinition.getTitle())
        .gradeSlug(student.getSection().getGradeSlug())
        .rollNumber(student.getClassRollNumber())
        .dateOfBirth(studentAttributeService.getDateOfBirthFormat(dateOfBirth))
        .mothersName(
            mother
                .map(guardian -> guardian.getFirstName() + " " + guardian.getLastName())
                .orElse(null))
        .fathersName(
            father
                .map(guardian -> guardian.getFirstName() + " " + guardian.getLastName())
                .orElse(null))
        .firstTable(buildFirstTable(testDefinition, tableMarks.firstTableMarks(), student))
        .secondTable(buildSecondTable(testDefinition, tableMarks.secondTableMarks()))
        .thirdTable(buildThirdTable(testDefinition, tableMarks.thirdTableMarks()))
        .fourthTable(buildThirdTable(testDefinition, tableMarks.forthTableMarks()))
        .attendance(buildAttendance(offlineTestDefinitionId, student))
        .classTeacher(teacher != null ? teacher.getUserInfo().getFirstName() : null)
        .build();
  }

  private ReportCardDto.Attendance buildAttendance(Long offlineTestDefinitionId, Student student) {
    var testDefinition =
        offlineTestScheduleService.validateOfflineTestDefinition(offlineTestDefinitionId);
    var studentData =
        offlineTestScheduleStudentAttendanceRepository.findByOfflineTestDefinitionAndStudentId(
            testDefinition, student.getId());
    if (studentData == null) {
      return ReportCardDto.Attendance.builder().build();
    }
    var attendancePercentage =
        studentData.getPresentDays() == null || testDefinition.getTotalAttendanceDays() == null
            ? null
            : calculatePercentage(
                studentData.getPresentDays(),
                Long.valueOf(testDefinition.getTotalAttendanceDays()));
    return ReportCardDto.Attendance.builder()
        .workingDays(
            testDefinition.getTotalAttendanceDays() == null
                ? null
                : Long.parseLong(testDefinition.getTotalAttendanceDays()))
        .daysPresent(studentData.getPresentDays() == null ? null : studentData.getPresentDays())
        .attendancePercentage(attendancePercentage)
        .remarks(studentData.getRemarks())
        .build();
  }

  private Double calculatePercentage(Long presentDays, Long workingDays) {
    if (presentDays == null || workingDays == null || workingDays == 0) {
      return 0D;
    }
    return (double) presentDays / workingDays * 100;
  }

  private ReportCardDto.ThirdTable buildThirdTable(
      OfflineTestDefinition testDefinition, List<ReportCardDto.Marks> marks) {
    return ReportCardDto.ThirdTable.builder()
        .title(testDefinition.getTitle() + ": COSCHOLASTIC")
        .marks(marks)
        .build();
  }

  private ReportCardDto.SecondTable buildSecondTable(
      OfflineTestDefinition testDefinition, List<ReportCardDto.Marks> marks) {
    return ReportCardDto.SecondTable.builder()
        .title(testDefinition.getTitle() + ": OPTIONAL")
        .marks(marks)
        .build();
  }

  private ReportCardDto.FirstTable buildFirstTable(
      OfflineTestDefinition testDefinition, List<ReportCardDto.Marks> marks, Student student) {
    return ReportCardDto.FirstTable.builder()
        .title(testDefinition.getTitle() + ": SCHOLASTIC")
        .marks(marks)
        .totals(buildTotals(student, testDefinition))
        .build();
  }

  private ReportCardDto.TableMarks buildTableMarks(
      OfflineTestDefinition testDefinition, Student student) {
    var testSchedules =
        testDefinition.getOfflineTestScheduleSchedule().stream()
            .filter(offlineTestSchedule -> offlineTestSchedule.getDeletedAt() == null)
            .toList();
    List<ReportCardDto.Marks> firstTableMarks = new ArrayList<>();
    List<ReportCardDto.Marks> secondTableMarks = new ArrayList<>();
    List<ReportCardDto.Marks> thirdTableMarks = new ArrayList<>();
    List<ReportCardDto.Marks> fourthTableMarks = new ArrayList<>();
    int thirdTableSeq = 1;

    for (var testSchedule : testSchedules) {
      if (testSchedule.getPublishedAt() == null) {
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST,
            "Please publish "
                + testSchedule.getSubjectsMetaData().getName()
                + " subject to download the report card");
      }
      var studentDataOptional =
          testSchedule.getOfflineTestScheduleStudents().stream()
              .filter(x -> x.getStudentId().equals(student.getId()))
              .findFirst();

      var studentsSubjectMetaData =
          subjectsMetadataStudentsRepository.findByStudentId(student.getId());

      if (studentDataOptional.isPresent()) {
        var studentData = studentDataOptional.get();
        var subjectsMetaDataOptional =
            getSubjectMetaData(
                studentsSubjectMetaData, testSchedule.getSubjectsMetaData().getName());

        if (subjectsMetaDataOptional.isPresent()) {
          var subjectsMetaData = subjectsMetaDataOptional.get();
          BigDecimal marksObtained =
              studentData.getMarks() == null ? BigDecimal.ZERO : studentData.getMarks();
          BigDecimal totalMarks =
              new BigDecimal(studentData.getOfflineTestScheduleDetails().getMarks());
          BigDecimal percentage =
              marksObtained
                  .divide(totalMarks, 3, BigDecimal.ROUND_HALF_UP)
                  .multiply(new BigDecimal(100));
          String grade1 =
              pointScaleEvaluator.evaluate(
                  "3point",
                  studentData.getMarks() == null ? BigDecimal.ZERO : studentData.getMarks());
          var marksBuilder =
              ReportCardDto.Marks.builder()
                  .maximumMarks(
                      String.valueOf(studentData.getOfflineTestScheduleDetails().getMarks()))
                  .marksObtained(
                      String.valueOf(
                          studentData.getMarks() == null
                              ? Boolean.TRUE.equals(studentData.getIsAttended())
                                  ? "-"
                                  : (studentData.getRemarks() == null
                                      ? "-"
                                      : studentData.getRemarks().substring(0, 2).toUpperCase())
                              : studentData.getMarks()))
                  .gradeObtained(calculateGrade(percentage, testDefinition.getGradeScaleSlug()))
                  .internalAssessment(calculateMarks(studentData))
                  .annualExam(calculateMarks(studentData))
                  .subject(testSchedule.getSubjectsMetaData().getName())
                  .grade(calculateGrade(studentData.getMarks(), testDefinition.getGradeScaleSlug()))
                  .grade1(grade1);

          if (subjectsMetaData.getSubjectsTypeEnum().equals(SubjectsTypeEnum.OPTIONAL)) {
            marksBuilder.seqNo(subjectsMetaData.getSeqNo());
            secondTableMarks.add(marksBuilder.build());
          } else if (subjectsMetaData.getCategoryEnum().equals(SubjectsCategoryEnum.SCHOLASTIC)) {
            marksBuilder.seqNo(subjectsMetaData.getSeqNo());
            firstTableMarks.add(marksBuilder.build());
          } else if (subjectsMetaData.getCategoryEnum().equals(SubjectsCategoryEnum.CO_SCHOLASTIC)
              && subjectsMetaData.getSubjectsTypeEnum().equals(SubjectsTypeEnum.MANDATORY)) {
            marksBuilder.seqNo(subjectsMetaData.getSeqNo());
            fourthTableMarks.add(marksBuilder.build());
          } else {
            marksBuilder.sno(thirdTableSeq++);
            marksBuilder.seqNo(subjectsMetaData.getSeqNo());
            thirdTableMarks.add(marksBuilder.build());
          }
        }
      }
    }
    var sortTables =
        sortTable(firstTableMarks, secondTableMarks, thirdTableMarks, fourthTableMarks);
    return ReportCardDto.TableMarks.builder()
        .firstTableMarks(sortTables.firstTableMarks())
        .secondTableMarks(sortTables.secondTableMarks())
        .thirdTableMarks(sortTables.thirdTableMarks())
        .forthTableMarks(sortTables.forthTableMarks())
        .build();
  }

  private Optional<SubjectsMetaData> getSubjectMetaData(
      List<SubjectsMetadataStudents> studentsSubjectMetaData, String subjectName) {
    return studentsSubjectMetaData.stream()
        .map(SubjectsMetadataStudents::getSubjectsMetaData)
        .filter(subjectMetaData -> subjectMetaData.getName().equals(subjectName))
        .findFirst();
  }

  private ReportCardDto.TableMarks sortTable(
      List<ReportCardDto.Marks> firstTable,
      List<ReportCardDto.Marks> secondTable,
      List<ReportCardDto.Marks> thirdTable,
      List<ReportCardDto.Marks> fourthTable) {
    List<ReportCardDto.Marks> firstTableMarks = new ArrayList<>();
    List<ReportCardDto.Marks> secondTableMarks = new ArrayList<>();
    List<ReportCardDto.Marks> fourthTableMarks = new ArrayList<>();
    var sortedFirstTable =
        firstTable.stream().sorted(Comparator.comparingLong(ReportCardDto.Marks::seqNo)).toList();
    for (int i = 0; i < sortedFirstTable.size(); i++) {
      ReportCardDto.Marks mark = sortedFirstTable.get(i);
      firstTableMarks.add(
          ReportCardDto.Marks.builder()
              .sno(i + 1)
              .maximumMarks(mark.maximumMarks())
              .marksObtained(mark.marksObtained())
              .gradeObtained(mark.gradeObtained())
              .internalAssessment(mark.internalAssessment())
              .annualExam(mark.annualExam())
              .total(mark.total())
              .subject(mark.subject())
              .grade(mark.grade())
              .build());
    }
    var sortedSecondTable =
        secondTable.stream().sorted(Comparator.comparingLong(ReportCardDto.Marks::seqNo)).toList();
    for (int i = 0; i < sortedSecondTable.size(); i++) {
      ReportCardDto.Marks mark = sortedSecondTable.get(i);
      secondTableMarks.add(
          ReportCardDto.Marks.builder()
              .sno(sortedFirstTable.size() + i + 1)
              .maximumMarks(mark.maximumMarks())
              .marksObtained(mark.marksObtained())
              .gradeObtained(mark.gradeObtained())
              .internalAssessment(mark.internalAssessment())
              .annualExam(mark.annualExam())
              .total(mark.total())
              .subject(mark.subject())
              .grade(mark.grade())
              .build());
    }
    var sortedFourthTable =
        fourthTable.stream().sorted(Comparator.comparingLong(ReportCardDto.Marks::seqNo)).toList();
    for (int i = 0; i < sortedFourthTable.size(); i++) {
      ReportCardDto.Marks mark = sortedFourthTable.get(i);
      fourthTableMarks.add(
          ReportCardDto.Marks.builder()
              .sno((sortedFirstTable.size() + i) + (sortedSecondTable.size() + i + 1))
              .maximumMarks(mark.maximumMarks())
              .marksObtained(mark.marksObtained())
              .gradeObtained(mark.gradeObtained())
              .internalAssessment(mark.internalAssessment())
              .annualExam(mark.annualExam())
              .total(mark.total())
              .subject(mark.subject())
              .grade(mark.grade())
              .grade1(mark.grade1())
              .build());
    }

    return ReportCardDto.TableMarks.builder()
        .firstTableMarks(firstTableMarks)
        .secondTableMarks(secondTableMarks)
        .thirdTableMarks(thirdTable)
        .forthTableMarks(fourthTableMarks)
        .build();
  }

  private String calculateMarks(OfflineTestScheduleStudent studentData) {
    if (studentData.getIsAttended() == Boolean.FALSE) {
      return "A";
    } else if (studentData.getMarks() == null) {
      return null;
    }
    return studentData.getMarks().toString();
  }

  private ReportCardDto.Totals buildTotals(Student student, OfflineTestDefinition testDefinition) {
    var marks =
        offlineTestScheduleStudentRepository.getReportCardTotals(
            student.getId(), testDefinition.getId());

    var overAllPercentage =
        marks.getMarksScored() == 0
            ? 0L
            : Double.parseDouble(
                String.format("%.2f", marks.getMarksScored() * 100.0 / marks.getTotalMarks()));
    var overAllGrade =
        calculateGrade(BigDecimal.valueOf(overAllPercentage), testDefinition.getGradeScaleSlug());
    return ReportCardDto.Totals.builder()
        .totalMaximumMarks(marks.getTotalMarks())
        .totalMarksObtained(marks.getMarksScored())
        .totalGradeObtained(overAllGrade)
        .annualExam(marks.getMarksScored())
        .total(marks.getTotalMarks())
        .grade(calculateGrade(BigDecimal.valueOf(overAllPercentage), "FourPointScale"))
        .overallPercentage((double) overAllPercentage)
        .overallGrade(overAllGrade)
        .build();
  }

  private String calculateGrade(BigDecimal marks, String gradeScaleSlug) {
    return marks == null ? null : pointScaleEvaluator.evaluate(gradeScaleSlug, marks);
  }
}
