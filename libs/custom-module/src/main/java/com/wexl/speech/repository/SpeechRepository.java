package com.wexl.speech.repository;

import com.wexl.speech.domain.SpeechTask;
import jakarta.transaction.Transactional;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

public interface SpeechRepository extends JpaRepository<SpeechTask, Long> {

  List<SpeechTask> findAllBySpeechRefOrderByCreatedAtDesc(String speechRef);

  @Transactional
  void deleteBySpeechRef(String reference);
}
