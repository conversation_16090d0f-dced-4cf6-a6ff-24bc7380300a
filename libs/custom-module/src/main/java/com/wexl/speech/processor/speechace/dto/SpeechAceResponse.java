package com.wexl.speech.processor.speechace.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.speech.dto.SpeechEvaluation;
import java.util.List;

public record SpeechAceResponse() {

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record Response(
      @JsonProperty("status") String status,
      @JsonProperty("short_message") String shortMessage,
      @JsonProperty("detail_message") String detailMessage,
      @JsonProperty("text_score") TextScore textScores,
      @JsonProperty("speech_score") SpeechEvaluation.SpeechScore speechScore,
      @JsonProperty("version") String version,
      @JsonProperty("request_id") String requestId,
      @JsonProperty("quota_remaining") Integer quotaRemaining) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record TextScore(
      @JsonProperty("text") String text,
      @JsonProperty("word_score_list") List<WordScore> scores,
      @JsonProperty("ielts_score") PronunciationScore ieltsScore,
      @JsonProperty("speechace_score") PronunciationScore speechAceScore,
      @JsonProperty("pte_score") PronunciationScore pteScore,
      @JsonProperty("toeic_score") PronunciationScore toeicScore,
      @JsonProperty("cefr_score") CefrScore cefrScore) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record CefrScore(String pronunciation) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record PronunciationScore(float pronunciation) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record WordScore(
      @JsonProperty("word") String word,
      @JsonProperty("quality_score") Double qualityScore,
      @JsonProperty("phone_score_list") List<PhoneScore> phoneScoreList,
      @JsonProperty("syllable_score_list") List<SyllableScore> syllableScoreList) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record PhoneScore(
      @JsonProperty("phone") String phone,
      @JsonProperty("stress_level") String stressLevel,
      @JsonProperty("quality_score") Double qualityScore,
      @JsonProperty("extent") List<Long> extent,
      @JsonProperty("sound_most_like") String soundMostLike) {}

  @JsonIgnoreProperties(ignoreUnknown = true)
  public record SyllableScore(
      @JsonProperty("phone_count") int phoneCount,
      @JsonProperty("stress_level") int stressLevel,
      @JsonProperty("letters") String letters,
      @JsonProperty("quality_score") Double qualityScore,
      @JsonProperty("stress_score") float stressScore,
      @JsonProperty("extent") List<Long> extent,
      @JsonProperty("predicted_stress_level") int predictedStressLevel) {}
}
