package com.wexl.studentmarksmigration.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.model.Student;
import com.wexl.retail.offlinetest.dto.OfflineTestScheduleDto;
import com.wexl.retail.offlinetest.model.OfflineTestSchedule;
import com.wexl.retail.offlinetest.model.OfflineTestScheduleStudent;
import com.wexl.retail.offlinetest.repository.*;
import com.wexl.retail.offlinetest.service.OfflineTestScheduleService;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.subjects.model.SubjectsMetaData;
import com.wexl.studentmarksmigration.PallaviStudentMarksMigration;
import com.wexl.studentmarksmigration.dto.StudentMarksMigrationDto;
import com.wexl.studentmarksmigration.repository.PallaviStudentMarksMigrationRepository;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Date;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class StudentMarksMigrationService {

  private final PallaviStudentMarksMigrationRepository studentMarksMigrationRepository;
  private final OfflineTestDefinitionRepository offlineTestDefinitionRepository;
  private final StudentRepository studentRepository;
  private final OfflineTestScheduleService offlineTestScheduleService;
  private final OfflineTestScheduleRepository offlineTestScheduleRepository;

  public void migrateStudentMarksByStudentRollNumber(
      StudentMarksMigrationDto.StudentMarksMigrationRequest request) {

    var offlineTestDefinition =
        offlineTestDefinitionRepository
            .findAllByIdAndOrgSlug(request.otdId(), request.orgSlug())
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST,
                        "error.Invalid.OfflineTestSchedule",
                        new String[] {Long.toString(request.otdId())}));

    var offlineTestSchedules = offlineTestDefinition.getOfflineTestScheduleSchedule();
    var otsStudentIds =
        offlineTestSchedules.stream()
            .map(OfflineTestSchedule::getOfflineTestScheduleStudents)
            .flatMap(Collection::stream)
            .map(OfflineTestScheduleStudent::getStudentId)
            .toList();

    var subjectNames =
        offlineTestSchedules.stream()
            .map(OfflineTestSchedule::getSubjectsMetaData)
            .map(SubjectsMetaData::getName)
            .toList();

    var studentRollNumbers =
        studentRepository.findStudentListById(otsStudentIds).stream()
            .map(Student::getRollNumber)
            .toList();
    var studentMarksMigrations =
        studentMarksMigrationRepository.findBySubjectNameInAndClassRollNumberInAndTermAssessment(
            subjectNames,
            studentRollNumbers,
            offlineTestDefinition.getAssessmentCategory().getName());

    var subjectMigrationMap =
        studentMarksMigrations.stream()
            .collect(Collectors.groupingBy(PallaviStudentMarksMigration::getSubjectName));

    offlineTestSchedules.forEach(
        ots -> {
          var otsOfflineTestScheduleStudents = ots.getOfflineTestScheduleStudents();
          var marksMigrations = subjectMigrationMap.get(ots.getSubjectsMetaData().getName());
          if (Objects.isNull(marksMigrations)) {
            return;
          }
          var studentMigrationMap =
              marksMigrations.stream()
                  .collect(
                      Collectors.toMap(
                          PallaviStudentMarksMigration::getClassRollNumber, Function.identity()));

          otsOfflineTestScheduleStudents.forEach(
              otss -> {
                var student = studentRepository.findById(otss.getStudentId()).orElseThrow();
                var studentMarksMigration = studentMigrationMap.get(student.getRollNumber());

                if (studentMarksMigration == null) {
                  log.warn(
                      "No marks migration data found for student roll number: {}",
                      student.getRollNumber());
                  return;
                }
                for (OfflineTestSchedule offlineTestSchedule : offlineTestSchedules) {
                  var studentMarks =
                      OfflineTestScheduleDto.Students.builder()
                          .marks1Grade(studentMarksMigration.getMarks())
                          .build();

                  BigDecimal marks;
                  String marksValue = studentMarksMigration.getMarks();

                  if (marksValue != null && !marksValue.trim().isEmpty()) {
                    try {
                      marks = new BigDecimal(marksValue);
                    } catch (NumberFormatException e) {
                      marks =
                          offlineTestScheduleService.getMarksByGrade(
                              studentMarks, offlineTestSchedule);
                    }
                  } else {
                    marks =
                        offlineTestScheduleService.getMarksByGrade(
                            studentMarks, offlineTestSchedule);
                  }

                  otss.setMarks(marks);
                  otss.setMarks1(marks);
                  otss.setIsAttended(Objects.equals("PR", studentMarksMigration.getIsPresent()));
                  otss.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
                  log.info("save completed");
                }
              });
        });
    offlineTestDefinitionRepository.save(offlineTestDefinition);
    studentMarksMigrations.forEach(
        sm -> {
          sm.setOfflineTestDefinitionId(offlineTestDefinition.getId().toString());
          sm.setDeletedAt(new Date());
        });
    studentMarksMigrationRepository.saveAll(studentMarksMigrations);
  }
}
