package com.wexl.mgcv.timetable.repository;

import com.wexl.mgcv.timetable.model.MgcvTimeTable;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface MgcvTimeTableRepository extends JpaRepository<MgcvTimeTable, Long> {
  Optional<MgcvTimeTable> findByOrgSlugAndSectionUuid(String orgSlug, String sectionUuid);
}
