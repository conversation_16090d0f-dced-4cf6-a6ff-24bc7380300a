package com.wexl.mgcv.feeslite.dto;

import java.sql.Timestamp;

public interface MgcvStudent {

  String getUsername();

  String getFirstName();

  String getLastName();

  String getRollNumber();

  String getClassRollNumber();

  String getTerm1Status();

  String getTerm2Status();

  String getAcademicYear();

  String getSectionName();

  Long getUserId();

  Long getStudentId();

  int getClassId();

  int getBoardId();

  Timestamp getTerm1Date();

  Timestamp getTerm2Date();
}
