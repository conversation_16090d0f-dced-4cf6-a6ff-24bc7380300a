package com.wexl.metrics.bet;

import com.wexl.dps.dto.BetReportDto;
import com.wexl.dps.reportcard.BetReportCard;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.test.schedule.service.ScheduleTestService;
import java.util.*;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class BetExamService {
  private final BetReportCard betReportCard;
  private final UserRepository userRepository;
  private final ScheduleTestService scheduleTestService;
  private static final String SECTION_NAME = "sectionName";
  private static final String MARKS = "marks";
  private static final String TOTAL_QUESTIONS = "total_questions";

  public List<GenericMetricResponse> getBetExamAnalysis(String authUserId, String testScheduleId) {
    var user =
        userRepository
            .findByAuthUserId(authUserId)
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.UserNotFound"));
    var testSchedule = scheduleTestService.validateTestSchedule(Long.valueOf(testScheduleId));
    BetReportDto.Body reportCard = null;
    var testScheduleStudent =
        testSchedule.getScheduleTestStudent().stream()
            .filter(tss -> tss.getStudent().equals(user))
            .findFirst();
    if (testScheduleStudent.isPresent()
        && Objects.equals(testScheduleStudent.get().getStatus(), "COMPLETED")) {
      reportCard = betReportCard.buildBody(user.getStudentInfo(), testSchedule, null);
    }
    List<GenericMetricResponse> genericMetricResponses = new LinkedList<>();
    for (int i = 0; i < 4; i++) {
      genericMetricResponses.add(
          GenericMetricResponse.builder()
              .data(buildData(testSchedule.getTestDefinition().getTestName(), reportCard, i))
              .build());
    }
    return genericMetricResponses;
  }

  private Map<String, Object> buildData(String testName, BetReportDto.Body reportCard, int i) {
    Map<String, Object> map = new LinkedHashMap<>();
    if (Objects.isNull(reportCard)) {
      return map;
    }
    if (i == 0) {
      map.put("test_name", testName);
      map.put(SECTION_NAME, reportCard.section1Name());
      map.put(MARKS, reportCard.section1Value());
      map.put(TOTAL_QUESTIONS, reportCard.Section1Questions());
      return map;
    } else if (i == 1) {
      map.put(SECTION_NAME, reportCard.section2Name());
      map.put(MARKS, reportCard.section2Value());
      map.put(TOTAL_QUESTIONS, reportCard.Section2Questions());
      return map;
    } else if (i == 2) {
      map.put(SECTION_NAME, reportCard.section3Name());
      map.put(MARKS, reportCard.section3Value());
      map.put(TOTAL_QUESTIONS, reportCard.Section3Questions());
      return map;
    } else {
      map.put(SECTION_NAME, reportCard.section4Name());
      map.put(MARKS, reportCard.section4Value());
      map.put(TOTAL_QUESTIONS, reportCard.Section4Questions());
      return map;
    }
  }
}
