package com.wexl.retail.notification.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import java.sql.Timestamp;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TestRequest {
  private String userName;
  private String chapter;
  private String subject;
  private String subTopic;
  private String board;
  private String fullName;
  private String organization;
  private String grade;
  private String type;
  private Integer totalQuestions;
  private Integer correctAnswers;
  private Timestamp startTime;
  private Timestamp endTime;
  private String testUrl;
  private String resultUrl;
  private String correctionUrl;
  private List<String> section;
  private String parent;
  private long examId;
  private boolean corrected;
  private long studentId;
  private Float marksScored;
  private Float totalMarks;
  private Float percentage;
  private String firebaseToken;
}
