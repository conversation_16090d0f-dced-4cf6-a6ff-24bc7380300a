package com.wexl.erp.fees.service.rules;

import java.util.List;
import java.util.UUID;
import lombok.Builder;

public record RuleDto() {
  @Builder
  public record RuleParam(
      UUID feeMasterId, RuleParamType paramType, List<String> paramValues, String orgSlug) {}

  @Builder
  public record StudentFeeDto(
      Long id,
      String gradeSlug,
      String gender,
      String studentCategory,
      String sectionUuid,
      String orgSlug,
      String transportSlab,
      String hostelSlab) {}
}
