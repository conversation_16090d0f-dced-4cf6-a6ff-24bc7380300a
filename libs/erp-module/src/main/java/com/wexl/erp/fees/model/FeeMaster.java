package com.wexl.erp.fees.model;

import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.retail.model.Model;
import com.wexl.retail.model.User;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Type;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "fee_masters")
public class FeeMaster extends Model {
  @Id @GeneratedValue private UUID id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "fee_group_id", nullable = false)
  private FeeGroup feeGroup;

  @Column(name = "org_slug", nullable = false)
  private String orgSlug;

  @Column(name = "scope_type")
  private ScopeType scopeType;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "createdBy")
  private User createdBy;

  @Type(JsonType.class)
  @Column(columnDefinition = "jsonb")
  private FeeDto.Rules rules;

  @Column(name = "board_slug")
  private String boardSlug;

  @Column(name = "academic_year")
  private String academicYear;
}
