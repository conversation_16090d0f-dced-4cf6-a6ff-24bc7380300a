package com.wexl.erp.fees.events;

import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.retail.student.exam.Exam;
import java.util.UUID;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
public class FeeMasterEventPublisher {
  @Autowired private ApplicationEventPublisher applicationEventPublisher;

  public void publishCreateFeeMaster(
      UUID feeMasterId, FeeDto.FeeMasterRequest request, String orgSlug) {
    FeeMasterCreatedEvent createFeeMasterEvent =
        new FeeMasterCreatedEvent(feeMasterId, request, orgSlug);
    applicationEventPublisher.publishEvent(createFeeMasterEvent);
  }

  public void publishUpdateFeeMaster(final Exam exam) {
    FeeMasterUpdatedEvent updateFeeMasterEvent = new FeeMasterUpdatedEvent(exam);
    applicationEventPublisher.publishEvent(updateFeeMasterEvent);
  }
}
