package com.wexl.erp.infirmary.repository;

import com.wexl.erp.infirmary.model.InfirmaryEntry;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface InfirmaryRepository extends JpaRepository<InfirmaryEntry, Long> {
  List<InfirmaryEntry> findAllByStudentIdOrderByIdDesc(Long studentId);

  List<InfirmaryEntry> findAllByOrgSlug(String orgSlug);

  @Query(
      value =
          """
                  SELECT ie.*
                          FROM infirmary_entries ie
                          JOIN students s ON ie.student_id = s.id
                          JOIN sections sec ON s.section_id = sec.id
                          WHERE (:gradeSlug IS NULL OR sec.grade_slug = :gradeSlug)
                            AND (:sectionUuid IS NULL OR CAST(sec.uuid AS varchar) = :sectionUuid)
                            AND sec.organization = :orgSlug
                           AND ((:fromDate IS NULL OR to_char(ie.created_at,'yyyy-MM-dd') >=  :fromDate)
                           AND (:toDate IS NULL OR to_char(ie.created_at,'yyyy-MM-dd') <=  :toDate))
                          ORDER BY ie.created_at DESC
          """,
      nativeQuery = true)
  List<InfirmaryEntry> findAllByTeacherGradeAndSection(
      String orgSlug, String gradeSlug, String sectionUuid, String fromDate, String toDate);

  List<InfirmaryEntry> findByStudentIdAndOrgSlug(Long studentId, String orgSlug);
}
