package com.wexl.erp.infirmary.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;

public class InfirmaryEntryDto {

  @Builder
  public record Request(
      @JsonProperty("infirmary_id") Long infirmaryId,
      @JsonProperty("student_id") Long studentId,
      @JsonProperty("date") Long date,
      @JsonProperty("in_time") Long inTime,
      @JsonProperty("out_time") Long outTime,
      @JsonProperty("complaint") String complaint,
      @JsonProperty("treatment_or_advice") String treatmentOrAdvice,
      @JsonProperty("remarks") String remarks) {}

  @Builder
  public record Response(
      @JsonProperty("infirmaryId") Long infirmaryId,
      @JsonProperty("student_id") Long studentId,
      @JsonProperty("student_name") String studentName,
      @JsonProperty("grade") String grade,
      @JsonProperty("date") Long date,
      @JsonProperty("in_time") Long inTime,
      @JsonProperty("out_time") Long outTime,
      @JsonProperty("remarks") String remarks,
      @JsonProperty("complaint") String complaint,
      @JsonProperty("treatment_or_advice") String treatmentOrAdvice) {}

  @Builder
  public record StudentInfo(
      @JsonProperty("student_id") Long studentId,
      @JsonProperty("student_name") String studentName,
      @JsonProperty("grade") String grade,
      @JsonProperty("user_name") String userName,
      @JsonProperty("section") String section,
      @JsonProperty("auth_user_Id") String authUserId) {}

  @Builder
  public record SearchRequest(String gradeSlug, String sectionUuid, Long fromDate, Long toDate) {}
}
