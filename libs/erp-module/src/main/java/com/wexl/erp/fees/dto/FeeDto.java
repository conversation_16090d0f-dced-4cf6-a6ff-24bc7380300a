package com.wexl.erp.fees.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.erp.fees.model.ConcessionType;
import com.wexl.erp.fees.model.FineType;
import com.wexl.erp.fees.model.ScopeType;
import java.util.List;
import java.util.UUID;
import lombok.Builder;

public record FeeDto() {

  public record FeeTypeRequest(String name, String code, String description) {}

  @Builder
  public record FeeTypeResponse(
      UUID id,
      String name,
      String code,
      String description,
      @JsonProperty("created_by") String createdBy,
      @JsonProperty("created_by_user_id") Long userId,
      @JsonProperty("created_on") Long createdOn) {}

  public record FeeGroupRequest(String name, String description) {}

  @Builder
  public record FeeGroupResponse(
      UUID id,
      String name,
      @JsonProperty("is_active") Boolean isActive,
      String description,
      @JsonProperty("created_by") String createdBy,
      @JsonProperty("created_by_user_id") Long userId,
      @JsonProperty("published_at") Long publishedAt,
      @JsonProperty("created_on") Long createdOn) {}

  public record FeeGroupFeeTypeRequest(@JsonProperty("fee_types") List<FeeTypes> feeTypes) {}

  public record FeeTypes(
      FineType fineType,
      @JsonProperty("fee_type_id") String feeTypeId,
      @JsonProperty("due_date") Long dueDate,
      Long percentage,
      Double amount,
      @JsonProperty("fine_amount") Double fineAmount,
      @JsonProperty("remainder_days") Long remainderDays) {}

  @Builder
  public record FeeGroupFeeTypeResponse(
      @JsonProperty("fee_group_id") String feeGroupId,
      String name,
      String description,
      List<FeeTypesResponse> feeTypes) {}

  @Builder
  public record FeeTypesResponse(
      String id,
      FineType fineType,
      @JsonProperty("created_by") String createdBy,
      @JsonProperty("created_by_user_id") Long userId,
      @JsonProperty("created_on") Long createdOn,
      @JsonProperty("fee_type_id") String feeTypeId,
      @JsonProperty("due_date") Long dueDate,
      Double amount,
      String name,
      Long percentage,
      @JsonProperty("fine_amount") Double fineAmount,
      @JsonProperty("remainder_days") Long remainderDays) {}

  public record FeeGroupFeeTypeUpdateRequest(
      FineType fineType,
      @JsonProperty("fee_type_id") String feeTypeId,
      @JsonProperty("due_date") Long dueDate,
      Double amount,
      @JsonProperty("fine_amount") Double fineAmount,
      @JsonProperty("remainder_days") Long remainderDays) {}

  @Builder
  public record FeeMasterRequest(
      @JsonProperty("fee_group_id") String feeGroupId,
      @JsonProperty("board_slug") String boardSlug,
      @JsonProperty("scope_type") ScopeType scopeType,
      @JsonProperty("grade_slug") List<String> gradeSlug,
      @JsonProperty("section_uuid") List<String> sectionUuid,
      @JsonProperty("student_id") List<Long> studentId) {}

  @Builder
  public record FeeMasterResponse(
      @JsonProperty("id") String id,
      @JsonProperty("fee_group_id") String feeGroupId,
      @JsonProperty("fee_group_description") String feeGroupDescription,
      @JsonProperty("fee_group_name") String feeGroupName,
      @JsonProperty("scope_type") ScopeType scopeType,
      @JsonProperty("created_by_user_id") Long userId,
      @JsonProperty("created_on") Long createdOn) {}

  @Builder
  public record FeeHeadResponse(
      @JsonProperty("student_id") Long studentId,
      @JsonProperty("student_name") String studentName,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("section_uuid") String sectionUuid,
      @JsonProperty("section_name") String sectionName) {}

  @Builder
  public record Rules(
      @JsonProperty("param_type") String paramType,
      @JsonProperty("param_value") List<String> paramValue) {}

  @Builder
  public record StudentsFeeHeadResponse(
      @JsonProperty("fee_head_id") String feeHeadId,
      @JsonProperty("student_id") Long studentId,
      @JsonProperty("student_name") String studentName,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("section_uuid") String sectionUuid,
      @JsonProperty("section_name") String sectionName,
      @JsonProperty("fee_master_id") UUID feeMasterId,
      Double amount,
      @JsonProperty("fine_amount") Double fineAmount,
      @JsonProperty("discount_amount") Double discountAmount,
      @JsonProperty("paid_amount") Double paidAmount,
      @JsonProperty("balance_amount") Double balanceAmount,
      @JsonProperty("due_date") Long dueDate,
      @JsonProperty("fee_type_id") UUID feeTypeId,
      @JsonProperty("fee_type_code") String feeTypeCode,
      @JsonProperty("fee_type_name") String feeTypeName,
      @JsonProperty("fee_type_description") String feeTypeDescription,
      @JsonProperty("created_on") Long createdOn) {}

  public record ConcessionRequest(
      @JsonProperty("concession_type") ConcessionType concessionType,
      Double value,
      String description) {}

  @Builder
  public record ConcessionResponse(
      UUID id,
      ConcessionType concessionType,
      @JsonProperty("created_on") Long createdOn,
      @JsonProperty("created_by") String createdBy,
      @JsonProperty("created_by_user_id") Long userId,
      Double value,
      @JsonProperty("is_published") Boolean isPublished,
      String description) {}

  public record ConcessionHeadRequest(
      @JsonProperty("fee_head_id") String feeHeadId,
      @JsonProperty("student_id") List<Long> studentId) {}

  @Builder
  public record ConcessionHeadResponse(
      @JsonProperty("fee_head_id") String feeHeadId,
      @JsonProperty("fee_type_code") String feeTypeCode,
      @JsonProperty("fee_type_name") String feeTypeName,
      @JsonProperty("fee_type_description") String feeTypeDescription,
      @JsonProperty("fee_master_id") String feeMasterId,
      @JsonProperty("fee_group_id") String feeGroupId,
      @JsonProperty("fee_group_description") String feeGroupDescription,
      @JsonProperty("fee_group_name") String feeGroupName,
      @JsonProperty("student_id") Long studentId,
      @JsonProperty("student_name") String studentName) {}

  @Builder
  public record CollectFeeRequest(Double amount) {}

  public record AssignConcessionRequest(
      @JsonProperty("board_slug") String boardSlug,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("fee_master_id") String feeMasterId,
      @JsonProperty("fee_group_id") String feeGroupId,
      @JsonProperty("fee_type_id") String feeTypeId,
      @JsonProperty("students") List<Long> students) {}
}
