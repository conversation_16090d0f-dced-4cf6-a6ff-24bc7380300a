package com.wexl.erp.fees.model;

import com.wexl.retail.model.Model;
import com.wexl.retail.model.User;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "fee_group_fee_type")
public class FeeGroupFeeType extends Model {
  @Id @GeneratedValue private UUID id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(
      name = "fee_group_id",
      nullable = false,
      foreignKey = @ForeignKey(name = "fk_fee_group_fee_type_fee_group"))
  private FeeGroup feeGroup;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(
      name = "fee_type_id",
      nullable = false,
      foreignKey = @ForeignKey(name = "fk_fee_group_fee_type_fee_type"))
  private FeeType feeType;

  private Double amount;

  @Column(name = "due_date")
  private LocalDateTime dueDate;

  @Column(name = "fine_type")
  private FineType fineType;

  @Column(name = "fine_amount")
  private double fineAmount;

  @Column(name = "remainder_days")
  private Long remainderDays;

  private Long percentage;

  @Column(name = "org_slug", nullable = false)
  private String orgSlug;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "createdBy")
  private User createdBy;
}
