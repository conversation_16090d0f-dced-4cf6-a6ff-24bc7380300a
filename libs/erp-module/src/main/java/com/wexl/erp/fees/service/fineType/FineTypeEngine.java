package com.wexl.erp.fees.service.fineType;

import com.wexl.erp.fees.model.FeeGroupFeeType;
import com.wexl.erp.fees.model.FeeHead;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class FineTypeEngine {

  private final List<FineTypeRule> fineTypeRules;

  public Double getFineAmount(FeeHead feeHead, FeeGroupFeeType feeGroupFeeType) {
    return fineTypeRules.stream()
        .filter(rule -> rule.supports(feeGroupFeeType))
        .findFirst()
        .map(rule -> rule.calculateFine(feeHead, feeGroupFeeType))
        .orElse(0.0);
  }
}
