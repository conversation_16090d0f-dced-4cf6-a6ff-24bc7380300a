package com.wexl.erp.fees.model;

import com.wexl.retail.model.Model;
import com.wexl.retail.model.Student;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "fee_heads")
public class FeeHead extends Model {
  @Id @GeneratedValue private UUID id;

  @Column(name = "org_slug", nullable = false)
  private String orgSlug;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "fee_master_id", nullable = false)
  private FeeMaster feeMaster;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "student_id")
  private Student student;

  private Double amount;

  @Column(name = "fine_amount")
  private Double fineAmount;

  @Column(name = "discount_amount")
  private Double discountAmount;

  @Column(name = "paid_amount")
  private Double paidAmount;

  @Column(name = "balance_amount")
  private Double balanceAmount;

  @Column(name = "due_date")
  private LocalDateTime dueDate;

  private FeeStatus status;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "fee_type_id", nullable = false)
  private FeeType feeType;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "concession_id")
  private Concession concession;
}
