package com.wexl.erp.infirmary.controller;

import com.wexl.erp.infirmary.dto.InfirmaryEntryDto;
import com.wexl.erp.infirmary.service.InfirmaryService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}/infirmary")
public class InfirmaryController {

  private final InfirmaryService infirmaryService;

  @ResponseStatus(HttpStatus.CREATED)
  @PostMapping
  public void addStudentToInfirmary(
      @RequestBody InfirmaryEntryDto.Request request,
      @PathVariable String orgSlug,
      @RequestParam(required = false) String staffAuthId) {
    infirmaryService.markEntry(request, orgSlug, staffAuthId);
  }

  @GetMapping("/student")
  public List<InfirmaryEntryDto.Response> getInfirmaryEntriesOfStudent(
      @RequestParam Long studentId) {
    return infirmaryService.getAllInfirmaryEntriesOfStudent(studentId);
  }

  @GetMapping
  public List<InfirmaryEntryDto.Response> getInfirmaryEntriesOfOrg(@PathVariable String orgSlug) {
    return infirmaryService.getAllInfirmaryEntriesOfOrg(orgSlug);
  }

  @ResponseStatus(HttpStatus.NO_CONTENT)
  @PutMapping
  public void updateInfirmaryEntry(@RequestBody InfirmaryEntryDto.Request request) {
    infirmaryService.updateInfirmaryEntry(request);
  }

  @ResponseStatus(HttpStatus.NO_CONTENT)
  @DeleteMapping
  public void deleteInfirmaryEntry(@RequestParam Long infirmaryId) {
    infirmaryService.deleteInfirmaryEntry(infirmaryId);
  }

  @GetMapping("/student-info")
  public List<InfirmaryEntryDto.StudentInfo> getStudentInfo(
      @PathVariable String orgSlug, @RequestParam String userName) {
    return infirmaryService.getStudentInfo(orgSlug, userName);
  }

  @IsOrgAdminOrTeacher
  @GetMapping("/teachers/{authUserId}")
  public List<InfirmaryEntryDto.Response> getSectionStudents(
      @PathVariable String orgSlug,
      @PathVariable String authUserId,
      InfirmaryEntryDto.SearchRequest request) {
    try {
      return infirmaryService.getAllStudentsBySectionAndGrade(orgSlug, authUserId, request);
    } catch (Exception e) {
      log.error("Error while fetching appointment requests: {}", e.getMessage(), e);
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  @IsOrgAdminOrTeacher
  @GetMapping("/teachers/{authUserId}/{searchKey}")
  public List<InfirmaryEntryDto.Response> getInfirmaryEntriesByStudentName(
      @PathVariable String searchKey,
      @PathVariable String orgSlug,
      @PathVariable String authUserId) {
    try {
      return infirmaryService.getInfirmaryEntriesByStudentName(searchKey, authUserId, orgSlug);
    } catch (Exception e) {
      log.error("Error while fetching appointment requests: {}", e.getMessage(), e);
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }
}
