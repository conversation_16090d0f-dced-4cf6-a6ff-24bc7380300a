package com.wexl.erp.fees.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "cumulative_fine")
public class CumulativeFine extends Model {
  @Id @GeneratedValue private UUID id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "fee_group_fee_type_id")
  private FeeGroupFeeType feeGroupFeeType;

  @Column(name = "fine_amount")
  private Double fineAmount;

  @Column(name = "due_date")
  private LocalDateTime dueDate;
}
