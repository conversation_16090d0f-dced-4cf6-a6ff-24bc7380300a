package com.wexl.retail.courses.step.controller;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.storage.S3FileUploadResult;
import com.wexl.retail.courses.step.dto.CourseStepRequest;
import com.wexl.retail.courses.step.dto.CourseStepResponse;
import com.wexl.retail.courses.step.dto.StepReorderRequest;
import com.wexl.retail.courses.step.service.CourseStepService;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}/courses/{courseDefId}/modules/{moduleId}/steps")
public class CourseStepController {

  private final CourseStepService courseStepService;

  @PostMapping
  public List<CourseStepResponse> createCourseStep(
      @PathVariable long moduleId, @Valid @RequestBody List<CourseStepRequest> courseStepRequests) {
    try {
      return courseStepService.createCourseStep(courseStepRequests, moduleId);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  @PutMapping("/{stepId}")
  public CourseStepResponse updateCourseStep(
      @PathVariable long stepId, @Valid @RequestBody CourseStepRequest courseStepRequest) {
    try {
      return courseStepService.updateCourseStep(courseStepRequest, stepId);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  @GetMapping
  public List<CourseStepResponse> getAllCourseStepsAssociatedToModule(@PathVariable long moduleId) {
    try {
      return courseStepService.getAllCourseStepsAssociatedToModule(moduleId);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  @PostMapping("/reorder")
  public void reorderSteps(
      @PathVariable long moduleId, @RequestBody StepReorderRequest stepReorderRequest) {
    try {
      courseStepService.reorderSteps(stepReorderRequest, moduleId);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  @PostMapping("/upload")
  public S3FileUploadResult uploadScormFiles(
      @PathVariable long moduleId, @Valid @RequestBody List<CourseStepRequest> courseStepRequests) {
    return courseStepService.uploadScormFiles(courseStepRequests.getFirst(), moduleId);
  }
}
