package com.wexl.retail.section.service;

import static java.util.Objects.nonNull;
import static org.apache.commons.lang3.ObjectUtils.isEmpty;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.AuthUtil;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.Entity;
import com.wexl.retail.content.model.Grade;
import com.wexl.retail.curriculum.SectionDto;
import com.wexl.retail.model.*;
import com.wexl.retail.organization.dto.CurriculumBoard;
import com.wexl.retail.organization.dto.CurriculumGrade;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.domain.SectionStatus;
import com.wexl.retail.section.domain.TeacherSection;
import com.wexl.retail.section.dto.ConnectedRetailTeacher;
import com.wexl.retail.section.dto.ConnectedStudent;
import com.wexl.retail.section.dto.ConnectedTeacher;
import com.wexl.retail.section.dto.request.SectionCreateRequest;
import com.wexl.retail.section.dto.response.GenericSectionResponse;
import com.wexl.retail.section.dto.response.SectionEntityDto;
import com.wexl.retail.section.dto.response.SectionResponse;
import com.wexl.retail.section.dto.response.SectionResponseQueryResult;
import com.wexl.retail.section.dto.response.SectionStudentResponse;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.services.StudentService;
import com.wexl.retail.teacher.orgs.TeacherOrgsService;
import com.wexl.retail.teacher.profile.TeacherProfileService;
import com.wexl.retail.util.Constants;
import com.wexl.retail.util.StrapiService;
import jakarta.validation.Valid;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class SectionService {

  public static final String UNAUTHORIZED_ACCESS = "Unauthorized Access";
  private static final String ERROR_UNAUTHORIZED = "error.UnAuthorized";
  @Autowired private StudentService studentService;

  @Autowired private UserRepository userRepository;

  @Autowired private TeacherProfileService teacherService;

  @Autowired private SectionRepository sectionRepository;

  @Autowired private TeacherSectionService teacherSectionService;
  @Autowired private StrapiService strapiService;
  @Autowired private StudentRepository studentRepository;
  @Autowired private AuthService authService;
  @Autowired private TeacherOrgsService teacherOrgsService;
  @Autowired private ContentService contentService;
  @Autowired private DateTimeUtil dateTimeUtil;
  @Autowired private TeacherRepository teacherRepository;

  @Autowired private OrganizationRepository organizationRepository;

  @SneakyThrows
  public Section createSection(
      final String orgSlug, @Valid final SectionCreateRequest sectionCreateRequest) {

    var organization = strapiService.getOrganizationBySlug(orgSlug);

    var existingSection =
        getSectionByNameAndOrg(sectionCreateRequest.getName(), organization.getSlug());

    if (existingSection != null) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.Section.Exists",
          new String[] {sectionCreateRequest.getName()});
    }

    var gradeId = -1;
    var gradeName = "";
    var gradeSlug = "";
    if (!isEmpty(sectionCreateRequest.getGradeSlug())) {
      final Grade grade = contentService.getGradeBySlug(sectionCreateRequest.getGradeSlug());
      gradeId = grade.getId();
      gradeName = grade.getName();
      gradeSlug = grade.getSlug();
    }
    final Entity board = strapiService.getEduBoardBySlug(sectionCreateRequest.getBoardSlug());

    final var section =
        Section.builder()
            .name(sectionCreateRequest.getName())
            .remarks(sectionCreateRequest.getRemarks())
            .gradeId(gradeId)
            .gradeName(gradeName)
            .gradeSlug(gradeSlug)
            .boardName(board.getName())
            .boardSlug(board.getSlug())
            .status(sectionCreateRequest.getStatus())
            .uuid(UUID.randomUUID())
            .organization(orgSlug)
            .build();
    sectionRepository.save(section);
    return section;
  }

  public void addTeacherToSection(long teacherId, Section sectionToConnect) {
    final var teacher = teacherService.getOne(teacherId);
    if (!teacher.getUserInfo().getOrganization().equals(sectionToConnect.getOrganization())) {
      throw new ApiException(InternalErrorCodes.UN_AUTHORIZED, ERROR_UNAUTHORIZED);
    }

    Optional<Section> connectedSection =
        teacher.getSections().stream()
            .filter(section -> section.equals(sectionToConnect))
            .findAny();

    if (connectedSection.isEmpty()) {
      int noOfUpdatedRemovedRecords =
          teacherSectionService.addSectionIfRemovedAlready(
              sectionToConnect.getId(), teacherId, Timestamp.valueOf(LocalDateTime.now()));
      if (noOfUpdatedRemovedRecords == 0) {
        teacherSectionService.save(
            TeacherSection.builder().section(sectionToConnect).teacher(teacher).build());
      } else {
        log.debug("updated teacher section which was removed earlier");
      }
    } else {
      log.debug("Teacher already a member of mentioned Section");
    }
  }

  public Section addTeacherToSectionById(Long teacherId, String sectionUuid) {
    final var sectionToConnect = findByUuid(sectionUuid);
    addTeacherToSection(teacherId, sectionToConnect);
    return sectionToConnect;
  }

  public List<SectionEntityDto.Response> getAllConnectedSectionsToTeacher(User userInfo) {
    final var teacher = teacherService.findByUserInfo(userInfo);
    if (AuthUtil.isOrgAdmin(teacher.getUserInfo())) {
      Set<Section> allSections =
          sectionRepository.findAllByOrganizationAndDeletedAtIsNullAndStatusOrderByName(
              teacher.getUserInfo().getOrganization(), SectionStatus.ACTIVE);
      return buildSectionListResponse(allSections);
    }
    return buildSectionListResponse(teacher.getSections());
  }

  public List<SectionEntityDto.Response> getAllConnectedSectionsToTeacher(Long id) {
    final var teacher = teacherService.getOne(id);
    return buildSectionListResponse(teacher.getSections());
  }

  @Transactional
  public Set<GenericSectionResponse> getAllConnectedSectionsToStudent(User userInfo) {
    final var student = studentService.findByUserInfo(userInfo);
    return Set.of(transformSectionToGenericSectionResponse(student.getSection()));
  }

  private GenericSectionResponse transformSectionToGenericSectionResponse(Section section) {
    return GenericSectionResponse.builder()
        .createdAt(section.getCreatedAt())
        .updatedAt(section.getUpdatedAt())
        .deletedAt(section.getDeletedAt())
        .gradeId(section.getGradeId())
        .id(section.getId())
        .name(section.getName())
        .organization(section.getOrganization())
        .remarks(section.getRemarks())
        .uuid(section.getUuid())
        .status(section.getStatus())
        .build();
  }

  public Set<ConnectedStudent> getAllConnectedStudents(User userInfo) {
    final var teacher = teacherService.findByUserInfo(userInfo);
    return getAllConnectedStudents(teacher.getSections());
  }

  public Set<ConnectedStudent> getAllConnectedStudents(String sectionUuid) {
    final var section = findByUuid(sectionUuid);
    final Set<Section> sections = new HashSet<>();
    sections.add(section);
    return getAllConnectedStudents(sections);
  }

  public Set<ConnectedStudent> getAllConnectedStudents(List<String> sectionUuids) {
    List<UUID> uuidList = new ArrayList<>();
    sectionUuids.forEach(uuid -> uuidList.add(UUID.fromString(uuid)));
    var sectionStudents = new HashSet<>(sectionRepository.findStudentsInSectionList(uuidList));
    return getAllConnectedStudents(sectionStudents);
  }

  private Set<ConnectedStudent> getAllConnectedStudents(Set<Section> sections) {
    final Set<Student> connectedStudents = studentService.getStudentsBySections(sections);
    var activeStudents =
        connectedStudents.stream()
            .filter(cs -> cs.getUserInfo().getDeletedAt() == null)
            .collect(Collectors.toSet());
    return activeStudents.stream()
        .map(
            student ->
                ConnectedStudent.builder()
                    .id(student.getId())
                    .firstName(
                        Objects.nonNull(student.getUserInfo())
                            ? buildName(student.getUserInfo().getFirstName())
                            : "")
                    .lastName(
                        Objects.nonNull(student.getUserInfo())
                            ? buildName(student.getUserInfo().getLastName())
                            : "")
                    .rollNumber(student.getRollNumber())
                    .emailId(student.getUserInfo().getEmail())
                    .mobileNumber(student.getUserInfo().getMobileNumber())
                    .userId(student.getUserInfo().getId())
                    .classId(student.getClassId())
                    .authUserId(student.getUserInfo().getAuthUserId())
                    .sectionUuid(Set.of(student.getSection().getUuid().toString()))
                    .grade(student.getSection().getGradeName())
                    .classRollNumber(
                        student.getClassRollNumber() == null ? "-" : student.getClassRollNumber())
                    .build())
        .sorted(
            Comparator.comparing(
                student ->
                    StringUtils.isNumeric(student.getClassRollNumber())
                        ? Long.valueOf(student.getClassRollNumber())
                        : null,
                Comparator.nullsLast(Comparator.naturalOrder())))
        .collect(Collectors.toCollection(LinkedHashSet::new));
  }

  public String buildName(String name) {
    if (name == null || name.isEmpty()) {
      return "";
    }
    return name.toUpperCase();
  }

  public void removeTeacherFromSection(Long teacherId, String sectionUuid) {
    final var teacher = teacherService.getOne(teacherId);
    final var sectionToRemove = findByUuid(sectionUuid);
    final var teacherSection =
        teacherSectionService.findByTeacherAndSection(teacher, sectionToRemove);
    setUpdatedAtAndDeletedAt(teacherSection);
    teacherSectionService.save(teacherSection);
  }

  public Section addStudentToSection(Long studentId, String sectionUuid) {
    final var student = studentService.getStudentById(studentId);

    final var sectionToConnect = findByUuid(sectionUuid);

    if (!student.getUserInfo().getOrganization().equals(sectionToConnect.getOrganization())
        || (sectionToConnect.getGradeId() != null
            && sectionToConnect.getGradeId() != -1
            && !sectionToConnect.getGradeId().equals(student.getClassId()))) {
      throw new ApiException(InternalErrorCodes.UN_AUTHORIZED, ERROR_UNAUTHORIZED);
    }

    student.setSection(sectionToConnect);
    studentRepository.save(student);
    return sectionToConnect;
  }

  public Set<ConnectedRetailTeacher> getAllConnectedTeachers(User userInfo) {
    final var student = studentService.findByUserInfo(userInfo);
    Set<ConnectedTeacher> allConnectedTeachers =
        getAllConnectedTeachers(Set.of(student.getSection()));
    return allConnectedTeachers.stream()
        .map(this::transformConnectedTeacherToRetailTeacher)
        .collect(Collectors.toSet());
  }

  public Set<ConnectedTeacher> getAllConnectedTeachers(String sectionUuid) {
    final var section = findByUuid(sectionUuid);
    final Set<Section> sections = new HashSet<>();
    sections.add(section);
    return getAllConnectedTeachers(sections);
  }

  private Set<ConnectedTeacher> getAllConnectedTeachers(Set<Section> sections) {
    final Set<Teacher> connectedTeachers = teacherService.getTeachersBySections(sections);
    return connectedTeachers.stream()
        .map(
            teacher ->
                ConnectedTeacher.builder()
                    .id(teacher.getId())
                    .firstName(teacher.getUserInfo().getFirstName())
                    .lastName(teacher.getUserInfo().getLastName())
                    .emailId(teacher.getUserInfo().getEmail())
                    .mobileNumber(teacher.getUserInfo().getMobileNumber())
                    .userId(teacher.getUserInfo().getId())
                    .teacherCode(teacher.getTeacherCode())
                    .build())
        .collect(Collectors.toSet());
  }

  public Section findByUuid(final String sectionUuid) {
    final Optional<Section> sectionOptional =
        sectionRepository.findByUuid(UUID.fromString(sectionUuid));
    if (sectionOptional.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.InvalidSection", new String[] {sectionUuid});
    }
    return sectionOptional.get();
  }

  public List<SectionEntityDto.Response> getAllSections(
      final String organization, boolean showInactiveSections) {
    if (showInactiveSections) {
      return buildSectionListResponse(
          sectionRepository.findAllByOrganizationAndDeletedAtIsNullOrderByName(organization));
    }
    return buildSectionListResponse(
        sectionRepository.findAllByOrganizationAndDeletedAtIsNullAndStatusOrderByName(
            organization, SectionStatus.ACTIVE));
  }

  public SectionResponse getSection(String organization, String sectionUuid) {
    final var section = findByUuid(sectionUuid);
    if (!section.getOrganization().equals(organization)) {
      throw new ApiException(InternalErrorCodes.UN_AUTHORIZED, ERROR_UNAUTHORIZED);
    }

    Set<ConnectedTeacher> allConnectedTeachers = getAllConnectedTeachers(sectionUuid);

    List<ConnectedTeacher> connectedTeachers = new ArrayList<>(allConnectedTeachers);

    return SectionResponse.builder()
        .name(section.getName())
        .status(section.getStatus())
        .id(section.getId())
        .uuid(section.getUuid())
        .grade(section.getGradeId())
        .board(section.getBoardSlug())
        .teachers(connectedTeachers)
        .build();
  }

  public List<SectionResponse> getSectionsOfGrade(String org, int gradeId, String board) {
    var grade = strapiService.getGradeById(gradeId);
    var sections =
        sectionRepository.findAllByOrganizationAndBoardSlugAndGradeSlugAndStatusOrderByName(
            org, board, grade.getSlug(), SectionStatus.ACTIVE);

    var sectionResponses = new ArrayList<SectionResponse>();
    sections.forEach(
        section -> {
          var sectionResponse = new SectionResponse();
          BeanUtils.copyProperties(section, sectionResponse);
          sectionResponse.setGrade(section.getGradeId());
          sectionResponse.setBoard(section.getBoardSlug());
          sectionResponses.add(sectionResponse);
        });

    return sectionResponses;
  }

  public SectionResponse editSection(
      String org, String sectionUuid, SectionCreateRequest sectionRequest) {
    final var section = findByUuid(sectionUuid);
    if (!section.getOrganization().equals(org)) {
      throw new ApiException(InternalErrorCodes.UN_AUTHORIZED, ERROR_UNAUTHORIZED);
    }

    var gradeId = section.getGradeId();
    var gradeName = section.getGradeName();
    var gradeSlug = section.getGradeSlug();
    if (!isEmpty(sectionRequest.getGradeSlug())) {
      final Grade grade = contentService.getGradeBySlug(sectionRequest.getGradeSlug());
      gradeId = grade.getId();
      gradeName = grade.getName();
      gradeSlug = grade.getSlug();
    }
    final Entity board = strapiService.getEduBoardBySlug(sectionRequest.getBoardSlug());
    section.setGradeId(gradeId);
    section.setGradeName(gradeName);
    section.setGradeSlug(gradeSlug);
    section.setBoardName(board.getName());
    section.setBoardSlug(board.getSlug());
    section.setStatus(sectionRequest.getStatus());
    section.setName(sectionRequest.getName());
    var updatedSection = sectionRepository.save(section);

    Set<ConnectedTeacher> allConnectedTeachers = getAllConnectedTeachers(sectionUuid);

    allConnectedTeachers.forEach(
        teacher -> {
          if (isTeacherRemoved(teacher, sectionRequest.getTeachers())) {
            removeTeacherFromSection(teacher.getId(), sectionUuid);
          }
        });

    if (nonNull(sectionRequest.getTeachers())) {
      sectionRequest
          .getTeachers()
          .forEach(
              teacher -> {
                if (isTeacherNewlyAdded(teacher, allConnectedTeachers)) {
                  addTeacherToSection(teacher.getId(), updatedSection);
                }
              });
    }

    return getSection(org, sectionUuid);
  }

  private boolean isTeacherNewlyAdded(
      ConnectedTeacher teacher, Set<ConnectedTeacher> connectedTeachers) {
    if (!connectedTeachers.isEmpty()) {
      return connectedTeachers.stream()
          .noneMatch(connectedTeacher -> connectedTeacher.getId() == teacher.getId());
    }
    return true;
  }

  private boolean isTeacherRemoved(ConnectedTeacher teacher, List<ConnectedTeacher> teachers) {
    if (nonNull(teachers) && !isEmpty(teachers)) {
      return teachers.stream()
          .noneMatch(connectedTeacher -> connectedTeacher.getId() == teacher.getId());
    }
    return true;
  }

  public void addTeachersToSection(List<ConnectedTeacher> teachers, Section section) {
    if (nonNull(teachers)) {
      teachers.forEach(teacher -> addTeacherToSection(teacher.getId(), section));
    }
  }

  public void removeSection(String organization, String sectionUuid) {
    final var section = findByUuid(sectionUuid);
    if (!section.getOrganization().equals(organization)
        && !organization.equals(Constants.WEXL_INTERNAL)) {
      throw new ApiException(InternalErrorCodes.UN_AUTHORIZED, ERROR_UNAUTHORIZED);
    }
    List<Student> students = studentRepository.getStudentsBySection(section);
    if (Objects.nonNull(students) && !students.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.StudentsRemove.Section",
          new String[] {section.getName()});
    }
    setUpdatedAtAndDeletedAt(section);
    section.setStatus(SectionStatus.INACTIVE);
    sectionRepository.save(section);
  }

  private void setUpdatedAtAndDeletedAt(Model model) {
    final var timestamp = Timestamp.valueOf(LocalDateTime.now());
    final var date = new Date(timestamp.getTime());
    model.setUpdatedAt(timestamp);
    model.setDeletedAt(date);
  }

  public List<ConnectedTeacher> searchTeacher(@Valid String query, User student) {
    Set<ConnectedRetailTeacher> studentConnectedTeachers = getAllConnectedTeachers(student);
    List<String> alreadyConnectedTeacherCodes =
        studentConnectedTeachers.stream()
            .map(ConnectedTeacher::getTeacherCode)
            .collect(Collectors.toList());
    alreadyConnectedTeacherCodes.add("");
    List<User> matchedTeachers =
        userRepository.getTeacherBySearchTerm(query, alreadyConnectedTeacherCodes);

    return matchedTeachers.stream()
        .map(
            teacher -> {
              var defaultSection = "";
              List<SectionEntityDto.Response> allConnectedSectionsToTeacher =
                  getAllConnectedSectionsToTeacher(teacher);

              if (!allConnectedSectionsToTeacher.isEmpty()) {
                defaultSection = allConnectedSectionsToTeacher.getFirst().uuid().toString();
              }
              return ConnectedRetailTeacher.teacherBuilder()
                  .emailId(teacher.getEmail())
                  .firstName(teacher.getFirstName())
                  .lastName(teacher.getLastName())
                  .userId(teacher.getId())
                  .mobileNumber(teacher.getMobileNumber())
                  .teacherCode(
                      teacher.getTeacherInfo() != null
                          ? teacher.getTeacherInfo().getTeacherCode()
                          : null)
                  .defaultSectionUuid(defaultSection)
                  .build();
            })
        .collect(Collectors.toList());
  }

  public ConnectedRetailTeacher transformConnectedTeacherToRetailTeacher(ConnectedTeacher teacher) {
    var defaultSection = "";
    List<SectionEntityDto.Response> allConnectedSectionsToTeacher =
        getAllConnectedSectionsToTeacher(teacher.getId());
    if (!allConnectedSectionsToTeacher.isEmpty()) {
      defaultSection = allConnectedSectionsToTeacher.getFirst().uuid().toString();
    }
    return ConnectedRetailTeacher.teacherBuilder()
        .emailId(teacher.getEmailId())
        .firstName(teacher.getFirstName())
        .lastName(teacher.getLastName())
        .userId(teacher.getId())
        .mobileNumber(teacher.getMobileNumber())
        .teacherCode(teacher.getTeacherCode())
        .defaultSectionUuid(defaultSection)
        .build();
  }

  public Section getSectionByNameAndOrg(String name, String orgSlug) {
    List<Section> sections = sectionRepository.findTop1ByOrganizationAndName(orgSlug, name);
    if (sections.isEmpty()) {
      return null;
    }
    return sections.getFirst();
  }

  public List<String> getSectionUuidByGrade(final String organization, final String gradeSlug) {
    final Grade grade = contentService.getGradeBySlug(gradeSlug);
    int gradeId = grade.getId();
    Set<Section> sections =
        sectionRepository.getSectionsByOrganizationAndGradeId(organization, gradeId);
    return sections.stream().map(section -> section.getUuid().toString()).toList();
  }

  public List<SectionEntityDto.Response> getSectionsByGrade(
      final String organization, final String gradeSlug) {
    final Grade grade = contentService.getGradeBySlug(gradeSlug);
    int gradeId = grade.getId();

    return buildSectionListResponse(
        sectionRepository.getSectionsByOrganizationAndGradeId(organization, gradeId));
  }

  private List<SectionEntityDto.Response> buildSectionListResponse(Set<Section> sectionList) {
    var sectionIds = sectionList.stream().map(Section::getId).collect(Collectors.toList());
    var sectionStudentResponses = sectionRepository.getStudentCountsForSections(sectionIds);

    Map<Long, Long> responseMap =
        sectionStudentResponses.stream()
            .collect(
                Collectors.toMap(
                    SectionStudentResponse::getSectionId, SectionStudentResponse::getStudentCount));

    return sectionList.stream()
        .sorted(Comparator.comparing(Section::getName))
        .map(
            section ->
                SectionEntityDto.Response.builder()
                    .id(section.getId())
                    .name(section.getName())
                    .uuid(section.getUuid())
                    .gradeId(section.getGradeId())
                    .status(section.getStatus())
                    .organization(section.getOrganization())
                    .gradeName(section.getGradeName())
                    .gradeSlug(section.getGradeSlug())
                    .boardName(section.getBoardName())
                    .boardSlug(section.getBoardSlug())
                    .remarks(section.getRemarks())
                    .createdAt(section.getCreatedAt())
                    .deletedAt(section.getDeletedAt())
                    .updatedAt(section.getUpdatedAt())
                    .studentCount(responseMap.getOrDefault(section.getId(), 0L))
                    .build())
        .toList();
  }

  public List<SectionResponse> getSectionsOfChildOrgsByGrade(
      String gradeSlug, List<String> requestedChildOrgs) {

    User user = authService.getUserDetails();
    List<Organization> organizations = teacherOrgsService.getChildOrgs(user.getAuthUserId());
    List<String> childOrgs = organizations.stream().map(Organization::getSlug).toList();

    if (!new HashSet<>(childOrgs).containsAll(requestedChildOrgs)) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Sections.Retreival");
    }

    if (Objects.nonNull(gradeSlug)) {
      List<SectionResponseQueryResult> sections =
          sectionRepository.getSectionsOfChildOrgsByGrade(gradeSlug, requestedChildOrgs);
      return buildSectionResponse(sections);
    }
    return buildSectionResponse(sectionRepository.getSectionsOfChildOrgs(requestedChildOrgs));
  }

  private List<SectionResponse> buildSectionResponse(List<SectionResponseQueryResult> sections) {

    return sections.stream()
        .map(
            section ->
                SectionResponse.builder()
                    .orgName(section.getOrganizationName())
                    .orgSlug(section.getOrganizationSlug())
                    .name(section.getSectionName())
                    .uuid(UUID.fromString(section.getSectionUuid()))
                    .id(section.getSectionId())
                    .build())
        .toList();
  }

  public List<com.wexl.retail.model.Grade> getGradesByOrgSlug(String orgSlug) {
    var organization = organizationRepository.findBySlug(orgSlug);
    if (Objects.isNull(organization) || Objects.nonNull(organization.getDeletedAt())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.OrganizationNotFound");
    }
    if (Boolean.FALSE.equals(organization.getSelfSignup())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.Invalid.GradesByOrg");
    }
    List<String> gradeSlugs =
        organization.getCurriculum().getBoards().stream()
            .map(CurriculumBoard::getGrades)
            .flatMap(Collection::stream)
            .map(CurriculumGrade::getSlug)
            .toList();
    List<Grade> allGrades = strapiService.getAllGrades();
    List<Grade> grades =
        allGrades.stream()
            .filter(grade -> gradeSlugs.contains(grade.getSlug()))
            .distinct()
            .toList();
    return buildGrades(orgSlug, grades);
  }

  private List<com.wexl.retail.model.Grade> buildGrades(String orgSlug, List<Grade> gradeEntities) {
    return gradeEntities.isEmpty()
        ? Collections.emptyList()
        : gradeEntities.stream()
            .map(
                grade -> {
                  var sections = sectionRepository.getSectionsOfGrade(orgSlug, grade.getId());
                  return com.wexl.retail.model.Grade.builder()
                      .id(grade.getId())
                      .slug(grade.getSlug())
                      .name(grade.getName())
                      .orderId(grade.getOrder())
                      .sections(buildSectionToSectioDto(sections))
                      .build();
                })
            .sorted(Comparator.comparing(com.wexl.retail.model.Grade::getOrderId))
            .toList();
  }

  public List<SectionDto> buildSectionToSectioDto(List<Section> sections) {
    List<SectionDto> sectionDtos = new ArrayList<>();
    sections.forEach(
        section ->
            sectionDtos.add(
                SectionDto.builder()
                    .id(section.getId())
                    .name(section.getName())
                    .uuid(section.getUuid())
                    .build()));
    return sectionDtos;
  }

  public Section findSectionById(Long sectionId) {
    Optional<Section> optionalSection = sectionRepository.findById(sectionId);
    if (optionalSection.isEmpty()) {
      throw new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "Invalid SectionId");
    }
    return optionalSection.get();
  }

  public List<Section> getSectionsByGradeIdAndOrg(
      int gradeId, String orgSlug, boolean throwIfEmpty) {
    var sections = sectionRepository.getSectionsOfGrade(orgSlug, gradeId);
    if (sections.isEmpty() && throwIfEmpty) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.SectionFind.Grade");
    }
    return sections;
  }

  public void createSectionsForAmbassdor(String orgSlug) {
    var organization = strapiService.getOrganizationBySlug(orgSlug);
    var grades = organization.getSettings().getBoards().get(0).getGrades();
    List<Section> sectionList = new ArrayList<>();
    grades.forEach(
        grade -> {
          final Grade gradeData = contentService.getGradeBySlug(grade.getSlug());
          sectionList.add(
              Section.builder()
                  .name(getSectionName(grade.getSlug()))
                  .gradeName(getGradeName(grade.getSlug()))
                  .gradeSlug(grade.getSlug())
                  .gradeId(gradeData.getId())
                  .status(SectionStatus.ACTIVE)
                  .uuid(UUID.randomUUID())
                  .organization(orgSlug)
                  .build());
        });

    sectionRepository.saveAll(sectionList);
  }

  private String getGradeName(String gradeSlug) {
    return switch (gradeSlug) {
      case "i" -> "First Grade - I";
      case "ii" -> "Second Grade - II";
      case "iii" -> "Third Grade - III";
      case "iv" -> "Fourth Grade - IV";
      case "v" -> "Fifth Grade - V";
      case "vi" -> "Sixth Grade - VI";
      case "vii" -> "Seventh Grade - VII";
      case "viii" -> "Eight Grade - VIII";
      case "ix" -> "Nineth Grade - IX";
      case "x" -> "Tenth Grade - X";
      default -> null;
    };
  }

  private String getSectionName(String gradeSlug) {
    return switch (gradeSlug) {
      case "i" -> "1A";
      case "ii" -> "2A";
      case "iii" -> "3A";
      case "iv" -> "4A";
      case "v" -> "5A";
      case "vi" -> "6A";
      case "vii" -> "7A";
      case "viii" -> "8A";
      case "ix" -> "9A";
      case "x" -> "10A";
      default -> null;
    };
  }

  public List<SectionEntityDto.Response> getAllSectionsByOrg(String organization) {
    return buildSectionListClassroomResponse(
        sectionRepository.getSectionsByOrganizationAndDeletedAtIsNullOrderByName(organization));
  }

  private List<SectionEntityDto.Response> buildSectionListClassroomResponse(
      List<Section> sectionList) {
    return sectionList.stream()
        .filter(section -> sectionRepository.getStudentCountOfSection(section.getId()) != 0)
        .map(
            section -> {
              var studentCount = sectionRepository.getStudentCountOfSection(section.getId());
              return SectionEntityDto.Response.builder()
                  .id(section.getId())
                  .name(section.getName())
                  .studentCount(studentCount)
                  .uuid(section.getUuid())
                  .gradeId(section.getGradeId())
                  .status(section.getStatus())
                  .organization(section.getOrganization())
                  .gradeName(section.getGradeName())
                  .gradeSlug(section.getGradeSlug())
                  .boardSlug(section.getBoardSlug())
                  .boardName(section.getBoardName())
                  .remarks(section.getRemarks())
                  .createdAt(section.getCreatedAt())
                  .deletedAt(section.getDeletedAt())
                  .updatedAt(section.getUpdatedAt())
                  .build();
            })
        .sorted(Comparator.comparing(SectionEntityDto.Response::name))
        .toList();
  }

  public void updateBoardForSections() {
    List<Section> sections = sectionRepository.findAllByBoardSlugIsNull();
    var allBoards = strapiService.getAllBoards();

    Map<Object, Entity> data = new HashMap<>();
    allBoards.forEach(board -> data.put(board.getId(), board));

    sections.forEach(
        section -> {
          try {
            var student = studentRepository.findTop1BySection(section);

            if (student.isPresent()) {
              var boardId = student.get().getBoardId();
              var boardDetails = data.get(boardId);
              if (boardDetails != null) {
                section.setBoardName(boardDetails.getAssetName());
                section.setBoardSlug(boardDetails.getSlug());
                sectionRepository.save(section);
              }
            }
          } catch (Exception e) {
            log.error(
                "Failed to update section with ID {}: {}", section.getId(), e.getMessage(), e);
          }
        });
  }

  public List<SectionEntityDto.Response> getSectionsByBoardAndGrade(
      String org, String gradeSlug, String boardSlug) {
    var sections =
        sectionRepository.findAllByOrganizationAndBoardSlugAndGradeSlugAndStatusOrderByName(
            org, boardSlug, gradeSlug, SectionStatus.ACTIVE);
    return buildSectionListResponse(new HashSet<>(sections));
  }

  public List<SectionEntityDto.Response> getSectionsAndGradeByTeacher(
      String teacherId, String gradeSlug, String board) {
    var user = userRepository.findByAuthUserId(teacherId);
    var teacher =
        teacherRepository
            .findByUserInfo(user.get())
            .orElseThrow(
                () ->
                    new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.TeacherNotFound"));
    return buildSectionListClassroomResponse(
        teacher.getSections().stream()
            .filter(
                section ->
                    section.getGradeSlug().equals(gradeSlug)
                        && section.getBoardSlug().equalsIgnoreCase(board))
            .toList());
  }
}
