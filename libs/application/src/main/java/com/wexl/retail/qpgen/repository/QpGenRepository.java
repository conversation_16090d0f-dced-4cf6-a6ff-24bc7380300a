package com.wexl.retail.qpgen.repository;

import com.wexl.retail.qpgen.model.QpGen;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface QpGenRepository extends JpaRepository<QpGen, Long> {
  List<QpGen> findByOrgSlugOrderByIdDesc(String slug);

  @Query(
      value = "SELECT qg.* FROM qp_gen qg WHERE :testDefinationId  = ANY(qg.test_definition_id)",
      nativeQuery = true)
  Optional<QpGen> getDetailsByTestDefinationId(Long testDefinationId);
}
