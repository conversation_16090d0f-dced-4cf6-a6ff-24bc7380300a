package com.wexl.retail.student.registration.service;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.ecommerce.ProductDto.Customer;
import com.wexl.retail.email.EmailService;
import com.wexl.retail.model.Gender;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.msg91.dto.Msg91Dto;
import com.wexl.retail.msg91.service.Msg91SmsService;
import com.wexl.retail.organization.admin.StudentRequest;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.student.auth.StudentAuthService;
import com.wexl.retail.student.registration.dto.StudentRegistrationRequest;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class CommerceCustomerSignup implements DefaultStudentSignup {

  private final StudentRepository studentRepository;
  private final StudentAuthService studentAuthService;
  private final AuthService authService;
  private final UserRepository userRepository;
  private final OrganizationRepository organizationRepository;
  private final EmailService emailService;
  private final Msg91SmsService msg91SmsService;

  @Value("${app.ecommerce.student.board}")
  private String defaultBoard;

  @Value("${app.ecommerce.student.grade}")
  private String defaultGrade;

  @Value("${app.ecommerce.student.section}")
  private String defaultSection;

  @Override
  public String createStudent(
      String orgSlug, StudentRegistrationRequest studentRegistrationRequest) {
    var existingUser = userRepository.findByAuthUserId(studentRegistrationRequest.getPhoneNumber());
    if (existingUser.isEmpty()) {
      var studentSignupRequest = mapStudentSignupRequest(studentRegistrationRequest);
      var organization = organizationRepository.findBySlug(orgSlug);
      var response = studentAuthService.createOrgStudent(studentSignupRequest, orgSlug);
      var user = authService.getUserByAuthUserId(response.getUserName());
      var student = studentRepository.findByUserId(user.getId());
      student.setExtRef(Long.valueOf(studentRegistrationRequest.getExtRef()));
      // The datatype of extRef in database is Long, but the value is being set as String.
      // This is a bug will be changed later.
      studentRepository.save(student);
      if (Boolean.TRUE.equals(organization.getSendSms())) {
        var recipient = buildRecipient(studentRegistrationRequest, studentSignupRequest, orgSlug);
        log.info("SMS Sent to user '{}' successfully", recipient);
        msg91SmsService.sendBulkMessage("6607df9fd6fc057c20186b82", recipient);
      }
      emailService.sendCommerceStudentSignupEmail(
          studentSignupRequest.getFirstName(),
          studentSignupRequest.getEmail(),
          studentSignupRequest.getPassword(),
          studentSignupRequest.getUserName(),
          studentRegistrationRequest.getAndroidAppUrl(),
          studentRegistrationRequest.getWebAppUrl());
      return user.getAuthUserId();
    }
    return existingUser.get().getAuthUserId();
  }

  public List<Msg91Dto.Recipient> buildRecipient(
      StudentRegistrationRequest studentRegistrationRequest,
      StudentRequest studentSignupRequest,
      String orgSlug) {
    List<Msg91Dto.Recipient> recipients = new ArrayList<>();
    var org = organizationRepository.findBySlug(orgSlug);
    recipients.add(
        Msg91Dto.Recipient.builder()
            .orgname(org.getName())
            .name(studentSignupRequest.getUserName())
            .password(studentSignupRequest.getPassword())
            .mobiles(studentSignupRequest.getMobileNumber())
            .weblink(studentRegistrationRequest.getWebAppUrl())
            .applink(studentRegistrationRequest.getAndroidAppUrl())
            .build());

    return new ArrayList<>(recipients);
  }

  private StudentRequest mapStudentSignupRequest(StudentRegistrationRequest customer) {
    var userName = generateUserName(customer.getPhoneNumber());
    StudentRequest student = new StudentRequest();
    student.setFirstName(customer.getFullName());
    student.setLastName("");
    student.setUserName(userName);
    student.setEmail(customer.getEmail());
    student.setSchoolName("Commerce School");
    student.setMobileNumber(customer.getPhoneNumber());
    student.setAcademicYearSlug("23-24");
    student.setGender(Gender.MALE);
    student.setGradeSlug(defaultGrade);
    student.setBoardSlug(defaultBoard);
    student.setSection(defaultSection);
    student.setParentFirstName("");
    student.setParentEmail("");
    student.setParentMobileNumber("");
    student.setPassword(generateRandomCharacters(8));
    return student;
  }

  private String generateUserName(String phone) {
    var user = userRepository.findUsersByMobileNumber(phone);
    if (user.isEmpty()) {
      return phone;
    }
    if (phone.startsWith("+91") && phone.length() == 13) {
      phone = phone.substring(3);
    }
    return generateRandomCharacters(4).concat(phone);
  }

  public Optional<Student> getStudentByCustomerDetails(String orgSlug, Customer customer) {
    if (StringUtils.isBlank(customer.email()) || ObjectUtils.isEmpty(customer.id())) {
      return Optional.empty();
    }
    var student = studentRepository.findByExtRef(customer.id());
    if (student != null) {
      return Optional.of(student);
    }

    var possibleUser = userRepository.findByEmailAndOrganization(customer.email(), orgSlug);
    return possibleUser.map(User::getStudentInfo);
  }

  public void updateStudentExtRefIfRequired(Student student, Long externalCustomRef) {
    if (Objects.equals(student.getExtRef(), externalCustomRef)) {
      return;
    }
    student.setExtRef(externalCustomRef);
    studentRepository.save(student);
  }
}
