package com.wexl.retail.schedules.domain;

import com.wexl.retail.model.Model;
import com.wexl.retail.section.domain.Section;
import jakarta.persistence.*;
import java.sql.Timestamp;
import java.time.DayOfWeek;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Entity
@Data
@Table(name = "section_schedule")
@EqualsAndHashCode(callSuper = true)
public class SectionSchedule extends Model {
  @Id
  @GeneratedValue(
      strategy = GenerationType.SEQUENCE,
      generator = "section-schedule-sequence-generator")
  @SequenceGenerator(
      name = "section-schedule-sequence-generator",
      sequenceName = "section_schedule_seq",
      allocationSize = 1)
  private long id;

  private String url;
  private String name;

  @ManyToOne
  @JoinColumn(name = "section_id")
  private Section section;

  @Enumerated(EnumType.STRING)
  private DayOfWeek dayOfWeek;

  @Enumerated(EnumType.STRING)
  private MeetingType meetingType;

  @Column(name = "to_time")
  private Timestamp toTime;

  @Column(name = "from_time")
  private Timestamp fromTime;
}
