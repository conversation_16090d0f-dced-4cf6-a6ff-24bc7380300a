package com.wexl.retail.mlp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.sql.Timestamp;
import java.util.List;
import lombok.Builder;
import lombok.NonNull;

public record GroupMlpDto() {
  @Builder
  public record GroupMlpRequest(
      @JsonProperty("title") String title,
      @NonNull @JsonProperty("org_slugs") List<String> organizationSlug,
      @NonNull @JsonProperty("board_slug") String boardSlug,
      @NonNull @JsonProperty("grade_slug") String gradeSlug,
      @NonNull @JsonProperty("subject_slug") String subjectSlug,
      @NonNull @JsonProperty("chapter_slug") String chapterSlug,
      @JsonProperty("subtopic_slug") String subtopicSlug,
      @JsonProperty("video_slug") String videoSlug,
      @JsonProperty("sha_link") String shaLink,
      @JsonProperty("video_source") String videoSource,
      @JsonProperty("synopsis_slug") String synopsisSlug,
      @JsonProperty("description") String description,
      @JsonProperty("question_count") Integer questionCount,
      @JsonProperty("question_uuids") List<String> questionUuids) {}

  @Builder
  public record GroupMlpResponse(
      long mlpId, int duration, Timestamp startDate, long endDate, String status) {}
}
