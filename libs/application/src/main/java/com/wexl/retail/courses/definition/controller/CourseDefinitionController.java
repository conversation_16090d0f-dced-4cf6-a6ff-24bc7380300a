package com.wexl.retail.courses.definition.controller;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.courses.definition.dto.CourseDefinitionRequest;
import com.wexl.retail.courses.definition.dto.CourseDefinitionResponse;
import com.wexl.retail.courses.definition.service.CourseDefinitionService;
import io.jsonwebtoken.lang.Collections;
import jakarta.validation.Valid;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}/courses")
public class CourseDefinitionController {

  private final CourseDefinitionService courseDefinitionService;

  @PostMapping
  public CourseDefinitionResponse createCourseDefinition(
      @PathVariable String orgSlug,
      @Valid @RequestBody CourseDefinitionRequest courseDefinitionRequest) {
    try {
      associateCurrentOrgToCourseDef(orgSlug, courseDefinitionRequest);
      return courseDefinitionService.createCourseDefinition(courseDefinitionRequest);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  private void associateCurrentOrgToCourseDef(String orgSlug, CourseDefinitionRequest request) {
    if (Collections.isEmpty(request.getOrgAssociations())) {
      request.setOrgAssociations(List.of(orgSlug));
    }
  }

  @PostMapping("/{courseDefId}:publish")
  public CourseDefinitionResponse publishCourseDefinitionById(@PathVariable long courseDefId) {
    try {
      return courseDefinitionService.publishCourseDefinitionById(courseDefId);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  @PostMapping("/{courseDefId}:clone")
  public CourseDefinitionResponse cloneCourseDefinitionById(
      @PathVariable long courseDefId, @PathVariable String orgSlug) {
    try {
      return courseDefinitionService.cloneCourseDefinitionById(courseDefId, List.of(orgSlug));
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  @GetMapping("/{courseDefId}")
  public CourseDefinitionResponse getCourseDefinitionById(
      @PathVariable long courseDefId, @RequestParam(required = false) boolean preview) {
    try {
      return courseDefinitionService.getCourseDefinitionById(courseDefId, preview);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  @PutMapping("/{courseDefId}")
  public CourseDefinitionResponse updateCourseDefinitionById(
      @PathVariable long courseDefId,
      @PathVariable String orgSlug,
      @Valid @RequestBody CourseDefinitionRequest courseDefinitionRequest) {
    try {
      associateCurrentOrgToCourseDef(orgSlug, courseDefinitionRequest);
      return courseDefinitionService.updateCourseDefinitionById(
          courseDefinitionRequest, courseDefId);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  @GetMapping
  public List<CourseDefinitionResponse> getAllCourseDefinitions(
      @RequestParam(value = "is_active", required = false) boolean isActive,
      @RequestParam(value = "course_category", required = false) Long categoryId) {
    try {
      return courseDefinitionService.getAllCourseDefinitions(isActive, categoryId);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  @DeleteMapping("/{courseDefId}")
  public CourseDefinitionResponse deleteCourseDefinitionById(@PathVariable long courseDefId) {
    try {
      return courseDefinitionService.deleteCourseDefinitionById(courseDefId);
    } catch (Exception exception) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, exception.getMessage(), exception);
    }
  }
}
