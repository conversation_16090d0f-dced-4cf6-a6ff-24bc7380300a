package com.wexl.retail.courses.enrollment.repository;

import com.wexl.retail.courses.definition.model.CourseDefinition;
import com.wexl.retail.courses.enrollment.model.CourseSchedule;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface CourseScheduleRepository extends JpaRepository<CourseSchedule, Long> {

  long countByOrgSlug(String orgSlug);

  List<CourseSchedule> findByCourseDefinition(CourseDefinition courseDefinition);

  List<CourseSchedule> findByOrgSlug(String orgSlug);
}
