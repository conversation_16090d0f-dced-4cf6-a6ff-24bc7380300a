package com.wexl.retail.staff.controller;

import com.wexl.retail.staff.dto.StaffDto;
import com.wexl.retail.staff.service.DepartmentService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}/departments")
@RequiredArgsConstructor
@Slf4j
public class DepartmentController {
  private final DepartmentService departmentService;

  @PostMapping()
  public ResponseEntity<StaffDto.DepartmentResponse> createDepartment(
      @RequestBody StaffDto.DepartmentRequest request, @PathVariable String orgSlug) {
    log.info("Creating new department: {}", request.name());
    return new ResponseEntity<>(
        departmentService.createDepartment(request, orgSlug), HttpStatus.CREATED);
  }

  @GetMapping("/{id}")
  public ResponseEntity<StaffDto.DepartmentResponse> getDepartmentById(
      @PathVariable Long id, @PathVariable String orgSlug) {
    log.info("Fetching department with id: {}", id);
    return ResponseEntity.ok(departmentService.getDepartmentById(id, orgSlug));
  }

  @GetMapping()
  public ResponseEntity<List<StaffDto.DepartmentResponse>> getAllDepartments() {
    log.info("Fetching all departments");
    return ResponseEntity.ok(departmentService.getAllDepartments());
  }

  @PutMapping("/{id}")
  public ResponseEntity<StaffDto.DepartmentResponse> updateDepartment(
      @PathVariable Long id, @RequestBody StaffDto.DepartmentRequest request) {
    log.info("Updating department with id: {}", id);
    return ResponseEntity.ok(departmentService.updateDepartment(id, request));
  }

  @DeleteMapping("/{id}")
  public ResponseEntity<Void> deleteDepartment(@PathVariable Long id) {
    log.info("Deleting department with id: {}", id);
    departmentService.deleteDepartment(id);
    return ResponseEntity.noContent().build();
  }
}
