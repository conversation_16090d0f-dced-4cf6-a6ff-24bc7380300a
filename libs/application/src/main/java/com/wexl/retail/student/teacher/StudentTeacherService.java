package com.wexl.retail.student.teacher;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.content.model.Entity;
import com.wexl.retail.model.Student;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.section.domain.TeacherSubjects;
import com.wexl.retail.section.repository.TeacherSubjectsRepository;
import com.wexl.retail.student.profile.ProfileService;
import com.wexl.retail.student.subject.profiles.repository.SubjectProfilesRepository;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.util.StrapiService;
import com.wexl.retail.util.ValidationUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RestController;

@Service
@RestController
@Slf4j
@RequiredArgsConstructor
public class StudentTeacherService {
  private final TeacherRepository teacherRepository;
  private final ProfileService profileService;
  private final ValidationUtils validation;
  private final StrapiService strapiService;
  private final TeacherSubjectsRepository teacherSubjectsRepository;
  private final SubjectProfilesRepository subjectProfilesRepository;
  private final UserService userService;

  public List<StudentTeacherDto.Response> getStudentTeacherDetails(String studentAuthId) {
    List<Long> studentList = new ArrayList<>();
    var user = validation.isValidUser(studentAuthId);
    var student = user.getStudentInfo().getId();
    studentList.add(student);
    return buildStudentTeacherResponse(user.getStudentInfo());
  }

  private List<StudentTeacherDto.Response> buildStudentTeacherResponse(Student student) {

    var teacherSubjectList = getTeacherSubjects(student);
    var teacherList =
        teacherSubjectList.stream()
            .map(TeacherSubjects::getTeacher)
            .filter(teacher -> teacher.getUserInfo().getIsDeleted() == null)
            .distinct()
            .toList();
    List<StudentTeacherDto.Response> response = new ArrayList<>();
    var strapiSubjects = strapiService.getAllSubjects();
    teacherList.forEach(
        teacher -> {
          var teacherUser = teacher.getUserInfo();
          var teacherSubjects =
              teacherSubjectList.stream()
                  .filter(teacherSubject -> teacherSubject.getTeacher().getId() == teacher.getId())
                  .map(TeacherSubjects::getSubject)
                  .toList();
          var subjectNames =
              buildSubjects(teacherSubjects, strapiSubjects).stream().map(Entity::getName).toList();
          var subjectSlugs =
              buildSubjects(teacherSubjects, strapiSubjects).stream().map(Entity::getSlug).toList();
          response.add(
              StudentTeacherDto.Response.builder()
                  .teacherId(teacher.getId())
                  .teacherName(teacherUser.getFirstName() + " " + teacherUser.getLastName())
                  .teacherAuthId(teacherUser.getAuthUserId())
                  .image(
                      Objects.isNull(teacherUser.getProfileImage())
                          ? null
                          : profileService.getProfileImageUrl(teacherUser.getProfileImage()))
                  .subjects(subjectNames)
                  .subjectSlugs(subjectSlugs)
                  .degree(teacher.getMetadata().getEducationQualification())
                  .build());
        });
    return response;
  }

  private List<TeacherSubjects> getTeacherSubjects(Student student) {
    List<String> subjectSlugs = new ArrayList<>();
    var studentSubjectProfileList =
        subjectProfilesRepository.getSubjectProfileOfStudent(student.getId());
    studentSubjectProfileList.forEach(
        subjectProfile ->
            subjectProfile
                .getSubjectProfileDetails()
                .forEach(subject -> subjectSlugs.add(subject.getSubjectSlug())));

    return teacherSubjectsRepository.getTeachersBySubjectsAndSection(
        subjectSlugs, student.getSection().getId());
  }

  private List<Entity> buildSubjects(List<String> teacherSubjects, List<Entity> strapiSubjects) {
    List<Entity> subjectEntities = new ArrayList<>();
    teacherSubjects.forEach(
        subject -> {
          var subjectData =
              strapiSubjects.stream().filter(x -> x.getSlug().equals(subject)).findAny();
          if (subjectData.isEmpty()) {
            throw new ApiException(
                InternalErrorCodes.INVALID_REQUEST,
                "error.InvalidSubjectSlug",
                new String[] {subject});
          }
          subjectEntities.add(subjectData.get());
        });
    return subjectEntities;
  }
}
