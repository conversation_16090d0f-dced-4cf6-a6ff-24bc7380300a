package com.wexl.retail.student.exam.competitive.processor;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.content.model.QuestionType;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.student.exam.competitive.dto.CompetitiveExamsDto;
import com.wexl.retail.test.school.domain.*;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.beans.factory.annotation.Autowired;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public abstract class AbstractCompetitiveExamProcessor implements CompetitiveExamProcessor {
  @Autowired protected AuthService authService;
  @Autowired protected SectionService sectionService;

  @Override
  public TestDefinition buildTestDefinition(CompetitiveExamsDto.Request request, String orgSlug) {
    TestDefinition testDefinition = new TestDefinition();
    testDefinition.setTestName(request.title());
    testDefinition.setCategory(request.testCategory());
    testDefinition.setOrganization(orgSlug);
    testDefinition.setType(TestType.MOCK_TEST);
    testDefinition.setTeacher(authService.getTeacherDetails());
    testDefinition.setTestDefinitionSections(
        buildTestDefinitionSections(testDefinition, request.answerKeys()));
    testDefinition.setQuestionPath(request.questionPaperRef());
    testDefinition.setSolutionPath(request.answersPaperRef());
    testDefinition.setActive(Boolean.TRUE);
    testDefinition.setGradeSlug(getGradeSlug(request.sections().getFirst()));
    testDefinition.setBoardSlug(
        sectionService.findByUuid(request.sections().getFirst()).getBoardSlug());
    testDefinition.setMetadata(TestDefinitionMetadata.builder().build());
    return testDefinition;
  }

  private String getGradeSlug(String uuid) {
    var section = sectionService.findByUuid(uuid);
    return section.getGradeSlug();
  }

  protected abstract List<TestDefinitionSection> buildTestDefinitionSections(
      TestDefinition testDefinition, List<CompetitiveExamsDto.AnswerKeys> answerKeys);

  public TestDefinitionSection buildSections(
      TestDefinition testDefinition,
      String sectionName,
      Long questionCount,
      QuestionType type,
      List<CompetitiveExamsDto.AnswerKeys> answerKeys,
      Long seqNo) {
    TestDefinitionSection testDefinitionSection = new TestDefinitionSection();
    testDefinitionSection.setTestDefinition(testDefinition);
    testDefinitionSection.setName(sectionName);
    testDefinitionSection.setNoOfQuestions(questionCount);
    testDefinitionSection.setSequenceNumber(seqNo);
    testDefinitionSection.setTestQuestions(
        buildTestQuestions(testDefinitionSection, sectionName, questionCount, type, answerKeys));
    return testDefinitionSection;
  }

  protected String generateMcqQuestionUuid(Long answer, Long questionNumber) {
    DateFormat sdf = new SimpleDateFormat("ddMMyyyyHHmmssSSS");
    String simpleDate = sdf.format(new Date());
    return "%s_%s_%s_%s_%s".formatted("special", "Q" + questionNumber, answer, 4L, simpleDate);
  }

  protected abstract List<TestQuestion> buildTestQuestions(
      TestDefinitionSection testDefinitionSection,
      String sectionName,
      Long questionCount,
      QuestionType type,
      List<CompetitiveExamsDto.AnswerKeys> answerKeys);
}
