package com.wexl.retail.metrics.reportcards;

import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.metrics.handler.AbstractMetricHandler;
import com.wexl.retail.metrics.handler.MetricHandler;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SubjectMean extends AbstractMetricHandler implements MetricHandler {

  private final ReportCardService reportCardService;

  @Override
  public String name() {
    return "subject-mean";
  }

  @Override
  public List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {
    List<String> section =
        Optional.ofNullable(genericMetricRequest.getInput().get(SECTIONS))
            .map(List.class::cast)
            .orElse(Collections.emptyList());
    List<String> gradeList =
        Optional.ofNullable(genericMetricRequest.getInput().get(GRADE))
            .map(List.class::cast)
            .orElse(Collections.emptyList());
    List<String> year =
        Optional.ofNullable(genericMetricRequest.getInput().get(YEAR))
            .map(List.class::cast)
            .orElse(Collections.emptyList());
    return reportCardService.getSubjectMean(org, gradeList, section, year);
  }
}
