package com.wexl.retail.thread.controller;

import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.commons.security.annotation.IsStudent;
import com.wexl.retail.thread.dto.ThreadDto;
import com.wexl.retail.thread.service.ThreadService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}")
@RequiredArgsConstructor
public class ThreadController {

  private final ThreadService threadService;

  @IsStudent
  @PostMapping("/students/{studentAuthId}/threads")
  public void createThread(
      @RequestBody ThreadDto.CreateThreadRequest request,
      @PathVariable String orgSlug,
      @PathVariable String studentAuthId) {
    threadService.createThread(request, orgSlug, studentAuthId);
  }

  @IsStudent
  @PutMapping("/students/{studentAuthId}/threads/{threadId}")
  public void updateThread(
      @RequestBody ThreadDto.CreateThreadRequest request,
      @PathVariable String orgSlug,
      @PathVariable String studentAuthId,
      @PathVariable Long threadId) {
    threadService.updateThread(orgSlug, request, studentAuthId, threadId);
  }

  @IsStudent
  @GetMapping("/students/{studentAuthId}/threads")
  public List<ThreadDto.ThreadResponse> getStudentThreads(@PathVariable String studentAuthId) {
    return threadService.getStudentThreads(studentAuthId);
  }

  @IsStudent
  @GetMapping("/threads")
  public List<ThreadDto.ThreadResponse> getAlStudentThreads(
      @PathVariable String orgSlug, @RequestParam(required = true) String gradeSlug) {
    return threadService.getAllStudentThreads(orgSlug, gradeSlug);
  }

  @GetMapping("/threads/{threadId}")
  public ThreadDto.ThreadByIdResponse getThreadById(@PathVariable Long threadId) {
    return threadService.getThreadById(threadId);
  }

  @IsOrgAdminOrTeacher
  @GetMapping("/teachers/{teacherAuthId}/threads")
  public List<ThreadDto.ThreadResponse> getThreadsByOrg(
      @PathVariable String orgSlug, @PathVariable String teacherAuthId) {
    return threadService.getThreadsByOrg(orgSlug, teacherAuthId);
  }

  @IsStudent
  @DeleteMapping("/students/{studentAuthId}/threads/{threadId}")
  public void deletingThreadById(
      @PathVariable String studentAuthId,
      @PathVariable Long threadId,
      @PathVariable String orgSlug) {
    threadService.deletingThreadById(studentAuthId, threadId, orgSlug);
  }
}
