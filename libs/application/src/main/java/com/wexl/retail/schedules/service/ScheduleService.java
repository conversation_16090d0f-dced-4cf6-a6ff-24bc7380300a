package com.wexl.retail.schedules.service;

import static com.wexl.retail.util.Constants.STUDENT_ZOOM_PATH;
import static com.wexl.retail.util.Constants.TEACHER_ZOOM_PATH;
import static com.wexl.retail.util.Constants.ZOOM_STANDARD_WEB_URL;
import static java.util.Comparator.comparingLong;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.auth.UserRoleHelper;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.schedules.domain.SectionSchedule;
import com.wexl.retail.schedules.dto.AggregatedScheduleResponse;
import com.wexl.retail.schedules.dto.ScheduleRequest;
import com.wexl.retail.schedules.dto.ScheduleResponse;
import com.wexl.retail.schedules.repository.SectionScheduleRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.service.SectionService;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Slf4j
@Service
@RequiredArgsConstructor
public class ScheduleService {

  public static final String SCHEDULE_NOT_FOUND_FOR_ID = "Schedule not found for Id";

  private final AuthService authService;
  private final ModelMapper modelMapper;
  private final DateTimeUtil dateTimeUtil;
  private final SectionService sectionService;
  private final SectionScheduleRepository sectionScheduleRepository;
  private final UserRoleHelper userRoleHelper;

  public ScheduleResponse createSchedule(ScheduleRequest scheduleRequest) {
    modelMapper.typeMap(ScheduleRequest.class, SectionSchedule.class);
    var schedule = modelMapper.map(scheduleRequest, SectionSchedule.class);
    var section = sectionService.findByUuid(scheduleRequest.getSectionUuid());
    schedule.setSection(section);
    schedule.setDayOfWeek(dateTimeUtil.getDayOfWeekFromEpoch(scheduleRequest.getScheduleTime()));

    validateOrganization(scheduleRequest.getOrganization(), section);

    var sectionSchedule = sectionScheduleRepository.save(schedule);
    return buildScheduleResponse(sectionSchedule);
  }

  public ScheduleResponse editSchedule(ScheduleRequest scheduleRequest) {
    modelMapper.typeMap(ScheduleRequest.class, SectionSchedule.class);
    final var scheduleById = sectionScheduleRepository.findById(scheduleRequest.getId());

    scheduleById.ifPresentOrElse(
        schedule -> validateOrganization(scheduleRequest.getOrganization(), schedule.getSection()),
        () -> {
          throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.ScheduleFind.Id");
        });

    assert scheduleById.isPresent();

    modelMapper.map(scheduleRequest, scheduleById.get());
    var sectionSchedule = sectionScheduleRepository.save(scheduleById.get());
    return buildScheduleResponse(sectionSchedule);
  }

  public List<ScheduleResponse> getSectionSchedules(String organization, String section) {
    return buildScheduleResponsesList(
        sectionScheduleRepository.getSectionSchedules(organization, UUID.fromString(section)));
  }

  public List<AggregatedScheduleResponse> getAllTeacherSchedulesAggregated(long teacherId) {
    List<ScheduleResponse> allTeacherSchedules = getAllTeacherSchedules(teacherId);
    return new ScheduleResponseAggregator(allTeacherSchedules).aggregate();
  }

  public List<ScheduleResponse> getAllTeacherSchedules(long teacherId) {
    return buildScheduleResponsesList(sectionScheduleRepository.getAllTeacherSchedules(teacherId));
  }

  public void deleteSchedule(String organization, Long scheduleId) {
    Assert.notNull(scheduleId, "scheduleId cannot be null");

    var scheduleById = sectionScheduleRepository.findById(scheduleId);

    Assert.isTrue(scheduleById.isPresent(), SCHEDULE_NOT_FOUND_FOR_ID);
    validateOrganization(organization, scheduleById.get().getSection());
    scheduleById.get().setDeletedAt(new Date());

    sectionScheduleRepository.save(scheduleById.get());
  }

  private void validateOrganization(String orgSlug, Section section) {
    if (!section.getOrganization().equals(orgSlug)) {
      throw new ApiException(InternalErrorCodes.UN_AUTHORIZED, "error.UnauthorizedAccess");
    }
  }

  private ScheduleResponse buildScheduleResponse(SectionSchedule schedule) {
    modelMapper.typeMap(SectionSchedule.class, ScheduleResponse.class);
    ScheduleResponse response = modelMapper.map(schedule, ScheduleResponse.class);
    String zoomMeetingNumber = deriveZoomMeetingFromUrl(response.getUrl());
    response.setZoomMeetingNumber(zoomMeetingNumber);
    if (isStudent()) {
      response.setUrl(schedule.getUrl().replace(TEACHER_ZOOM_PATH, STUDENT_ZOOM_PATH));
    } else if (StringUtils.isNotBlank(zoomMeetingNumber)) {
      response.setTeacherUrl(getUrlForTeacherRole(zoomMeetingNumber));
    }
    return response;
  }

  private String getUrlForTeacherRole(String zoomMeetingNumber) {
    return ZOOM_STANDARD_WEB_URL.formatted(zoomMeetingNumber);
  }

  private boolean isStudent() {
    var user = authService.getUserDetails();
    return userRoleHelper.isStudent(user);
  }

  public String deriveZoomMeetingFromUrl(String url) {
    // A hacky way of deriving the meeting number from url via pattern
    if (StringUtils.isBlank(url) || (!url.contains("wexledu.com") && !url.contains("wexl.in"))) {
      return "";
    }
    var studentZoomPrefix =
        new String[] {
          ".wexledu.com" + STUDENT_ZOOM_PATH,
          ".wexledu.com" + TEACHER_ZOOM_PATH,
          ".wexl.in" + TEACHER_ZOOM_PATH,
          ".wexl.in" + STUDENT_ZOOM_PATH
        };

    Optional<String> foundPrefix =
        Arrays.stream(studentZoomPrefix).filter(url::contains).findFirst();
    if (foundPrefix.isEmpty()) {
      return "";
    }

    var zoomPrefix = foundPrefix.get();
    int index = url.indexOf(zoomPrefix) + zoomPrefix.length();
    return url.substring(index);
  }

  private List<ScheduleResponse> buildScheduleResponsesList(List<SectionSchedule> schedules) {
    return schedules.stream()
        .map(this::buildScheduleResponse)
        .sorted(comparingLong(ScheduleResponse::getCreatedAt).reversed())
        .toList();
  }
}
