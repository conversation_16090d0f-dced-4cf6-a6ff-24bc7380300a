package com.wexl.retail.msg91.service;

import com.wexl.retail.msg91.dto.Msg91Dto;
import com.wexl.retail.msg91.dto.Msg91Dto.Recipient;
import com.wexl.retail.msg91.dto.Msg91Dto.Request;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Slf4j
@RequiredArgsConstructor
@Service
public class Msg91SmsService implements SmsService {

  private static final String INDIA_COUNTRY_CODE = "91";
  protected static final String MSG91_AUTHKEY_HEADER = "authkey";

  private final RestTemplate restTemplate;

  @Value("${app.msg91.authkey}")
  private String msgAuthkey;

  @Value("${app.msg91.sendBulkSms}")
  private String sendBulkSmsUrl;

  public void sendBulkMessage(String templateId, List<Recipient> recipients) {
    final List<Recipient> finalList = filterInvalidPhoneNumbers(recipients);
    if (finalList.isEmpty()) {
      log.info("No valid phone numbers to send message");
      return;
    }
    final Request request =
        Request.builder().templateId(templateId).recipients(finalList).sender("WEXLIN").build();
    var response =
        restTemplate.exchange(
            sendBulkSmsUrl, HttpMethod.POST, getRequestEntity(request), Msg91Dto.Response.class);

    if (!response.getStatusCode().is2xxSuccessful()
        || (response.getBody() != null && "error".equals(response.getBody().getType()))) {
      log.error("Error while sending message to msg91 {}", response.getBody());
    }
  }

  private HttpEntity<Request> getRequestEntity(Request request) {
    var headers = new HttpHeaders();
    headers.add(MSG91_AUTHKEY_HEADER, msgAuthkey);
    return new HttpEntity<>(request, headers);
  }
}
