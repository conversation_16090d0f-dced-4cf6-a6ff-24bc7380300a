package com.wexl.retail.courses.enrollment.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;

public record TeacherCourseDto() {

  @Builder
  public record EnrollmentResponse(
      @JsonProperty("enrollment_date") Long enrollmentDate,
      @JsonProperty("full_name") String fullName,
      @JsonProperty("teacher_id") Long teacherId,
      @JsonProperty("status") String status,
      @JsonProperty("team_id") Long teamId,
      @JsonProperty("team_name") String teamName,
      @JsonProperty("duration") Long duration,
      @JsonProperty("teacher_authid") String teacherAuthId,
      @JsonProperty("course_scheduleid") Long courseScheduleId) {}
}
