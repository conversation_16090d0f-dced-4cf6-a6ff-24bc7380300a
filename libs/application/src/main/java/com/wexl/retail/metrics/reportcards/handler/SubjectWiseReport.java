package com.wexl.retail.metrics.reportcards.handler;

import com.wexl.retail.metrics.dto.GenericMetricRequest;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.metrics.handler.AbstractMetricHandler;
import com.wexl.retail.metrics.handler.MetricHandler;
import com.wexl.retail.metrics.reportcards.service.SubjectWiseReportService;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SubjectWiseReport extends AbstractMetricHandler implements MetricHandler {

  private final SubjectWiseReportService subjectWiseReportService;

  public String name() {
    return "subject-wise-report";
  }

  @Override
  protected List<GenericMetricResponse> executeInternal(
      String org, GenericMetricRequest genericMetricRequest) {

    String board = (String) genericMetricRequest.getInput().get(BOARD);
    String grade = (String) genericMetricRequest.getInput().get(GRADE);
    String section = (String) genericMetricRequest.getInput().get(SECTION);
    Integer scheduleId = (Integer) genericMetricRequest.getInput().get(SCHEDULE);
    return subjectWiseReportService.getSubjectDetail(org, board, grade, section, scheduleId);
  }
}
