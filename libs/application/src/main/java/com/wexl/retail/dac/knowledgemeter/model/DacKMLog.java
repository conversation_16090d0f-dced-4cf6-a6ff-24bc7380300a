package com.wexl.retail.dac.knowledgemeter.model;

import com.wexl.retail.dac.knowledgemeter.dto.DacKMLogDto;
import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "dac_km_logs")
public class DacKMLog extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "org_slug")
  private String orgSlug;

  @Column(name = "failure_reason", columnDefinition = "VARCHAR(5000)")
  private String failureReason;

  private DacKMLogDto.JobStatus status;
}
