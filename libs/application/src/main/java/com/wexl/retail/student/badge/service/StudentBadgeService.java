package com.wexl.retail.student.badge.service;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.model.Badge;
import com.wexl.retail.model.UserBadge;
import com.wexl.retail.model.UserRole;
import com.wexl.retail.repository.BadgeRepository;
import com.wexl.retail.student.badge.dto.StudentBadgeDto;
import com.wexl.retail.student.badge.mapper.StudentBadgeMapper;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class StudentBadgeService {
  private final BadgeRepository badgeRepository;
  private final AuthService authService;

  public List<StudentBadgeDto> getAllStudentBadges() {
    long userId = authService.getUserDetails().getId();
    final var studentBadge = badgeRepository.getAllStudentBadges(userId, UserRole.ROLE_ISTUDENT);

    if (studentBadge == null || studentBadge.isEmpty()) {
      return new ArrayList<>();
    }

    List<StudentBadgeDto> response = new ArrayList<>();
    for (var item : studentBadge) {
      var userBadge = (UserBadge) item[0];
      var badge = (Badge) item[1];

      response.add(StudentBadgeMapper.mapper.studentBadgeToStudentBadgeDto(userBadge, badge));
    }
    return response;
  }
}
