package com.wexl.retail.erp.attendance.repository;

import com.wexl.retail.erp.attendance.domain.SectionAttendance;
import com.wexl.retail.erp.attendance.dto.AttendanceSummary;
import com.wexl.retail.erp.attendance.dto.StudentsAttendanceReport;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.section.domain.Section;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface SectionAttendanceRepository extends JpaRepository<SectionAttendance, Long> {

  @Query(
      value = "select * from section_attendance where date_id =:date and section_id = :sectionId",
      nativeQuery = true)
  SectionAttendance getSectionAttendanceDetails(Integer sectionId, Integer date);

  @Query(
      value =
          "select * from section_attendance where section_id = :sectionId and  date_id >=:fromDate and date_id <=:toDate",
      nativeQuery = true)
  List<SectionAttendance> findByDates(Integer sectionId, Integer fromDate, Integer toDate);

  @Query(
      value = "select * from section_attendance where section_id in (:sectionList)",
      nativeQuery = true)
  List<SectionAttendance> getSectionsData(List<Long> sectionList);

  @Query(
      value =
          """
                          select concat(u.first_name,' ',u.last_name) as fullName,se.name as sectionName,se.grade_name as gradeName,count(distinct sa.*) as totalWorkingDays,
                          SUM(CASE WHEN sad.attendance_status = 'absent' THEN 1 ELSE 0 END) AS absentDays,
                          SUM(CASE WHEN sad.attendance_status = 'present' THEN 1 ELSE 0 END) AS presentDays,
                          SUM(CASE WHEN sad.attendance_status = 'leave' THEN 1 ELSE 0 END) AS leaveDays,
                          SUM(CASE WHEN sad.attendance_status = 'late_comer' THEN 1 ELSE 0 END) AS lateComerDays,
                          SUM(CASE WHEN sad.attendance_status = 'ptm' THEN 1 ELSE 0 END) AS ptmDays,
                          SUM(CASE WHEN sad.attendance_status = 'nid' THEN 1 ELSE 0 END) AS nidDays,
                          SUM(CASE WHEN sad.afternoon_attendance_status = 'half_day' THEN 1 ELSE 0 END) AS halfDayDays,
                          u.auth_user_id as authId,
                           s.roll_number as rollNumber,u.user_name as userName,s.class_roll_number as classRollNumber
                          from section_attendance sa
                          join sections se on sa.section_id  = se.id
                          join orgs o on o.id = sa.org_id
                          join section_attendance_details sad  on sad.section_attendance_id  = sa.id
                          join students s on s.id = sad.student_id
                          join users u on u.id = s.user_id
                          where is_holiday  = 0 and date_id  between (:fromDate) and (:toDate)
                          and o.slug =:orgSlug and (cast((:gradeSlugs) as varChar) is null or se.grade_slug in (:gradeSlugs))
                          and (cast((:sectionIds) as varChar) is null or se.id in (:sectionIds))
                          and (cast((:boardIds) as varChar) is null or s.board_id in (:boardIds))
                          group by fullName,sectionName,gradeName,authId,roll_number,user_name,classRollNumber
                          order by sectionName asc
                          """,
      nativeQuery = true)
  List<StudentsAttendanceReport> getStudentsAttendanceReport(
      String orgSlug,
      List<String> gradeSlugs,
      List<Long> sectionIds,
      List<Long> boardIds,
      Integer fromDate,
      Integer toDate);

  @Query(
      value =
          """
    SELECT
        EXTRACT(MONTH FROM to_date(sa.date_id::text, 'YYYYMMDD')) AS month_no,
        CONCAT(u.first_name, ' ', u.last_name) AS full_name,
        se.name AS section_name,
        se.grade_name AS grade_name,
        u.auth_user_id AS auth_id,
        s.roll_number AS roll_number,
        u.user_name AS user_name,
        s.class_roll_number AS class_roll_number,
        count(distinct sa.*) as totalWorkingDays,
        SUM(CASE WHEN sad.attendance_status = 'present' THEN 1 ELSE 0 END) AS present_days,
        SUM(CASE WHEN sad.attendance_status = 'absent'  THEN 1 ELSE 0 END) AS absent_days
    FROM section_attendance sa
    JOIN section_attendance_details sad ON sad.section_attendance_id = sa.id
    JOIN sections se ON se.id = sa.section_id
    JOIN orgs o ON o.id = sa.org_id
    JOIN students s ON s.id = sad.student_id
    JOIN users u ON u.id = s.user_id
    WHERE sa.is_holiday = 0
      AND sa.date_id BETWEEN :fromDate AND :toDate
      AND o.slug = :orgSlug
      AND se.id = :sectionId
      AND u.auth_user_id = :authUserId
    GROUP BY
        month_no, se.name, se.grade_name,
                  full_name, u.auth_user_id, s.roll_number,
                  u.user_name, s.class_roll_number
    ORDER BY
        month_no, section_name ASC
    """,
      nativeQuery = true)
  List<StudentsAttendanceReport> getStudentMonthlyAttendance(
      String orgSlug, Long sectionId, String authUserId, Integer fromDate, Integer toDate);

  @Query(
      value =
          """
                          select concat(u.first_name,' ',u.last_name) as fullName,se.name as sectionName,se.grade_name as gradeName,count(distinct sa.*) as totalWorkingDays,
                          SUM(CASE WHEN sad.afternoon_attendance_status = 'absent' THEN 1 ELSE 0 END) AS absentDays,
                          SUM(CASE WHEN sad.afternoon_attendance_status = 'present' THEN 1 ELSE 0 END) AS presentDays,
                          SUM(CASE WHEN sad.afternoon_attendance_status = 'leave' THEN 1 ELSE 0 END) AS leaveDays,
                          SUM(CASE WHEN sad.afternoon_attendance_status = 'late_comer' THEN 1 ELSE 0 END) AS lateComerDays,
                          SUM(CASE WHEN sad.afternoon_attendance_status = 'ptm' THEN 1 ELSE 0 END) AS ptmDays,
                          SUM(CASE WHEN sad.afternoon_attendance_status = 'nid' THEN 1 ELSE 0 END) AS nidDays,
                          SUM(CASE WHEN sad.afternoon_attendance_status = 'half_day' THEN 1 ELSE 0 END) AS halfDayDays,
                          u.auth_user_id as authId,
                           s.roll_number as rollNumber,u.user_name as userName,s.class_roll_number as classRollNumber
                          from section_attendance sa
                          join sections se on sa.section_id  = se.id
                          join orgs o on o.id = sa.org_id
                          join section_attendance_details sad  on sad.section_attendance_id  = sa.id
                          join students s on s.id = sad.student_id
                          join users u on u.id = s.user_id
                          where is_holiday  = 0 and date_id  between (:fromDate) and (:toDate)
                          and o.slug =:orgSlug and (cast((:gradeSlugs) as varChar) is null or se.grade_slug in (:gradeSlugs))
                          and (cast((:sectionIds) as varChar) is null or se.id in (:sectionIds))
                          and (cast((:boardIds) as varChar) is null or s.board_id in (:boardIds))
                          group by fullName,sectionName,gradeName,authId,roll_number,user_name,classRollNumber
                          order by sectionName asc
                          """,
      nativeQuery = true)
  List<StudentsAttendanceReport> getStudentsAfternoonAttendanceReport(
      String orgSlug,
      List<String> gradeSlugs,
      List<Long> sectionIds,
      List<Long> boardIds,
      Integer fromDate,
      Integer toDate);

  List<SectionAttendance> findBySectionAndOrgAndDateIdBetweenOrderByDateId(
      Section sectionId, Organization orgId, Integer fromDate, Integer endDate);

  @Query(
      value =
          """
                              SELECT sa.*
                              FROM section_attendance sa
                              JOIN orgs o ON sa.org_id = o.id
                              JOIN sections s ON sa.section_id = s.id
                              WHERE o.slug = :orgSlug
                                AND s.grade_slug IN (:gradeSlugs)
                                AND sa.date_id BETWEEN :startDateId AND :endDateId
                          """,
      nativeQuery = true)
  List<SectionAttendance> findAttendanceByDateRangeAndGrades(
      @Param("orgSlug") String orgSlug,
      @Param("startDateId") Integer startDateId,
      @Param("endDateId") Integer endDateId,
      @Param("gradeSlugs") List<String> gradeSlugs);

  @Query(
      value =
          """
                                  select
                                                                                                                                                        	s.id as sectionId,
                                                                                                                                                        	sa.id as attendanceId,
                                                                                                                                                        	s.grade_name as gradeName,
                                                                                                                                                        	s.grade_slug as gradeSlug,
                                                                                                                                                        	s.name as sectionName,
                                                                                                                                                        	s."uuid" as sectionUuid,
                                                                                                                                                        	sa.date_id as dateId,
                                                                                                                                                        	sa.is_holiday as isHoliday,
                                                                                                                                                        	sa.date_id as dateId,
                                                                                                                                                        sum(case when sad.attendance_status= 'absent' then 1 else 0 end) absentCount,
                                                                                                                                              			sum(case when sad.attendance_status= 'present' then 1 else 0 end) presentCount,
                                                                                                                                              			sum(case when sad.attendance_status= 'late_comer' then 1 else 0 end) lateComerCount,
                                                                                                                                              			sum(case when sad.attendance_status= 'half_day' then 1 else 0 end) halfDayCount,
                                                                                                                                              			sum(case when sad.attendance_status= 'leave' then 1 else 0 end) leaveCount,
                                                                                                                                              			sum(case when sad.attendance_status= 'ptm' then 1 else 0 end) ptmCount,
                                                                                                                                              			sum(case when sad.attendance_status= 'nid' then 1 else 0 end) nidCount
                                                                                                                                                        from
                                                                                                                                                        	section_attendance sa
                                                                                                                                                        join sections s on
                                                                                                                                                        	sa.section_id = s.id
                                                                                                                                                        left join section_attendance_details sad on
                                                                                                                                                        	sad.section_attendance_id = sa.id
                                                                                                                                                        where sa.section_id in (:sectionIds)
                                                                                                          	and (cast((:dateId) as varChar) is null or sa.date_id in (:dateId))
                                                                                                          		group by sa.id, s.id , sa.date_id
                          """,
      nativeQuery = true)
  List<AttendanceSummary> findSectionByDateAndSectionIds(Integer dateId, List<Long> sectionIds);

  List<SectionAttendance> findBySectionIdAndDateIdBetween(
      Long sectionId, Integer fromDate, Integer toDate);

  Optional<SectionAttendance> findBySectionIdAndDateId(Long sectionId, int dateId);
}
