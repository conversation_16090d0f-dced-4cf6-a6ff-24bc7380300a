package com.wexl.retail.mlp.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.section.domain.Section;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Table(name = "mlp", schema = "public", indexes = @Index(columnList = "createdAt"))
@Entity
@EntityListeners({AuditingEntityListener.class})
@Getter
@Setter
public class Mlp {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "teacher_id")
  private Teacher teacher;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "section_id")
  private Section section;

  private String title;
  private String gradeSlug;
  private String subtopicSlug;

  @Column(columnDefinition = "TEXT")
  private String videoSlug;

  private String altVideoSlug;
  private String videoSource;
  private String subjectSlug;
  private String synopsisSlug;
  private String orgSlug;
  private String chapterSlug;

  @Column(columnDefinition = "VARCHAR(500)")
  private String comment;

  private String gradeName;
  private String subtopicName;
  private String subjectName;
  private String synopsisName;
  private String chapterName;

  @CreatedDate private Timestamp createdAt;

  @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
  private Date deletedAt;

  @LastModifiedDate private Timestamp updatedAt;

  private Timestamp startDate;
  private Timestamp endDate;

  private Integer dayOfWeek;

  private String examRef;

  private Integer questionCount;

  @OneToMany(fetch = FetchType.LAZY, mappedBy = "mlp", cascade = CascadeType.ALL)
  private List<MlpInst> mlpInsts;

  @Type(JsonType.class)
  @Column(name = "question_uuids", columnDefinition = "jsonb")
  private List<String> questionUuids;

  @Column(name = "questions_assignee_mode")
  @Enumerated(EnumType.STRING)
  private QuestionsAssigneeMode questionsAssigneeMode;

  @Column(name = "knowledge_percentage")
  private Double knowledgePercentage;

  @Column(name = "attendance_percentage")
  private Double attendancePercentage;

  @Column(name = "sha_link")
  private String shaLink;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "parent_mlp_id")
  private Mlp parent;
}
