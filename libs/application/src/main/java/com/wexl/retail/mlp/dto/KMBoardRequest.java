package com.wexl.retail.mlp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.*;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class KMBoardRequest {
  @JsonProperty("grades")
  private List<KMGradesRequest> grades;

  @JsonProperty("subjects")
  private List<String> subjects;

  @JsonProperty("chapter")
  private List<String> chapter;

  private List<String> data;
}
