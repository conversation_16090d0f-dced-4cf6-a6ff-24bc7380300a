package com.wexl.retail.metrics;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.ChapterResponse;
import com.wexl.retail.metrics.dto.GenericMetricResponse;
import com.wexl.retail.metrics.dto.StudentChapterPerformanceDto;
import com.wexl.retail.metrics.util.StudentChapterPerformanceGroupingKey;
import com.wexl.retail.model.User;
import com.wexl.retail.student.answer.ExamAnswer;
import com.wexl.retail.student.answer.StudentAnswerRepository;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.student.exam.ExamRepository;
import com.wexl.retail.test.schedule.domain.ScheduleTest;
import com.wexl.retail.test.schedule.domain.ScheduleTestStudent;
import com.wexl.retail.test.schedule.repository.ScheduleTestStudentRepository;
import com.wexl.retail.test.school.domain.TestDefinition;
import com.wexl.retail.test.school.domain.TestType;
import com.wexl.retail.test.school.dto.QuestionDto;
import com.wexl.retail.test.school.service.TestDefinitionService;
import com.wexl.retail.util.ValidationUtils;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class StudentChapterPerformanceService {

  private final ScheduleTestStudentRepository scheduleTestStudentRepository;
  private final ExamRepository examRepository;
  private final StudentAnswerRepository studentAnswerRepository;
  private final TestDefinitionService testDefinitionService;
  private final ValidationUtils validationUtils;
  private final ContentService contentService;

  public List<GenericMetricResponse> getStudentChapterPerformanceReport(
      Long testScheduleId, Boolean isStudent, String studentAuthId) {

    ScheduleTest scheduleTest = validationUtils.isTestScheduleValid(testScheduleId);
    TestDefinition testDefinition = scheduleTest.getTestDefinition();

    if (!TestType.MOCK_TEST.equals(testDefinition.getType())) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.UnsupportedTestType",
          new String[] {testDefinition.getType().toString(), "MOCK_TEST"});
    }

    List<ScheduleTestStudent> scheduleTestStudents = getCompletedStudents(testScheduleId);
    QuestionDto.QuestionResponse questionResponse =
        testDefinitionService.getQuestionResponsePreconfigured(testDefinition, 0);
    List<QuestionDto.Question> questions = extractQuestionsFromResponse(questionResponse);
    Map<String, StudentChapterPerformanceDto.QuestionMetadata> questionMetadataMap =
        createQuestionMetadataMap(questions, testDefinition);

    if (Boolean.TRUE.equals(isStudent) && studentAuthId != null) {
      scheduleTestStudents =
          scheduleTestStudents.stream()
              .filter(student -> student.getStudent().getAuthUserId().equals(studentAuthId))
              .collect(Collectors.toList());
    }

    List<StudentChapterPerformanceDto.StudentData> studentDataList =
        processStudentData(scheduleTestStudents, testScheduleId, questionMetadataMap);

    return convertToGenericMetricResponse(
        studentDataList, scheduleTestStudents, Boolean.TRUE.equals(isStudent));
  }

  private List<QuestionDto.Question> extractQuestionsFromResponse(
      QuestionDto.QuestionResponse questionResponse) {
    return questionResponse.testDefinitionSectionResponses().stream()
        .map(QuestionDto.TestDefinitionSectionResponse::questions)
        .flatMap(List::stream)
        .collect(Collectors.toList());
  }

  private List<ScheduleTestStudent> getCompletedStudents(Long testScheduleId) {
    return scheduleTestStudentRepository
        .getAllStudentsByScheduledId(Collections.singletonList(testScheduleId))
        .stream()
        .filter(student -> "COMPLETED".equals(student.getStatus()))
        .collect(Collectors.toList());
  }

  private Map<String, StudentChapterPerformanceDto.QuestionMetadata> createQuestionMetadataMap(
      List<QuestionDto.Question> questions, TestDefinition testDefinition) {

    Set<String> chapterSlugs =
        questions.stream()
            .map(QuestionDto.Question::chapterSlug)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

    Map<String, String> chapterSlugToName = new HashMap<>();
    for (String chapterSlug : chapterSlugs) {
      try {
        ChapterResponse chapterResponse = contentService.getChapterBySlug(chapterSlug);
        if (chapterResponse != null && chapterResponse.getName() != null) {
          chapterSlugToName.put(chapterSlug, chapterResponse.getName());
        } else {
          chapterSlugToName.put(chapterSlug, chapterSlug);
        }
      } catch (Exception e) {
        log.warn("Failed to fetch chapter name for slug: {}, using slug as name", chapterSlug, e);
        chapterSlugToName.put(chapterSlug, chapterSlug);
      }
    }

    return questions.stream()
        .collect(
            Collectors.toMap(
                QuestionDto.Question::uuid,
                question ->
                    new StudentChapterPerformanceDto.QuestionMetadata(
                        question.chapterSlug(),
                        chapterSlugToName.getOrDefault(
                            question.chapterSlug(), question.chapterSlug()),
                        question.type() != null ? question.type().getType() : "UNKNOWN",
                        question.complexity(),
                        question.questionTags() != null
                            ? question.questionTags()
                            : Collections.emptyList(),
                        question.marks())));
  }

  private List<StudentChapterPerformanceDto.StudentData> processStudentData(
      List<ScheduleTestStudent> scheduleTestStudents,
      Long testScheduleId,
      Map<String, StudentChapterPerformanceDto.QuestionMetadata> questionMetadataMap) {

    List<StudentChapterPerformanceDto.StudentData> studentDataList = new ArrayList<>();

    for (ScheduleTestStudent scheduleTestStudent : scheduleTestStudents) {
      User studentUser = scheduleTestStudent.getStudent();

      Optional<Exam> examOpt =
          examRepository.getExamDetails(studentUser.getStudentInfo().getId(), testScheduleId);

      if (examOpt.isEmpty()) {
        continue;
      }

      Exam exam = examOpt.get();
      List<ExamAnswer> examAnswers = exam.getExamAnswers();

      float totalMarksScored = exam.getMarksScored();

      float totalPossibleMarks = exam.getTotalMarks();

      double overallPercentage =
          totalPossibleMarks > 0 ? (totalMarksScored / totalPossibleMarks) * 100 : 0;

      List<StudentChapterPerformanceDto.ChapterPerformance> chapterPerformances =
          processChapterPerformances(examAnswers, questionMetadataMap);

      StudentChapterPerformanceDto.StudentData studentData =
          new StudentChapterPerformanceDto.StudentData(
              studentUser.getFirstName() + " " + studentUser.getLastName(),
              studentUser.getStudentInfo().getId(),
              studentUser.getId(),
              studentUser.getStudentInfo().getSection().getGradeName(),
              totalMarksScored,
              totalPossibleMarks,
              overallPercentage,
              chapterPerformances);

      studentDataList.add(studentData);
    }

    return studentDataList;
  }

  private List<StudentChapterPerformanceDto.ChapterPerformance> processChapterPerformances(
      List<ExamAnswer> examAnswers,
      Map<String, StudentChapterPerformanceDto.QuestionMetadata> questionMetadataMap) {

    List<StudentChapterPerformanceDto.ChapterPerformance> performances = new ArrayList<>();

    Map<String, List<ExamAnswer>> answersByChapter =
        examAnswers.stream()
            .filter(answer -> questionMetadataMap.containsKey(answer.getQuestionUuid()))
            .collect(
                Collectors.groupingBy(
                    answer -> questionMetadataMap.get(answer.getQuestionUuid()).chapterSlug()));

    for (Map.Entry<String, List<ExamAnswer>> chapterEntry : answersByChapter.entrySet()) {
      String chapterSlug = chapterEntry.getKey();
      List<ExamAnswer> chapterAnswers = chapterEntry.getValue();

      Map<StudentChapterPerformanceGroupingKey, List<ExamAnswer>> groupedAnswers =
          chapterAnswers.stream()
              .collect(
                  Collectors.groupingBy(
                      answer -> {
                        StudentChapterPerformanceDto.QuestionMetadata metadata =
                            questionMetadataMap.get(answer.getQuestionUuid());
                        return new StudentChapterPerformanceGroupingKey(
                            metadata.chapterSlug(),
                            metadata.chapterName(),
                            metadata.questionType(),
                            metadata.complexity(),
                            metadata.questionTags());
                      }));

      for (Map.Entry<StudentChapterPerformanceGroupingKey, List<ExamAnswer>> groupEntry :
          groupedAnswers.entrySet()) {
        StudentChapterPerformanceGroupingKey key = groupEntry.getKey();
        List<ExamAnswer> groupAnswers = groupEntry.getValue();

        StudentChapterPerformanceDto.ChapterPerformance performance =
            calculateChapterGroupPerformance(key, groupAnswers, questionMetadataMap);

        performances.add(performance);
      }
    }

    return performances;
  }

  private StudentChapterPerformanceDto.ChapterPerformance calculateChapterGroupPerformance(
      StudentChapterPerformanceGroupingKey key,
      List<ExamAnswer> groupAnswers,
      Map<String, StudentChapterPerformanceDto.QuestionMetadata> questionMetadataMap) {

    int questionsCount = groupAnswers.size();
    float marksScored =
        groupAnswers.stream()
            .map(ExamAnswer::getMarksScoredPerQuestion)
            .filter(Objects::nonNull)
            .reduce(0f, Float::sum);

    float totalMarks =
        groupAnswers.stream()
            .map(ExamAnswer::getMarksPerQuestion)
            .filter(Objects::nonNull)
            .reduce(0, Integer::sum);

    double average = questionsCount > 0 ? marksScored / questionsCount : 0;
    double percentage = totalMarks > 0 ? (marksScored / totalMarks) * 100 : 0;

    // Get chapter name from grouping key (already includes both slug and name)
    String chapterName = key.chapterName;

    List<StudentChapterPerformanceDto.QuestionResponse> questionResponses =
        groupAnswers.stream()
            .map(
                answer ->
                    new StudentChapterPerformanceDto.QuestionResponse(
                        answer.getQuestionUuid(),
                        answer.getMarksScoredPerQuestion(),
                        answer.getMarksPerQuestion()))
            .collect(Collectors.toList());

    return new StudentChapterPerformanceDto.ChapterPerformance(
        chapterName,
        key.questionType,
        key.complexity,
        key.questionTags,
        questionsCount,
        average,
        percentage,
        questionResponses);
  }

  private List<GenericMetricResponse> convertToGenericMetricResponse(
      List<StudentChapterPerformanceDto.StudentData> studentDataList,
      List<ScheduleTestStudent> allStudents,
      boolean isStudentSpecific) {

    Map<String, Object> data = new HashMap<>();
    data.put("students", studentDataList);

    Map<String, Object> summary = new HashMap<>();
    if (!isStudentSpecific) {

      double overallAverage =
          studentDataList.stream()
              .mapToDouble(StudentChapterPerformanceDto.StudentData::overallPercentage)
              .average()
              .orElse(0.0);

      summary.put("overall_average", overallAverage);
      summary.put("total_students_appeared", allStudents.size());
      summary.put("total_students_completed", studentDataList.size());
    }

    return Collections.singletonList(
        GenericMetricResponse.builder().data(data).summary(summary).build());
  }
}
