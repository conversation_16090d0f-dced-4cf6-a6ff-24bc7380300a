package com.wexl.retail.student.exam.migration;

import com.wexl.retail.test.schedule.domain.ScheduleTestStudent;
import com.wexl.retail.test.schedule.dto.TestStudentStatus;
import com.wexl.retail.test.schedule.repository.ScheduleTestStudentRepository;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}")
public class ExamMigrationController {
  private final ExamMigrationService examMigrationService;
  private final ScheduleTestStudentRepository scheduleTestStudentRepository;

  @PostMapping("/mock-exams")
  public void migrateSubmittedExams() {
    final List<ScheduleTestStudent> tssEntries =
        examMigrationService.identifyValidTestScheduleStudentEntries();
    if (tssEntries.isEmpty()) {
      return;
    }
    tssEntries.forEach(tss -> tss.setStatus(TestStudentStatus.PROCESSING.name()));
    var tssEntriesUpdated = scheduleTestStudentRepository.saveAll(tssEntries);
    examMigrationService.migrateSubmittedExams(tssEntriesUpdated);
  }

  @PostMapping("/populate-exams/{testScheduleId}")
  public void reprocessTestSchedule(@PathVariable Long testScheduleId) {
    final List<ScheduleTestStudent> tssEntriesAll =
        scheduleTestStudentRepository.getAllStudentsByScheduledId(List.of(testScheduleId));
    final List<ScheduleTestStudent> tssEntries =
        tssEntriesAll.stream()
            .filter(tss -> !tss.getStatus().equals(TestStudentStatus.PENDING.name()))
            .toList();
    if (tssEntries.isEmpty()) {
      return;
    }
    tssEntries.forEach(tss -> tss.setStatus(TestStudentStatus.PROCESSING.name()));
    var tssEntriesUpdated = scheduleTestStudentRepository.saveAll(tssEntries);
    examMigrationService.migrateSubmittedExams(tssEntriesUpdated);
  }

  @PostMapping("/auto-submit")
  public void autoSubmitExam() {
    examMigrationService.autoSubmitExam();
  }

  @PostMapping("/migrate-exam-attributes/{testScheduleId}")
  public void migrateExamAttributes(@PathVariable Long testScheduleId) {
    examMigrationService.migrateExamAttributes(testScheduleId);
  }
}
