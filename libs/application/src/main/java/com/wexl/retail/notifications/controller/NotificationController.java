package com.wexl.retail.notifications.controller;

import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.notification.service.EventNotificationService;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.notifications.dto.NotificationType;
import com.wexl.retail.notifications.service.NotificationsService;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.UserRepository;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}/teachers/{teacherAuthUserId}/notifications")
public class NotificationController {

  private final NotificationsService notificationService;

  private final EventNotificationService eventNotificationService;
  private final UserRepository userRepository;
  private final StudentRepository studentRepository;

  @IsOrgAdminOrTeacher
  @PostMapping
  @ResponseStatus(HttpStatus.CREATED)
  public void createNotification(
      @RequestBody NotificationDto.NotificationRequest notificationRequest,
      @PathVariable String orgSlug,
      @PathVariable("teacherAuthUserId") String teacherAuthId,
      @RequestParam(required = false, defaultValue = "false") boolean allClassRooms) {
    long createdAt =
        LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();

    if (NotificationType.SECTION.name().equals(notificationRequest.notificationType().name())) {
      notificationService.createNotificationBySection(orgSlug, notificationRequest, teacherAuthId);
      eventNotificationService.sendPushNotificationForSection(
          orgSlug,
          notificationRequest.message(),
          notificationRequest.title(),
          notificationRequest.sectionUuids(),
          notificationRequest.studentIds(),
          teacherAuthId);
    } else if (NotificationType.GRADE
        .name()
        .equals(notificationRequest.notificationType().name())) {
      notificationService.createNotificationByGrade(orgSlug, notificationRequest, teacherAuthId);
      eventNotificationService.sendPushNotificationWithGrade(
          orgSlug,
          notificationRequest.message(),
          notificationRequest.title(),
          notificationRequest.gradeSlugs());
    } else if (NotificationType.ORGANIZATION
        .name()
        .equals(notificationRequest.notificationType().name())) {
      notificationService.createNotificationForOrganization(
          orgSlug, notificationRequest, teacherAuthId, true, createdAt);
    }
  }

  @IsOrgAdminOrTeacher
  @DeleteMapping("/{id}")
  @ResponseStatus(HttpStatus.ACCEPTED)
  public void deleteNotification(
      @PathVariable String orgSlug,
      @PathVariable("teacherAuthUserId") String teacherAuthId,
      @PathVariable Long id) {
    notificationService.deleteNotification(orgSlug, teacherAuthId, id);
  }

  @IsOrgAdminOrTeacher
  @GetMapping
  public List<NotificationDto.TeacherNotificationResponse> getTeacherNotifications(
      @PathVariable String orgSlug,
      @PathVariable("teacherAuthUserId") String teacherAuthId,
      @RequestParam(name = "category", required = false) Long category,
      @RequestParam(required = false, defaultValue = "50") int limit) {
    return notificationService.getTeacherNotifications(
        orgSlug, teacherAuthId, category, limit, false);
  }

  @IsOrgAdminOrTeacher
  @PostMapping("/{id}")
  @ResponseStatus(HttpStatus.ACCEPTED)
  public void editNotification(
      @RequestBody NotificationDto.NotificationRequest notificationRequest,
      @PathVariable String orgSlug,
      @PathVariable("teacherAuthUserId") String teacherAuthId,
      @PathVariable("id") Long notificationId) {
    notificationService.editNotificationByTeacher(
        orgSlug, notificationRequest, teacherAuthId, notificationId);
  }

  @GetMapping("/{id}/notification-logs")
  public List<NotificationDto.NotificationLogResponse> getNotificationLogResponse(
      @PathVariable("id") Long notificationId) {
    return notificationService.getNotificationLogById(notificationId);
  }

  @IsOrgAdminOrTeacher
  @GetMapping("/{id}")
  public NotificationDto.NotificationAttributes getNotificationById(
      @PathVariable String orgSlug, @PathVariable("id") Long notificationId) {
    return notificationService.getNotificationById(orgSlug, notificationId);
  }
}
