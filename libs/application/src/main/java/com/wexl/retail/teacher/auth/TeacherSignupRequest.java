package com.wexl.retail.teacher.auth;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.globalprofile.model.RoleTemplate;
import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class TeacherSignupRequest {
  String userName;
  String emailAddress;
  String firstName;
  String hearAboutFrom;
  String lastName;
  String mobileNumber;
  String principalName;
  String password;
  String captchaCode;
  Boolean termsAndConditions;
  String orgSlug;
  boolean orgAdmin;
  String roleType;
  String countryCode;
  String externalRef;
  String instituteName;

  @JsonProperty("role_template")
  RoleTemplate roleTemplate;
}
