package com.wexl.retail.student.auth;

import lombok.Builder;
import lombok.Data;

@Builder
@Data
public class BulkStudentSignup {
  private String userName;
  private String firstName;
  private String lastName;
  private String schoolName;
  private int classId;
  private int boardId;
  private String password;
  private String parentFirstName;
  private String parentLastName;
  private String parentEmail;
  private String parentMobileNumber;
  private String importStatus;
  private String importErrorMessage;
}
