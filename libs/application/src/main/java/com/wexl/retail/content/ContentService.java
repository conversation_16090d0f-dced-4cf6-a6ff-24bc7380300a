package com.wexl.retail.content;

import static java.lang.String.format;

import com.wexl.retail.calenderevent.dto.CalenderEventDto;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.content.model.*;
import com.wexl.retail.content.rest.model.Organization;
import com.wexl.retail.courses.step.dto.AssetResponse;
import com.wexl.retail.elp.dto.BoardsDto;
import com.wexl.retail.elp.dto.ElpDto;
import com.wexl.retail.model.OrgSettings;
import com.wexl.retail.model.PracticeRequest;
import com.wexl.retail.organization.dto.QuestionCountByOrg;
import com.wexl.retail.qpgen.dto.*;
import com.wexl.retail.student.goalplan.SubjectResponse;
import com.wexl.retail.task.dto.TestRequest;
import com.wexl.retail.test.assignment.dto.AssignmentRequest;
import com.wexl.retail.test.school.dto.QuestionDto;
import com.wexl.retail.test.school.dto.TestQuestionRequest;
import com.wexl.retail.util.Constants;
import com.wexl.retail.util.dto.SubjectDetailResponse;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

@Slf4j
@Service
@SuppressWarnings("unchecked")
public class ContentService {

  private final String url;
  private final RestTemplate restTemplate;
  private final String contentBearerToken;
  private final String contentBearerSub;

  private static final String INVALID_CHAPTER = "error.InvalidChapter";

  public ContentService(
      @Value("${urls.content}") String url,
      @Value("${app.contentToken}") String contentBearerToken,
      @Value("${app.contentBearerSub:ad197296-94b4-4775-9478-f9d7799cc1bd}")
          String contentBearerSub,
      RestTemplate restTemplate) {
    this.contentBearerToken = contentBearerToken;
    this.url = url;
    this.restTemplate = restTemplate;
    this.contentBearerSub = contentBearerSub;
  }

  public Question getQuestionBySubjectAndUuid(
      String bearerToken, QuestionType questionType, long subjectId, String uuid) {
    String endPoint =
        url + "subjects/" + subjectId + "/questions/" + questionType.getType() + "/" + uuid;
    ResponseEntity<Question> responseEntity =
        restTemplate.exchange(
            endPoint,
            HttpMethod.GET,
            getRequestEntity(bearerToken),
            questionType.getResultSetTypeWithAnswer());
    return responseEntity.getBody();
  }

  public Question getQuestionByUuid(String bearerToken, QuestionType questionType, String uuid) {
    String endPoint = url + "questions/" + questionType.getType() + "/" + uuid;
    try {
      ResponseEntity<Question> responseEntity =
          restTemplate.exchange(
              endPoint,
              HttpMethod.GET,
              getRequestEntity(bearerToken),
              questionType.getResultSetTypeWithAnswer());
      return responseEntity.getBody();
    } catch (Exception exception) {
      log.debug("Question not found with uuid " + uuid);
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, exception.getMessage(), exception);
    }
  }

  @Nullable
  public Question getQuestionBySubjectSlugAndUuid(
      String bearerToken,
      QuestionType questionType,
      String subjectSlug,
      String uuid,
      boolean showDefaultWhenQuestionIsDeleted) {

    var endPoint =
        "%squestions/%s/%s/%s?showDefaultWhenQuestionIsDeleted=%s"
            .formatted(
                url, questionType.getType(), subjectSlug, uuid, showDefaultWhenQuestionIsDeleted);
    String urlTemplate = UriComponentsBuilder.fromUriString(endPoint).encode().toUriString();
    ResponseEntity<Question> responseEntity =
        restTemplate.exchange(
            urlTemplate,
            HttpMethod.GET,
            getRequestEntity(bearerToken),
            questionType.getResultSetTypeWithAnswer());
    return responseEntity.getBody();
  }

  @Nullable
  public Question getQuestionBySubjectSlugAndUuidForStudent(
      String bearerToken,
      QuestionType questionType,
      String subjectSlug,
      String uuid,
      boolean showDefaultWhenQuestionIsDeleted) {
    var endPoint =
        "%squestions/%s/%s/%s?showDefaultWhenQuestionIsDeleted=%s"
            .formatted(
                url,
                questionType.getType(),
                subjectSlug,
                uuid.replace(" ", ""),
                showDefaultWhenQuestionIsDeleted);
    String urlTemplate = UriComponentsBuilder.fromUriString(endPoint).encode().toUriString();
    ResponseEntity<Question> responseEntity =
        restTemplate.exchange(
            urlTemplate,
            HttpMethod.GET,
            getRequestEntity(bearerToken),
            questionType.getResultSetTypeWithOutAnswer());
    return responseEntity.getBody();
  }

  public List<TestQuestionRequest> createAssignment(String bearerToken, AssignmentRequest request) {
    String endPoint = url + "assignment/questions";
    var headers = new HttpHeaders();
    headers.add(HttpHeaders.AUTHORIZATION, bearerToken);
    var httpEntity = new HttpEntity<>(request, headers);
    return restTemplate
        .exchange(
            endPoint,
            HttpMethod.POST,
            httpEntity,
            new ParameterizedTypeReference<List<TestQuestionRequest>>() {})
        .getBody();
  }

  public Question getAssignmentQuestionByUuid(String bearerToken, String uuid) {
    String endPoint = url + "assignment/questions/" + uuid;
    return restTemplate
        .exchange(endPoint, HttpMethod.GET, getRequestEntity(bearerToken), Question.class)
        .getBody();
  }

  public Organization createOrganization(String bearerToken, OrganizationRequest orgRequest) {
    String endPoint = url + "orgs";
    var headers = new HttpHeaders();
    headers.add(HttpHeaders.AUTHORIZATION, bearerToken);
    var httpEntity = new HttpEntity<>(orgRequest, headers);
    try {
      return restTemplate
          .exchange(endPoint, HttpMethod.POST, httpEntity, Organization.class)
          .getBody();
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InstituteName");
    }
  }

  private HttpEntity<String> getRequestEntity(String bearerToken) {
    var headers = new HttpHeaders();
    headers.add(HttpHeaders.AUTHORIZATION, bearerToken);
    return new HttpEntity<>(null, headers);
  }

  public Organization editOrganization(String bearerToken, OrganizationRequest orgRequest) {
    String endPoint = format("%sorgs/%s/edit", url, orgRequest.getSlug());
    var headers = new HttpHeaders();
    headers.add(HttpHeaders.AUTHORIZATION, bearerToken);
    var httpEntity = new HttpEntity<>(orgRequest, headers);
    return restTemplate
        .exchange(endPoint, HttpMethod.PUT, httpEntity, Organization.class)
        .getBody();
  }

  public QuestionResponse getPracticeQuestions(
      String bearerToken, PracticeRequest practiceRequest) {
    String endPoint =
        format(
            "%squestions/practice?subject=%s&chapter=%s&subTopic=%s&goalComplexity=%s&questionCount=%s&offset=%s&showAllComplexLevelQuestions=%s",
            url,
            practiceRequest.getSubject(),
            practiceRequest.getChapter(),
            practiceRequest.getSubTopic(),
            practiceRequest.getGoalComplexity(),
            practiceRequest.getQuestionCount(),
            practiceRequest.getOffset(),
            true);
    return restTemplate
        .exchange(endPoint, HttpMethod.GET, getRequestEntity(bearerToken), QuestionResponse.class)
        .getBody();
  }

  public SubTopicResponse getSubTopicBySlug(
      String orgSlug, String subTopicSlug, String bearerToken) {
    String endpoint = "%sorgs/%s/subtopics/%s".formatted(url, orgSlug, subTopicSlug);

    return restTemplate
        .exchange(endpoint, HttpMethod.GET, getRequestEntity(bearerToken), SubTopicResponse.class)
        .getBody();
  }

  public SubTopicResponse getSubTopicById(String orgSlug, Long subTopicId, String bearerToken) {
    String endpoint = "%sorgs/%s/subtopics?id=%s".formatted(url, orgSlug, subTopicId);

    List<SubTopicResponse> responses =
        restTemplate
            .exchange(
                endpoint,
                HttpMethod.GET,
                getRequestEntity(bearerToken),
                new ParameterizedTypeReference<List<SubTopicResponse>>() {})
            .getBody();
    if (responses == null || responses.size() != 1) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.SubTopicNotFound",
          new String[] {Long.toString(subTopicId)});
    }
    return responses.getFirst();
  }

  public List<SubTopicResponse> getSubtopicsByBoardAndGradeAndSubject(
      String orgSlug, String boardSlug, List<String> gradeSlug, List<String> subjectSlug) {
    StringBuilder gradeParams = new StringBuilder();
    for (String grade : gradeSlug) {
      gradeParams.append("&grade=").append(grade);
    }

    StringBuilder subjectParams = new StringBuilder();
    for (String subject : subjectSlug) {
      subjectParams.append("&subject=").append(subject);
    }
    String endpoint =
        "%sorgs/%s/subtopics/response?board=%s%s%s"
            .formatted(url, orgSlug, boardSlug, gradeParams, subjectParams);

    try {
      return restTemplate
          .exchange(
              endpoint,
              HttpMethod.GET,
              getRequestEntity(contentBearerToken),
              new ParameterizedTypeReference<List<SubTopicResponse>>() {})
          .getBody();
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.subtopicNotFound");
    }
  }

  public QuestionResponse getTestQuestionsByChapter(TestRequest testRequest, String bearerToken) {

    String endPoint = url + "questions/test";
    try {
      var headers = new HttpHeaders();
      headers.add(HttpHeaders.AUTHORIZATION, bearerToken);
      var httpEntity = new HttpEntity<>(testRequest, headers);
      return restTemplate
          .exchange(endPoint, HttpMethod.POST, httpEntity, QuestionResponse.class)
          .getBody();
    } catch (Exception exception) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, exception.getMessage(), exception);
    }
  }

  public QuestionDto.SearchQuestionResponse getQuestionsByUuid(
      String bearerToken, String questionType, String uuid, String orgSlug) {

    String endPoint =
        "%s/orgs/%s/questions?type=%s&uuid=%s&showDeletedQuestion=%s&questionType="
            .formatted(url, orgSlug, questionType.toUpperCase(), uuid, Boolean.TRUE);

    try {
      ResponseEntity<QuestionDto.SearchQuestionResponse> responseEntity =
          restTemplate.exchange(
              endPoint,
              HttpMethod.GET,
              getRequestEntity(bearerToken),
              QuestionDto.SearchQuestionResponse.class);
      return responseEntity.getBody();
    } catch (Exception exception) {
      log.debug("Question not found with uuid " + uuid);
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, exception.getMessage(), exception);
    }
  }

  public Entity getAcademicYearBySlug(String orgSlug, String academicYearSlug) throws ApiException {
    String endpoint = "%sorgs/%s/academic-years/%s".formatted(url, orgSlug, academicYearSlug);
    return restTemplate
        .exchange(endpoint, HttpMethod.GET, getRequestEntity(contentBearerToken), Entity.class)
        .getBody();
  }

  public void createQuestion(
      QuestionDto.QuestionRequest question, String orgSlug, String bearerToken) {
    String endPoint = "%sorgs/%s/questions".formatted(url, orgSlug);
    var headers = new HttpHeaders();
    headers.add(HttpHeaders.AUTHORIZATION, bearerToken);
    var httpEntity = new HttpEntity<>(question, headers);
    try {
      ResponseEntity<String> responseEntity =
          restTemplate.exchange(endPoint, HttpMethod.POST, httpEntity, String.class);
      if (!HttpStatus.CREATED.equals(responseEntity.getStatusCode())) {
        throw new ApiException(
            InternalErrorCodes.INVALID_REQUEST, "Could not create live work sheet");
      }
      log.info("{} question  created successfully in strapi", question.type());
    } catch (Exception e) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "Unable to create Question in strapi " + e.getMessage(),
          e);
    }
  }

  public Grade getGradeById(int gradeId) {
    String endPoint = "%s/orgs/%s/grades/%s".formatted(url, Constants.WEXL_INTERNAL, gradeId);

    ResponseEntity<Grade> responseEntity =
        restTemplate.exchange(
            endPoint, HttpMethod.GET, getRequestEntity(contentBearerToken), Grade.class);

    if (HttpStatus.OK.equals(responseEntity.getStatusCode())) {
      return responseEntity.getBody();
    } else {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.GradeGet.Id",
          new String[] {Integer.toString(gradeId)});
    }
  }

  public OrgSettings getOrgSettings(String orgSlug) {
    String endPoint = "%s/orgs/%s/org-settings".formatted(url, orgSlug);

    return restTemplate
        .exchange(endPoint, HttpMethod.GET, getRequestEntity(contentBearerToken), OrgSettings.class)
        .getBody();
  }

  public String getGradeNameBySlug(String gradeSlug) {
    if (ObjectUtils.isEmpty(gradeSlug)) {
      return "";
    }
    try {
      Grade result = getGradeBySlug(gradeSlug);
      return result == null ? "" : result.getName();
    } catch (Exception ex) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.InvalidGradeSlug", new String[] {gradeSlug});
    }
  }

  public Grade getGradeBySlug(String gradeSlug) {
    if (ObjectUtils.isEmpty(gradeSlug) || gradeSlug.length() > 8) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "Invalid GradeSlug", new String[] {gradeSlug});
    }
    String endPoint =
        "%s/orgs/%s/grade?gradeSlug=%s".formatted(url, Constants.WEXL_INTERNAL, gradeSlug);
    ResponseEntity<Grade> responseEntity =
        restTemplate.exchange(
            endPoint, HttpMethod.GET, getRequestEntity(contentBearerToken), Grade.class);

    if (HttpStatus.OK.equals(responseEntity.getStatusCode())) {
      return responseEntity.getBody();
    } else {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "Invalid GradeSlug", new String[] {gradeSlug});
    }
  }

  public BoardsDto.Boards getBoardDetailsBySlug(String orgSlug, String boardSlug) {
    String endPoint = "%s/orgs/%s/boards/%s".formatted(url, orgSlug, boardSlug);
    ResponseEntity<BoardsDto.Boards> responseEntity =
        restTemplate.exchange(
            endPoint, HttpMethod.GET, getRequestEntity(contentBearerToken), BoardsDto.Boards.class);

    if (HttpStatus.OK.equals(responseEntity.getStatusCode())) {
      return responseEntity.getBody();
    } else {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "Invalid BoardSlug", new String[] {boardSlug});
    }
  }

  public AssetResponse getAssetBySlug(String assetSlug) {
    String endPoint =
        "%sorgs/%s/teachers/%s/assets/%s"
            .formatted(url, Constants.WEXL_INTERNAL, contentBearerSub, assetSlug);
    ResponseEntity<AssetResponse> responseEntity =
        restTemplate.exchange(
            endPoint, HttpMethod.GET, getRequestEntity(contentBearerToken), AssetResponse.class);
    if (HttpStatus.OK.equals(responseEntity.getStatusCode())) {
      return responseEntity.getBody();
    } else {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.InvalidAssertSlug", new String[] {assetSlug});
    }
  }

  public List<CalenderEventDto.AssetResponse> getAssetLinks(List<String> assetSlugs) {
    if (assetSlugs == null || assetSlugs.isEmpty()) {
      return new ArrayList<>();
    }

    final List<CalenderEventDto.AssetResponse> assets = new ArrayList<>();
    assetSlugs.forEach(
        assetSlug -> {
          try {
            final AssetResponse assetBySlug = getAssetBySlug(assetSlug);
            assets.add(
                CalenderEventDto.AssetResponse.builder()
                    .assetLink(assetBySlug.getLink())
                    .linkPath(assetBySlug.getLinkPath())
                    .linkType(assetBySlug.getLinkType())
                    .fileType(assetBySlug.getFileType())
                    .assetName(assetBySlug.getName())
                    .linkSource(assetBySlug.getLinkSource())
                    .pageType(assetBySlug.getPageType())
                    .assetType(assetBySlug.getAssetType())
                    .assetSlug(assetSlug)
                    .build());
          } catch (Exception ex) {
            log.error("Unable to asset link for asset slug: " + assetSlug, ex);
          }
        });
    return assets;
  }

  public com.wexl.retail.content.model.ChapterResponse getChapterBySlug(String chapterSlug) {
    try {
      String endPoint = "%s%s".formatted(url, chapterSlug);
      ResponseEntity<com.wexl.retail.content.model.ChapterResponse> responseEntity =
          restTemplate.exchange(
              endPoint,
              HttpMethod.GET,
              getRequestEntity(contentBearerToken),
              com.wexl.retail.content.model.ChapterResponse.class);
      if (HttpStatus.OK.equals(responseEntity.getStatusCode())
          && Objects.nonNull(responseEntity.getBody())) {
        return responseEntity.getBody();
      }
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, INVALID_CHAPTER, new String[] {chapterSlug});
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, INVALID_CHAPTER, e);
    }
  }

  public ChapterResponse getChapterById(long chapterId) {
    try {
      String endPoint = "%sorgs/%s/chapters/%s".formatted(url, Constants.WEXL_INTERNAL, chapterId);
      ResponseEntity<ChapterResponse> responseEntity =
          restTemplate.exchange(
              endPoint,
              HttpMethod.GET,
              getRequestEntity(contentBearerToken),
              com.wexl.retail.content.model.ChapterResponse.class);
      if (HttpStatus.OK.equals(responseEntity.getStatusCode())
          && Objects.nonNull(responseEntity.getBody())) {
        return responseEntity.getBody();
      }
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, INVALID_CHAPTER);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  public List<SubjectDetailResponse> getAllSubjects() {
    String endPoint = "%sorgs/%s/subjects?showAll=%s".formatted(url, Constants.WEXL_INTERNAL, true);
    try {
      ResponseEntity<List<SubjectDetailResponse>> responseEntity =
          restTemplate.exchange(
              endPoint,
              HttpMethod.GET,
              getRequestEntity(contentBearerToken),
              new ParameterizedTypeReference<>() {});
      return responseEntity.getBody();
    } catch (Exception e) {
      log.error("There was an error while getting subjects from content :" + e.getMessage(), e);
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  public SubjectResponse getSubjectBySubjectId(long subjectId) {
    try {
      String endPoint = "%ssubjects?type=null&id=%s".formatted(url, subjectId);
      ResponseEntity<List<Subjects>> responseEntity =
          restTemplate.exchange(
              endPoint,
              HttpMethod.GET,
              getRequestEntity(contentBearerToken),
              new ParameterizedTypeReference<>() {});
      if (!HttpStatus.OK.equals(responseEntity.getStatusCode())
          || Objects.isNull(responseEntity.getBody())
          || Objects.requireNonNull(responseEntity.getBody()).isEmpty()) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.SubjectGet.Id");
      }
      var subject = Objects.requireNonNull(responseEntity.getBody()).getFirst();
      assert subject != null;
      return SubjectResponse.builder()
          .id(subject.getSubjectId())
          .name(subject.getSubjectName())
          .slug(subject.getSubjectSlug())
          .build();
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.SubjectGet.Id", e);
    }
  }

  public com.wexl.retail.model.Organization getOrganizationBySlug(String slug) {
    try {
      String endPoint = "%sorgs/%s/org-details".formatted(url, slug);
      ResponseEntity<com.wexl.retail.model.Organization> responseEntity =
          restTemplate.exchange(
              endPoint,
              HttpMethod.GET,
              getRequestEntity(contentBearerToken),
              new ParameterizedTypeReference<>() {});
      if (!HttpStatus.OK.equals(responseEntity.getStatusCode())
          && Objects.isNull(responseEntity.getBody())) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidOrganizationSlug");
      }
      var organization = responseEntity.getBody();
      assert organization != null;
      return com.wexl.retail.model.Organization.builder()
          .id(organization.getId())
          .name(organization.getName())
          .slug(organization.getSlug())
          .orgType(organization.getOrgType())
          .settings(organization.getSettings())
          .status(organization.getStatus())
          .type(organization.getType())
          .build();
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.InvalidOrg", e);
    }
  }

  public List<com.wexl.retail.model.Organization> getAllOrganizations() {
    String endPoint = "%sorgs".formatted(url);
    ResponseEntity<List<com.wexl.retail.model.Organization>> responseEntity =
        restTemplate.exchange(
            endPoint,
            HttpMethod.GET,
            getRequestEntity(contentBearerToken),
            new ParameterizedTypeReference<>() {});
    if (!HttpStatus.OK.equals(responseEntity.getStatusCode())
        && Objects.isNull(responseEntity.getBody())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidOrganization");
    }
    return responseEntity.getBody();
  }

  public List<QuestionDto.SearchQuestionByUuidResponse> getAnswersByUuid(
      QuestionDto.SearchQuestionByUuid searchQuestionRequest, String orgSlug) {
    StringBuilder builder = new StringBuilder();
    for (int i = 0; i < searchQuestionRequest.uuid().size(); i++) {
      builder = new StringBuilder(builder + "&uuid=" + searchQuestionRequest.uuid().get(i));
    }
    String endPoint =
        "%sorgs/%s/answers?type=%s&%s"
            .formatted(url, orgSlug, searchQuestionRequest.type(), builder);
    var headers = new HttpHeaders();
    headers.add(HttpHeaders.AUTHORIZATION, contentBearerToken);
    var httpEntity = new HttpEntity<>(searchQuestionRequest, headers);
    try {
      ResponseEntity<List<QuestionDto.SearchQuestionByUuidResponse>> responseEntity =
          restTemplate.exchange(
              endPoint, HttpMethod.GET, httpEntity, new ParameterizedTypeReference<>() {});
      return responseEntity.getBody();
    } catch (Exception e) {
      log.error(
          "There was an error while getting answers by uuids from content :" + e.getMessage(), e);
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  public List<QpGenDto.QpGenResponse> getQuestionsForQpGen(
      List<QpGenDto.Sections> sections, String orgSlug) {
    String endPoint = "%s/orgs/%s/questions:qp-gen".formatted(url, orgSlug);
    var headers = new HttpHeaders();
    headers.add(HttpHeaders.AUTHORIZATION, contentBearerToken);
    var data = QpGenDto.ContentRequest.builder().sections(sections).build();
    var httpEntity = new HttpEntity<>(data, headers);
    ResponseEntity<List<QpGenDto.QpGenResponse>> responseEntity =
        restTemplate.exchange(
            endPoint, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<>() {});
    return responseEntity.getBody();
  }

  public List<QpGenDto.QpGenResponse> getQuestionsForQpGenPro(
      List<BluePrintDto.Section> sections, String orgSlug, List<String> chapterSlug) {
    String endPoint = "%s/orgs/%s/questions:qp-gen-pro".formatted(url, orgSlug);
    var headers = new HttpHeaders();
    headers.add(HttpHeaders.AUTHORIZATION, contentBearerToken);
    var data =
        QpGenProDto.ContentRequest.builder().chapterSlug(chapterSlug).sections(sections).build();
    var httpEntity = new HttpEntity<>(data, headers);
    ResponseEntity<List<QpGenDto.QpGenResponse>> responseEntity =
        restTemplate.exchange(
            endPoint, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<>() {});
    return responseEntity.getBody();
  }

  public List<ContentQuestionsResponse> getQuestionsByChapter(
      BluePrintDto.ContentRequest request, String orgSlug) {
    String endPoint = "%s/orgs/%s/chapterQuestions".formatted(url, orgSlug);
    var headers = new HttpHeaders();
    headers.add(HttpHeaders.AUTHORIZATION, contentBearerToken);

    var httpEntity = new HttpEntity<>(request, headers);
    try {
      ResponseEntity<List<ContentQuestionsResponse>> responseEntity =
          restTemplate.exchange(
              endPoint, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<>() {});
      return responseEntity.getBody();
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  public List<ChapterResponse> getChaptersByBoardGradeAndSubject(
      String orgSlug, String boardSlug, String gradeSlug, String subjectSlug) {
    try {
      String endPoint = "%s/orgs/%s/boards/%s/chapters".formatted(url, orgSlug, boardSlug);
      var uri =
          UriComponentsBuilder.fromUriString(endPoint)
              .queryParam("grade", gradeSlug)
              .queryParam("subject", subjectSlug)
              .queryParam("limit", 1500)
              .toUriString();

      ResponseEntity<List<ChapterResponse>> responseEntity =
          restTemplate.exchange(
              uri,
              HttpMethod.GET,
              getRequestEntity(contentBearerToken),
              new ParameterizedTypeReference<>() {});
      if (responseEntity.getStatusCode().is2xxSuccessful()
          && Objects.nonNull(responseEntity.getBody())) {
        return responseEntity.getBody();
      }
      return new ArrayList<>();
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.chapterNotFound");
    }
  }

  public List<SubTopicResponse> getSubTopicsByChapter(String orgSlug, String chapter) {
    String endpoint = "%sorgs/%s/subtopics".formatted(url, orgSlug);
    var uriString =
        UriComponentsBuilder.fromUriString(endpoint)
            .queryParam("chapterSlug", chapter)
            .toUriString();
    try {
      ResponseEntity<List<SubTopicResponse>> subTopicResponse =
          restTemplate.exchange(
              uriString,
              HttpMethod.GET,
              getRequestEntity(contentBearerToken),
              new ParameterizedTypeReference<>() {});
      return subTopicResponse.getBody();
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  public QuestionDto.SearchQuestionResponse getQuestionsBySubjectAndSubtopics(
      String orgSlug, QuestionType questionType, String subject, List<String> subtopics) {

    String endPoint = "%s/orgs/%s/questions".formatted(url, orgSlug);
    var uriString =
        UriComponentsBuilder.fromUriString(endPoint)
            .queryParam("type", questionType.name())
            .queryParam("subject", subject)
            .queryParam("subtopic", subtopics)
            .queryParam("questionType", "wexlQuestions")
            .queryParam("showDeletedQuestion", "false")
            .toUriString();

    try {
      ResponseEntity<QuestionDto.SearchQuestionResponse> responseEntity =
          restTemplate.exchange(
              uriString,
              HttpMethod.GET,
              getRequestEntity(contentBearerToken),
              QuestionDto.SearchQuestionResponse.class);
      return responseEntity.getBody();
    } catch (Exception exception) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, exception.getMessage(), exception);
    }
  }

  public List<AssetResponse> getAssetsBySynopsis(
      String chapter, String teacherAuthId, String orgSlug) {
    String endPoint = "%s/orgs/%s/teachers/%s/assets".formatted(url, orgSlug, teacherAuthId);
    var uriString =
        UriComponentsBuilder.fromUriString(endPoint)
            .queryParam("chapter", chapter)
            .queryParam("type", "synopsis")
            .queryParam("limit", 100)
            .toUriString();
    try {
      ResponseEntity<List<AssetResponse>> responseEntity =
          restTemplate.exchange(
              uriString,
              HttpMethod.GET,
              getRequestEntity(contentBearerToken),
              new ParameterizedTypeReference<>() {});
      return responseEntity.getBody();
    } catch (Exception exception) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, exception.getMessage(), exception);
    }
  }

  public List<AssetResponse> getAllAssets(String chapter, String teacherAuthId, String orgSlug) {
    String endPoint = "%s/orgs/%s/teachers/%s/assets".formatted(url, orgSlug, teacherAuthId);
    var uriString =
        UriComponentsBuilder.fromUriString(endPoint)
            .queryParam("chapter", chapter)
            .queryParam("limit", 100)
            .toUriString();
    try {
      ResponseEntity<List<AssetResponse>> responseEntity =
          restTemplate.exchange(
              uriString,
              HttpMethod.GET,
              getRequestEntity(contentBearerToken),
              new ParameterizedTypeReference<>() {});
      return responseEntity.getBody();
    } catch (Exception exception) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, exception.getMessage(), exception);
    }
  }

  public List<QPGenProV2Dto.QuestionsResponse> getQuestionsForQpGenProV2(
      QPGenProV2Dto.QuestionSummaryResponse questionSummary, String orgSlug) {
    if (Objects.isNull(questionSummary.sectionsResponses())
        || questionSummary.sectionsResponses().isEmpty()) {
      return Collections.emptyList();
    }
    String endPoint = "%s/orgs/%s/questions:qp-gen-pro-v2".formatted(url, orgSlug);
    var headers = new HttpHeaders();
    headers.add(HttpHeaders.AUTHORIZATION, contentBearerToken);
    var data = QPGenProV2Dto.ContentRequest.builder().questionSummary(questionSummary).build();
    var httpEntity = new HttpEntity<>(data, headers);
    ResponseEntity<List<QPGenProV2Dto.QuestionsResponse>> responseEntity =
        restTemplate.exchange(
            endPoint, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<>() {});
    return responseEntity.getBody();
  }

  public List<QPGenProV2Dto.QuestionCategory> getQuestionCategories(String orgSlug) {
    String endPoint = "%s/orgs/%s/question-categories".formatted(url, orgSlug);
    var headers = new HttpHeaders();
    headers.add(HttpHeaders.AUTHORIZATION, contentBearerToken);
    ResponseEntity<List<QPGenProV2Dto.QuestionCategory>> responseEntity =
        restTemplate.exchange(
            endPoint,
            HttpMethod.GET,
            getRequestEntity(contentBearerToken),
            new ParameterizedTypeReference<>() {});
    return responseEntity.getBody();
  }

  public List<QPGenProV2Dto.QuestionComplexities> getQuestionComplexities(String orgSlug) {
    String endPoint = "%s/orgs/%s/question-complexities".formatted(url, orgSlug);
    var headers = new HttpHeaders();
    headers.add(HttpHeaders.AUTHORIZATION, contentBearerToken);
    ResponseEntity<List<QPGenProV2Dto.QuestionComplexities>> responseEntity =
        restTemplate.exchange(
            endPoint,
            HttpMethod.GET,
            getRequestEntity(contentBearerToken),
            new ParameterizedTypeReference<>() {});
    return responseEntity.getBody();
  }

  public List<QuestionCountByOrg> getQuestionsCount(
      String org, List<String> orgSlugs, String fromDate, String toDate) {
    try {
      var uriComponent =
          UriComponentsBuilder.fromUriString(url)
              .path("/orgs/")
              .path(org)
              .path("/teachers-question-by-org")
              .queryParam("fromDate", fromDate)
              .queryParam("toDate", toDate)
              .build()
              .toUri();
      var headers = new HttpHeaders();
      headers.add(HttpHeaders.AUTHORIZATION, contentBearerToken);
      uriComponent = UriComponentsBuilder.fromUri(uriComponent).build().toUri();
      var httpEntity = new HttpEntity<>(orgSlugs, headers);
      ResponseEntity<List<QuestionCountByOrg>> responseEntity =
          restTemplate.exchange(
              uriComponent,
              HttpMethod.POST,
              httpEntity,
              new ParameterizedTypeReference<List<QuestionCountByOrg>>() {});
      return responseEntity.getBody();
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  public QuestionDto.SearchQuestionResponse getElpQuestions(
      ElpDto.Request request, String orgSlug) {
    String endPoint = "%s/orgs/%s/elp-questions".formatted(url, orgSlug);
    HttpHeaders headers;
    headers = new HttpHeaders();
    headers.add(HttpHeaders.AUTHORIZATION, contentBearerToken);
    var data = ElpDto.ContentRequest.builder().request(request).build();
    HttpEntity<ElpDto.ContentRequest> httpEntity;
    httpEntity = new HttpEntity<>(data, headers);
    ResponseEntity<QuestionDto.SearchQuestionResponse> responseEntity;
    responseEntity =
        restTemplate.exchange(
            endPoint, HttpMethod.POST, httpEntity, QuestionDto.SearchQuestionResponse.class);
    return responseEntity.getBody();
  }

  public List<QuestionDto.QuestionTags> getQuestionTags(String orgSlug) {
    String endPoint = "%s/orgs/%s/question-tags".formatted(url, orgSlug);
    var headers = new HttpHeaders();
    headers.add(HttpHeaders.AUTHORIZATION, contentBearerToken);

    ResponseEntity<List<QuestionDto.QuestionTags>> responseEntity =
        restTemplate.exchange(
            endPoint,
            HttpMethod.GET,
            getRequestEntity(contentBearerToken),
            new ParameterizedTypeReference<>() {});
    return responseEntity.getBody();
  }

  public List<ContentQuestionsResponse> getQuestionsByChapterTags(
      BluePrintDto.ContentRequestForQuestionTags request, String orgSlug) {
    String endPoint = "%s/orgs/%s/question-tags:count".formatted(url, orgSlug);
    var headers = new HttpHeaders();
    headers.add(HttpHeaders.AUTHORIZATION, contentBearerToken);
    var httpEntity = new HttpEntity<>(request, headers);
    try {
      ResponseEntity<List<ContentQuestionsResponse>> responseEntity =
          restTemplate.exchange(
              endPoint, HttpMethod.POST, httpEntity, new ParameterizedTypeReference<>() {});
      return responseEntity.getBody();
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  public QuestionDto.ValidateAnswerResponse validateAnswerResponse(
      String questionType,
      String uuid,
      String orgSlug,
      QuestionDto.BetAnswerValidateRequest studentAnswer) {

    final String endPoint =
        "%s/orgs/%s/answer-validation?type=%s&uuid=%s"
            .formatted(url, orgSlug, questionType.toUpperCase(), uuid);
    HttpHeaders headers = new HttpHeaders();
    headers.add(HttpHeaders.AUTHORIZATION, contentBearerToken);
    HttpEntity<QuestionDto.BetAnswerValidateRequest> httpEntity =
        new HttpEntity<>(studentAnswer, headers);

    try {
      ResponseEntity<QuestionDto.ValidateAnswerResponse> responseEntity =
          restTemplate.exchange(
              endPoint, HttpMethod.POST, httpEntity, QuestionDto.ValidateAnswerResponse.class);
      return responseEntity.getBody();
    } catch (Exception exception) {
      log.debug("Question not found with uuid " + uuid);
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, exception.getMessage(), exception);
    }
  }

  public QuestionDto.UploadQuestionsResponse uploadQuestions(
      List<QuestionDto.UploadQuestionsRequest> questionList,
      String orgSlug,
      String board,
      String grade,
      String subject) {

    final String endPoint =
        "%s/orgs/%s/questions:csv?board=%s&grade=%s&subject=%s"
            .formatted(url, orgSlug, board, grade, subject);
    HttpHeaders headers = new HttpHeaders();
    headers.add(HttpHeaders.AUTHORIZATION, contentBearerToken);
    var data = QuestionDto.contentRequest.builder().uploadedQuestions(questionList).build();
    HttpEntity<QuestionDto.contentRequest> httpEntity = new HttpEntity<>(data, headers);

    try {
      ResponseEntity<QuestionDto.UploadQuestionsResponse> responseEntity =
          restTemplate.exchange(
              endPoint, HttpMethod.POST, httpEntity, QuestionDto.UploadQuestionsResponse.class);
      return responseEntity.getBody();
    } catch (Exception exception) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, exception.getMessage(), exception);
    }
  }

  public QuestionDto.SearchQuestionResponse getQuestionsFromContent(
      QuestionDto.QuestionsPdfRequest questionsPdfRequest) {
    final String endPoint = String.format("%spublic/getQuestions", url);
    HttpEntity<QuestionDto.QuestionsPdfRequest> httpEntity = new HttpEntity<>(questionsPdfRequest);
    try {
      ResponseEntity<QuestionDto.SearchQuestionResponse> responseEntity =
          restTemplate.exchange(
              endPoint, HttpMethod.POST, httpEntity, QuestionDto.SearchQuestionResponse.class);
      return responseEntity.getBody();
    } catch (Exception exception) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, exception.getMessage(), exception);
    }
  }
}
