package com.wexl.retail.device.model;

import java.util.List;
import lombok.Data;

@Data
public class DeviceMetadata {

  // ANDROID

  // The user-visible SDK version
  private Integer sdkInt;

  // The user-visible version string.
  private String release;

  // The base OS build the product is based on.
  private String baseOS;

  private String securityPatch;
  private Integer previewSdkInt;
  private String incremental;
  private String codename;
  private String buildBoard;
  private String bootloader;
  private String brand;
  private String display;
  private String fingerprint;
  private String hardware;
  private String host;
  private String manufacturer;
  private String product;
  private String tags;
  private String type;
  private Boolean isPhysicalDevice;
  private String androidId;
  private List<String> systemFeatures;

  // IOS

  // Device Name
  private String name;

  // Name of current OS
  private String systemName;

  // Current operating system version
  private String systemVersion;

  // Device Model
  private String model;

  // Localizedname of the device model
  private String localizedModel;

  // UUID value identifying the current device
  private String identifierForVendor;
}
