package com.wexl.retail.courses.enrollment.service;

import static com.wexl.retail.commons.errorcodes.InternalErrorCodes.INVALID_REQUEST;
import static java.lang.String.format;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.courses.categories.repository.CourseCategoryRepository;
import com.wexl.retail.courses.definition.dto.CourseDefinitionResponse;
import com.wexl.retail.courses.definition.repository.CourseDefinitionRepository;
import com.wexl.retail.courses.definition.service.CourseDefinitionService;
import com.wexl.retail.courses.enrollment.dto.*;
import com.wexl.retail.courses.enrollment.model.*;
import com.wexl.retail.courses.enrollment.repository.CourseScheduleInstRepository;
import com.wexl.retail.courses.enrollment.repository.CourseScheduleItemInstRepository;
import com.wexl.retail.courses.enrollment.repository.CourseScheduleRepository;
import com.wexl.retail.courses.module.dto.CourseModuleResponse;
import com.wexl.retail.courses.module.model.CourseModule;
import com.wexl.retail.courses.module.repository.CourseModuleRepository;
import com.wexl.retail.courses.module.service.CourseModuleService;
import com.wexl.retail.courses.step.dto.CourseStepResponse;
import com.wexl.retail.courses.step.dto.StepCompletionRequest;
import com.wexl.retail.courses.step.model.CourseItemType;
import com.wexl.retail.courses.step.repository.CourseStepRepository;
import com.wexl.retail.courses.step.service.CourseItemFactory;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.notification.dto.TimeBombRequest;
import com.wexl.retail.notification.model.AvailableTimeBombJob;
import com.wexl.retail.notification.model.ReferenceType;
import com.wexl.retail.notification.service.PushNotificationService;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.storage.StorageService;
import com.wexl.retail.student.exam.ExamRepository;
import com.wexl.retail.student.exam.ExamResponse;
import com.wexl.retail.student.exam.ExamTransformer;
import com.wexl.retail.student.exam.school.SchoolExamService;
import com.wexl.retail.team.domain.Team;
import com.wexl.retail.team.repository.TeamRepository;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.test.school.repository.TestDefinitionRepository;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class CourseScheduleInstService {

  private final AuthService authService;
  private final UserService userService;
  private final DateTimeUtil dateTimeUtil;
  private final UserRepository userRepository;
  private final CourseScheduleRepository courseScheduleRepository;
  private final CourseItemFactory courseItemFactory;
  private final CourseModuleService courseModuleService;
  private final CourseStepRepository courseStepRepository;
  private final CourseModuleRepository courseModuleRepository;
  private final CourseDefinitionService courseDefinitionService;
  private final PushNotificationService pushNotificationService;
  private final CourseScheduleItemInstRepository courseScheduleItemInstRepository;
  private final CourseScheduleInstRepository courseScheduleInstRepository;
  private final TeamRepository teamRepository;
  private final CourseDefinitionRepository courseDefinitionRepository;
  private final CourseCategoryRepository courseCategoryRepository;

  private final OrganizationRepository organizationRepository;
  private final StudentRepository studentRepository;
  private final SchoolExamService schoolExamService;
  private final TestDefinitionRepository testDefinitionRepository;
  private final ExamRepository examRepository;
  private final ExamTransformer examTransformer;
  private final StorageService storageService;

  public void createCourseAndEnrollStudents(CourseEnrollmentRequest request, long courseDefId) {
    var courseSchedule = courseScheduleRepository.save(buildCourse(request, courseDefId));
    updateCourseScheduleStatusTimeBombEntry(courseSchedule);
    enrollStudentsToCourse(courseSchedule, request.getStudents(), courseDefId);
  }

  public void enrollStudentsToCourse(
      CourseSchedule courseSchedule, List<Long> studentIds, Long courseDefId) {
    var students = userRepository.fetchStudentsByIds(studentIds);
    var courseScheduleInstDate = Timestamp.from(Instant.now());

    var courseScheduleInsts =
        students.stream()
            .map(
                student -> buildCourseScheduleInst(courseSchedule, courseScheduleInstDate, student))
            .toList();

    courseScheduleInstRepository.saveAll(courseScheduleInsts);

    var courseModules = courseModuleRepository.getAllModulesAssociatedToCourse(courseDefId);

    var studentsCourseProgress = new ArrayList<CourseScheduleItemInst>();
    courseScheduleInsts.forEach(
        courseScheduleInst ->
            studentsCourseProgress.addAll(
                buildStudentCourseProgress(courseScheduleInst, courseModules)));

    courseScheduleItemInstRepository.saveAll(studentsCourseProgress);
  }

  public CourseDefinitionResponse getStudentCourseProgress(
      long courseDefId, long courseScheduleId, String studentAuthId) {

    var courseDefinition = courseDefinitionService.findCourseDefinitionById(courseDefId);
    var courseDefinitionResponse =
        courseDefinitionService.buildCourseDefinitionResponse(courseDefinition);

    var courseScheduleInst =
        courseScheduleInstRepository.getStudentEnrolledCourseById(
            userRepository.getUserByAuthUserId(studentAuthId).getId(), courseScheduleId);
    if (courseScheduleInst == null) {
      courseDefinitionResponse.setCourseKm(0.0);
      return courseDefinitionResponse;
    }
    courseDefinitionResponse.setStudentStatus(courseScheduleInst.getStatus().toString());

    courseDefinitionResponse.setModules(
        courseModuleRepository.getAllModulesAssociatedToCourse(courseDefId).stream()
            .map(
                courseModule -> {
                  var courseModuleResponse =
                      courseModuleService.buildCourseModuleResponse(courseModule);
                  List<CourseStepResponse> steps =
                      courseStepRepository
                          .getStudentEnrolledStepProgress(
                              courseScheduleId,
                              userRepository.getUserByAuthUserId(studentAuthId).getId(),
                              courseModule.getId())
                          .stream()
                          .map(courseItemFactory::buildCourseStepStudentProgress)
                          .toList();
                  courseModuleResponse.setSteps(steps);
                  courseModuleResponse.setCourseModuleKM(
                      buildCourseModuleKM(courseModuleResponse.getSteps()));
                  return courseModuleResponse;
                })
            .toList());
    if ((long) courseDefinitionResponse.getModules().size() > 0) {
      courseDefinitionResponse.setCourseKm(buildCourseKm(courseDefinitionResponse.getModules()));
    }
    return courseDefinitionResponse;
  }

  private Double buildCourseKm(List<CourseModuleResponse> modules) {
    double km;
    var sum = modules.stream().mapToDouble(CourseModuleResponse::getCourseModuleKM).sum();
    long count = modules.stream().filter(s -> s.getCourseModuleKM() != 0).count();
    km = sum == 0 ? 0.0 : Math.round(sum / count);
    return km;
  }

  private Double buildCourseModuleKM(List<CourseStepResponse> steps) {
    double km;
    var sum = steps.stream().mapToDouble(CourseStepResponse::getCourseStepKM).sum();
    long count = steps.stream().filter(s -> s.getCourseStepKM() != 0).count();
    km = sum == 0 ? 0.0 : Math.round(sum / count);
    return km;
  }

  @SneakyThrows
  public CourseSchedule findCourseScheduleById(long courseScheduleId) {
    return courseScheduleRepository
        .findById(courseScheduleId)
        .orElseThrow(
            () ->
                new ApiException(
                    INVALID_REQUEST,
                    "Course Schedule Not Found for %s".formatted(courseScheduleId)));
  }

  public void updateCourseScheduleInst(CourseEnrollmentRequest request, long courseScheduleId) {
    var courseSchedule = findCourseScheduleById(courseScheduleId);
    courseSchedule.setEndDate(dateTimeUtil.convertEpochToTimestamp(request.getEndDate()));
    courseSchedule.setStartDate(dateTimeUtil.convertEpochToTimestamp(request.getStartDate()));
    courseScheduleRepository.save(courseSchedule);
  }

  private List<CourseScheduleItemInst> buildStudentCourseProgress(
      CourseScheduleInst courseScheduleInst, List<CourseModule> courseModules) {

    var studentsCourseProgres = new ArrayList<CourseScheduleItemInst>();

    courseModules.forEach(
        module -> {
          var courseItems =
              courseStepRepository.getAllItemsAssociatedToCourseModule(module.getId());

          studentsCourseProgres.addAll(
              courseItems.stream()
                  .map(
                      courseItem ->
                          CourseScheduleItemInst.builder()
                              .courseScheduleInst(courseScheduleInst)
                              .courseItem(courseItem)
                              .orgSlug(courseScheduleInst.getOrgSlug())
                              .status(CourseScheduleItemInstStatus.NOT_STARTED)
                              .build())
                  .toList());
        });

    return studentsCourseProgres;
  }

  private CourseScheduleInst buildCourseScheduleInst(
      CourseSchedule courseSchedule, Timestamp courseScheduleInstDate, User student) {
    return CourseScheduleInst.builder()
        .courseSchedule(courseSchedule)
        .courseScheduleInstDate(courseScheduleInstDate)
        .orgSlug(student.getOrganization())
        .status(CourseScheduleInstStatus.NOT_STARTED)
        .studentId(student)
        .build();
  }

  public CourseSchedule buildCourse(CourseEnrollmentRequest request, long courseDefId) {

    return CourseSchedule.builder()
        .courseDefinition(courseDefinitionService.findCourseDefinitionById(courseDefId))
        .endDate(dateTimeUtil.convertEpochToTimestamp(request.getEndDate()))
        .startDate(dateTimeUtil.convertEpochToTimestamp(request.getStartDate()))
        .orgSlug(authService.getUserDetails().getOrganization())
        .teacherId(userService.findUserById(authService.getUserDetails().getId()))
        .metadata(
            CourseEnrollmentMetadata.builder()
                .sections(request.getSections())
                .grades(request.getGrades())
                .build())
        .build();
  }

  public void updateCourseCompletionStatus(CourseCompletionRequest request) {
    courseScheduleInstRepository.updateCourseCompletionStatus(
        authService.getStudentDetails().getId(),
        request.getCourseId(),
        CourseScheduleInstStatus.COMPLETED.toString());
  }

  public void updateStepCompletionStatus(StepCompletionRequest request) {
    courseScheduleItemInstRepository.updateStepCompletionStatus(
        authService.getStudentDetails().getId(),
        request.getCourseId(),
        request.getStepId(),
        CourseScheduleItemInstStatus.COMPLETED.toString());
  }

  public List<CourseScheduleDto.ScheduleResponse> getAllScheduledCourses(String orgSlug) {
    return courseScheduleInstRepository.getAllScheduledCourses(orgSlug).stream()
        .map(this::buildScheduleResponse)
        .toList();
  }

  private CourseScheduleDto.ScheduleResponse buildScheduleResponse(
      CourseEnrollmentSchedule response) {
    return CourseScheduleDto.ScheduleResponse.builder()
        .enrollments(response.getEnrollments())
        .courseDefinitionId(response.getCourseDefinitionId())
        .name(response.getName())
        .duration(response.getDuration())
        .build();
  }

  public List<CourseScheduleResponse> getEnrolledCourses(Integer courseCategoryId) {
    if (courseCategoryId != null) {
      return courseScheduleInstRepository
          .getStudentEnrolledCourses(authService.getUserDetails().getId())
          .stream()
          .filter(
              courseEnrollmentSchedule ->
                  Long.valueOf(courseCategoryId).equals(courseEnrollmentSchedule.getCategoryId()))
          .map(this::buildCourseScheduleResponse)
          .toList();
    }
    var unFilteredList =
        courseScheduleInstRepository.getStudentEnrolledCourses(
            authService.getUserDetails().getId());
    List<CourseScheduleResponse> finallist = new ArrayList<>();
    var filteredListToNotstartedAndEndTime =
        unFilteredList.stream()
            .filter(
                courseEnrollmentSchedule ->
                    DateTimeUtil.convertIso8601ToEpoch(
                            courseEnrollmentSchedule.getEndDate().toLocalDateTime())
                        >= DateTimeUtil.convertIso8601ToEpoch(LocalDateTime.now()))
            .filter(
                courseEnrollmentSchedule ->
                    courseEnrollmentSchedule.getStatus().equals("NOT_STARTED"))
            .map(this::buildCourseScheduleResponse)
            .toList();
    var filteredListToInprogress =
        unFilteredList.stream()
            .filter(
                courseEnrollmentSchedule ->
                    courseEnrollmentSchedule.getStatus().equals("IN_PROGRESS"))
            .map(this::buildCourseScheduleResponse)
            .toList();
    var filteredListToCompleted =
        unFilteredList.stream()
            .filter(
                courseEnrollmentSchedule ->
                    courseEnrollmentSchedule.getStatus().equals("COMPLETED"))
            .map(this::buildCourseScheduleResponse)
            .toList();
    var filteredListToEndTime =
        unFilteredList.stream()
            .filter(
                courseEnrollmentSchedule ->
                    DateTimeUtil.convertIso8601ToEpoch(
                            courseEnrollmentSchedule.getEndDate().toLocalDateTime())
                        < DateTimeUtil.convertIso8601ToEpoch(LocalDateTime.now()))
            .filter(
                courseEnrollmentSchedule ->
                    courseEnrollmentSchedule.getStatus().equals("NOT_STARTED"))
            .map(this::buildCourseScheduleResponse)
            .toList();
    finallist.addAll(filteredListToNotstartedAndEndTime);
    finallist.addAll(filteredListToInprogress);
    finallist.addAll(filteredListToCompleted);
    finallist.addAll(filteredListToEndTime);
    return finallist;
  }

  public List<CourseEnrollmentResponse> getAllStudentsEnrolledInCourse(long courseDefId) {
    return courseScheduleInstRepository.getAllStudentsEnrolledInCourse(courseDefId).stream()
        .map(this::buildCourseEnrollmentResponse)
        .toList();
  }

  private CourseEnrollmentResponse buildCourseEnrollmentResponse(StudentEnrollmentDetail response) {
    return CourseEnrollmentResponse.builder()
        .studentId(response.getStudentId())
        .studentAuthId(response.getStudentAuthId())
        .courseScheduleId(response.getCourseScheduleId())
        .status(response.getStatus())
        .enrollmentDate(
            DateTimeUtil.convertIso8601ToEpoch(response.getEnrollmentDate().toLocalDateTime()))
        .fullName(response.getFullName())
        .teamName(response.getTeamName())
        .teamId(response.getTeamId())
        .duration(response.getDuration())
        .build();
  }

  private CourseScheduleResponse buildCourseScheduleResponse(CourseEnrollmentSchedule response) {
    var courseScheduleResponse =
        CourseScheduleResponse.builder()
            .id(response.getId())
            .startDate(
                DateTimeUtil.convertIso8601ToEpoch(response.getStartDate().toLocalDateTime()))
            .endDate(DateTimeUtil.convertIso8601ToEpoch(response.getEndDate().toLocalDateTime()))
            .enrollments(response.getEnrollments())
            .courseDefinitionId(response.getCourseDefinitionId())
            .name(response.getName())
            .orgSlug(response.getOrgSlug())
            .version(response.getVersion())
            .status(response.getStatus())
            .categoryName(response.getCategoryName())
            .categoryId(response.getCategoryId())
            .thumbNail(
                response.getThumbnail() == null
                    ? response.getThumbnail()
                    : storageService.generatePreSignedUrlForFetch(response.getThumbnail()))
            .build();

    if (Objects.nonNull(response.getPublishedAt())) {
      courseScheduleResponse.setPublishedAt(
          DateTimeUtil.convertIso8601ToEpoch(response.getPublishedAt().toLocalDateTime()));
    }
    return courseScheduleResponse;
  }

  public void updateCourseScheduleStatusTimeBombEntry(CourseSchedule courseSchedule) {
    var timeBombRequest =
        TimeBombRequest.builder()
            .type(ReferenceType.COURSE_SCHEDULE)
            .jobToRun(AvailableTimeBombJob.UPDATE_COURSE_SCHEDULE_STATUS)
            .jobParams(Map.of("courseId", courseSchedule.getId()))
            .build();

    timeBombRequest.setExpiredAt(
        dateTimeUtil.subtractFifteenMinutesFromLocalDateTime(
            courseSchedule.getStartDate().toLocalDateTime()));
    timeBombRequest.setId(format("%s-%s", "IN_PROGRESS", courseSchedule.getId()));
    pushNotificationService.createTimeBombEntry(timeBombRequest);

    timeBombRequest.setExpiredAt(
        dateTimeUtil.subtractFifteenMinutesFromLocalDateTime(
            courseSchedule.getEndDate().toLocalDateTime()));
    timeBombRequest.setId(format("%s-%s", "COMPLETED", courseSchedule.getId()));
    pushNotificationService.createTimeBombEntry(timeBombRequest);
  }

  public void enrollTeamToCourses(
      CourseEnrollmentPersonRequest courseEnrollmentPersonRequest,
      Long courseDefId,
      String orgSlug) {

    validCourseCategory(orgSlug, courseEnrollmentPersonRequest);
    List<Long> studentIds = courseEnrollmentPersonRequest.getStudents();
    validStudents(studentIds);
    AssigneeMode assigneeMode = courseEnrollmentPersonRequest.getAssigneeMode();
    if (assigneeMode.toString().equals("TEAM")) {
      Team team =
          teamRepository.findByIdAndOrgSlug(courseEnrollmentPersonRequest.getTeamId(), orgSlug);
      validTeam(team);
      List<Student> students = team.getStudents();
      studentIds = students.stream().map(Student::getId).toList();
      if (studentIds.isEmpty()) {
        throw new ApiException(INVALID_REQUEST, "in this team students not available");
      }
    }
    List<Student> studentsDetails = studentRepository.findStudentListById(studentIds);
    for (var stu : studentsDetails) {
      if (!stu.getUserInfo().getOrganization().equals(orgSlug)) {
        throw new ApiException(INVALID_REQUEST, "invalid persons");
      }
    }
    List<Long> usersIds =
        studentsDetails.stream().map(student -> student.getUserInfo().getId()).toList();

    var courseDefinition = courseDefinitionRepository.findById(courseDefId);
    if (courseDefinition.isEmpty()) {
      throw new ApiException(INVALID_REQUEST, "invalid course");
    }
    courseDefinition.get().setDuration(courseEnrollmentPersonRequest.getDays());
    courseDefinitionRepository.save(courseDefinition.get());
    LocalDateTime startDate = LocalDateTime.now();
    LocalDateTime endDate = startDate.plusDays(courseEnrollmentPersonRequest.getDays());
    CourseEnrollmentRequest request = new CourseEnrollmentRequest();
    request.setEndDate(DateTimeUtil.convertIso8601ToEpoch(endDate));
    request.setStartDate(DateTimeUtil.convertIso8601ToEpoch(startDate));
    request.setStudents(usersIds);
    request.setCourseCategory(courseEnrollmentPersonRequest.getCourseCategory());
    request.setDuration(courseEnrollmentPersonRequest.getDays());
    createCourseAndEnrollStudents(request, courseDefId);
  }

  private void validCourseCategory(
      String orgSlug, CourseEnrollmentPersonRequest courseEnrollmentPersonRequest) {
    Organization organization = organizationRepository.findBySlug(orgSlug);
    var courseCategory =
        courseCategoryRepository.findByIdAndOrganization(
            courseEnrollmentPersonRequest.getCourseCategory(), organization);
    if (courseCategory == null) {
      throw new ApiException(
          InternalErrorCodes.SERVER_ERROR, "invalid course category please select valid category");
    }
  }

  private void validTeam(Team team) {
    if (Objects.isNull(team)) {
      throw new ApiException(INVALID_REQUEST, "Invalid Team Details");
    }
  }

  private void validStudents(List<Long> studentIds) {
    if (Objects.isNull(studentIds)) {
      throw new ApiException(INVALID_REQUEST, "student list cannot be empty");
    }
    if (studentIds.isEmpty()) {
      throw new ApiException(INVALID_REQUEST, "please select students");
    }
  }

  @Transactional
  public ExamResponse startCourseExam(long courseScheduleItemInstId) {
    final Optional<CourseScheduleItemInst> possibleCourseScheduleItemInst =
        courseScheduleItemInstRepository.findById(courseScheduleItemInstId);
    if (possibleCourseScheduleItemInst.isEmpty()) {
      throw new ApiException(INVALID_REQUEST, "error.invalidInput");
    }

    final var courseScheduleItemInst = possibleCourseScheduleItemInst.get();
    final var courseItem = courseScheduleItemInst.getCourseItem();

    if (CourseItemType.ASSIGNMENT.name().equals(courseItem.getItemType().name())
        || CourseItemType.SCHOOL_TEST.name().equals(courseItem.getItemType().name())) {
      return schoolExamService.createDirectAssignmentForCourse(
          courseItem.getTestDefinition().getId(), courseScheduleItemInst);
    }
    if (CourseItemType.MOCK_TEST.name().equals(courseItem.getItemType().name())) {
      return schoolExamService.createDirectAssignmentForCourse(
          courseItem.getTestDefinition().getId(), courseScheduleItemInst);
    }
    throw new ApiException(INVALID_REQUEST, "error.invalidInput");
  }

  public List<TeacherCourseDto.EnrollmentResponse> getAllTeachersEnrolledInCourse(
      long courseDefId) {
    return courseScheduleInstRepository.getAllTeachersEnrolledInCourse(courseDefId).stream()
        .map(this::buildTeacherCourseEnrollmentResponse)
        .toList();
  }

  private TeacherCourseDto.EnrollmentResponse buildTeacherCourseEnrollmentResponse(
      TeacherEnrollmentDetail response) {
    return TeacherCourseDto.EnrollmentResponse.builder()
        .teacherId(response.getTeacherId())
        .teacherAuthId(response.getTeacherAuthId())
        .courseScheduleId(response.getCourseScheduleId())
        .status(response.getStatus())
        .enrollmentDate(
            DateTimeUtil.convertIso8601ToEpoch(response.getEnrollmentDate().toLocalDateTime()))
        .fullName(response.getFullName())
        .teamName(response.getTeamName())
        .teamId(response.getTeamId())
        .duration(response.getDuration())
        .build();
  }
}
