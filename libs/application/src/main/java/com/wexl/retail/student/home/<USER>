package com.wexl.retail.student.home;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.student.exam.ExamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class HomeService {
  @Autowired AuthService authService;
  @Autowired private ExamService examService;

  public ActivitySummary fetchActivitySummary(String userName) {
    return examService.getStudentActivitySummary(userName);
  }
}
