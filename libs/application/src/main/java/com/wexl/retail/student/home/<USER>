package com.wexl.retail.student.home;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/orgs/{orgId}/students/{studentId}/")
public class HomeController {
  @Autowired private HomeService homeService;

  @GetMapping("activity-summary")
  public ActivitySummary activitySummary(@PathVariable String studentId) {
    return homeService.fetchActivitySummary(studentId);
  }
}
