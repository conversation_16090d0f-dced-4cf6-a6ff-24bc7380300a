package com.wexl.retail.proctoring.service;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.proctoring.dto.ProctoringDto;
import com.wexl.retail.proctoring.model.ProctoringSession;
import com.wexl.retail.proctoring.model.ProctoringStatus;
import com.wexl.retail.proctoring.repository.ProctoringRepository;
import com.wexl.retail.student.exam.Exam;
import com.wexl.retail.student.exam.ExamRepository;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.test.schedule.domain.ScheduleTestStudent;
import com.wexl.retail.test.schedule.dto.TestStudentStatus;
import com.wexl.retail.test.schedule.repository.ScheduleTestStudentRepository;
import com.wexl.retail.util.ValidationUtils;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Service
@RequiredArgsConstructor
public class ProctoringService {

  private final ProctoringRepository proctoringRepository;
  private final ValidationUtils validationUtils;
  private final UserService userService;
  private final DateTimeUtil dateTimeUtil;
  private final RestTemplate restTemplate;
  private final ScheduleTestStudentRepository scheduleTestStudentRepository;
  private final AuthService authService;
  private final ExamRepository examRepository;

  @Value("${app.shinkan.url:}")
  private String shinkanUrl;

  @Value("${app.shinkan.apikey:}")
  private String apiKey;

  @Value("${app.shinkan.enabled:false}")
  private boolean shinkanEnabled;

  public ProctoringDto.Response startProctoringSession(ProctoringDto.Request request) {
    try {
      var studentDetails = authService.getStudentDetails();
      var scheduleTestStudent =
          scheduleTestStudentRepository
              .findByStudentAndUuid(studentDetails, request.tssUuid())
              .orElseThrow(
                  () ->
                      new ApiException(
                          InternalErrorCodes.INVALID_REQUEST, "error.StudentTestNotFound"));

      var proctoringSession = validateProctoringSessionByExam(scheduleTestStudent);

      return ProctoringDto.Response.builder()
          .proctoringSessionId(proctoringSession.getId())
          .examId(proctoringSession.getExamId())
          .status(proctoringSession.getStatus())
          .startTime(
              dateTimeUtil.convertTimeStampToLong(
                  Timestamp.valueOf(proctoringSession.getStartTime())))
          .build();
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, e.getMessage(), e);
    }
  }

  private ProctoringSession validateProctoringSessionByExam(ScheduleTestStudent tss) {
    if (List.of(TestStudentStatus.SUBMITTED.name(), TestStudentStatus.COMPLETED.name())
        .contains(tss.getStatus())) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "Test already %s".formatted(tss.getStatus()));
    }
    var proctoringSession =
        proctoringRepository.findByTssUuidAndStatus(tss.getUuid(), ProctoringStatus.STARTED);
    return proctoringSession.orElseGet(
        () -> proctoringRepository.save(buildProctoringSession(tss)));
  }

  private ProctoringSession buildProctoringSession(ScheduleTestStudent testStudent) {
    var user = testStudent.getStudent();
    var student =
        validationUtils.validateStudentByAuthId(user.getAuthUserId(), user.getOrganization());
    var scheduleTest = testStudent.getScheduleTest();
    return ProctoringSession.builder()
        .startTime(testStudent.getStartTime())
        .testScheduleId(scheduleTest.getId())
        .testName(scheduleTest.getTestName())
        .status(ProctoringStatus.STARTED)
        .studentId(student.getId())
        .studentName(userService.getNameByUserInfo(user))
        .orgSlug(user.getOrganization())
        .tssUuid(testStudent.getUuid())
        .build();
  }

  public void updateProctoringStatus(final Exam exam) {
    final var student = exam.getStudent();
    var optionalSession =
        proctoringRepository.findByStudentIdAndTestScheduleId(
            student.getId(), exam.getScheduleTest().getId());
    optionalSession.ifPresent(
        proctoringSession -> {
          proctoringSession.setStatus(ProctoringStatus.SUBMITTED);
          proctoringSession.setExamId(exam.getId());
          proctoringSession.setEndTime(LocalDateTime.now());
          proctoringSession.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
          proctoringRepository.save(proctoringSession);
        });
  }

  private ProctoringSession validateProctoringSession(Long id) {
    return proctoringRepository
        .findById(id)
        .orElseThrow(
            () ->
                new ApiException(
                    InternalErrorCodes.INVALID_REQUEST, "error.ProctoringSessionNotFound"));
  }

  public ProctoringDto.ProctoringSessionResponse updateProctoringResult(
      String orgSlug, Long proctoringId) {
    var proctoringSession =
        proctoringRepository
            .findByIdAndOrgSlug(proctoringId, orgSlug)
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.INVALID_REQUEST, "error.ProctoringSessionNotFound"));

    if (List.of(ProctoringStatus.SUSPICIOUS, ProctoringStatus.HONEST)
        .contains(proctoringSession.getStatus())) {
      return buildProctoringSessionResponse(proctoringSession);
    }
    try {
      processProctoringSessionReportStatus(proctoringSession);
      return buildProctoringSessionResponse(proctoringRepository.save(proctoringSession));
    } catch (Exception e) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "Failed to fetch proctoring reports :%s".formatted(proctoringSession.getMessage()),
          e);
    }
  }

  private void processProctoringSessionReportStatus(ProctoringSession proctoringSession) {
    try {
      if (!shinkanEnabled) {
        proctoringSession.setMessage(
            "Proctoring report is not enabled, please contact the administrator");
        proctoringSession.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
        return;
      }
      var reportStatusRequest =
          ProctoringDto.ReportStatusRequest.builder()
              .testId(String.valueOf(proctoringSession.getTestScheduleId()))
              .studentId(String.valueOf(proctoringSession.getStudentId()))
              .build();
      final HttpHeaders httpHeaders = new HttpHeaders();
      httpHeaders.set("api_key", apiKey);
      httpHeaders.setContentType(MediaType.APPLICATION_JSON);
      var response =
          restTemplate.postForEntity(
              shinkanUrl,
              new HttpEntity<>(reportStatusRequest, httpHeaders),
              ProctoringDto.ProctoringResult.class);
      var proctoringResult = response.getBody();
      if (!response.getStatusCode().is2xxSuccessful() || Objects.isNull(proctoringResult)) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Failed to get reports");
      } else if (Boolean.FALSE.equals(Boolean.valueOf(proctoringResult.success()))) {
        proctoringSession.setMessage(proctoringResult.message());
        proctoringSession.setStatus(ProctoringStatus.IN_PROGRESS);
        return;
      }
      var responseData = proctoringResult.responseData();
      proctoringSession.setStatus(ProctoringStatus.valueOf(responseData.status()));
      proctoringSession.setVideoUrl(responseData.videoUrl());
      proctoringSession.setMessage(responseData.message());
      proctoringSession.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
    } catch (Exception e) {
      proctoringSession.setFailureReason(e.getMessage());
      proctoringSession.setStatus(ProctoringStatus.FAILED);
    }
  }

  private ProctoringDto.ProctoringSessionResponse buildProctoringSessionResponse(
      ProctoringSession proctoringSession) {
    return ProctoringDto.ProctoringSessionResponse.builder()
        .message(proctoringSession.getMessage())
        .status(proctoringSession.getStatus())
        .url(proctoringSession.getVideoUrl())
        .endTime(
            Objects.nonNull(proctoringSession.getEndTime())
                ? dateTimeUtil.convertTimeStampToLong(
                    Timestamp.valueOf(proctoringSession.getEndTime()))
                : null)
        .build();
  }
}
