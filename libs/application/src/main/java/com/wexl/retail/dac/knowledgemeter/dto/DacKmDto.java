package com.wexl.retail.dac.knowledgemeter.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.mlp.dto.KMGradesRequest;
import java.util.List;
import java.util.UUID;
import lombok.Builder;

public record DacKmDto() {
  @Builder
  public record DacKmResponse(
      @JsonProperty("org_name") String organization,
      @JsonProperty("average_percentage") Double averagePercentage,
      @JsonProperty("data") List<GeneralResponse> grades) {}

  @Builder
  public record GeneralResponse(
      String name,
      String slug,
      Integer order,
      @JsonProperty("section_summary") List<SectionResponse> sectionResponses,
      @JsonProperty("average_percentage") Double averagePercentage,
      @JsonProperty("subject_summary") List<DacKmDto.SubjectResponse> subjectResponses,
      @JsonProperty("exam_records") List<ExamRecords> examRecords) {}

  @Builder
  public record ExamRecords(@JsonProperty("exam_id") Long examId, String title) {}

  @Builder
  public record SectionResponse(
      String name,
      UUID uuid,
      @JsonProperty("average_percentage") Double averagePercentage,
      @JsonProperty("data") List<GeneralResponse> data) {}

  @Builder
  public record SubjectResponse(
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("subject_slug") String subjectSlug,
      @JsonProperty("subject_average") Double subjectAverage,
      List<ChapterResponse> chapters) {}

  @Builder
  public record ChapterResponse(
      Double average,
      @JsonProperty("chapter_name") String chapterName,
      @JsonProperty("chapter_slug") String chapterSlug,
      @JsonProperty("subject_name") String subjectName,
      List<GeneralResponse> subtopics) {}

  @Builder
  public record Data(@JsonProperty("data") List<String> subjects) {}

  @Builder
  public record KmRequest(Long examId) {}

  @Builder
  public record TestKmMigrationRequest(@JsonProperty("test_schedule_id") Long testScheduleId) {}

  @Builder
  public record OrgTestKnowledgeResponse(
      @JsonProperty("org_slug") String orgSlug,
      @JsonProperty("org_name") String orgName,
      @JsonProperty("knowledge_percentage") Long knowledgePercentage) {}

  @Builder
  public record gradeResponse(
      String name,
      @JsonProperty("average_percentage") Double averagePercentage,
      List<SubjectResponse> subjectResponses) {}

  @Builder
  public record BoardSummary(
      @JsonProperty("board_name") String boardName,
      @JsonProperty("board_slug") String boardSlug,
      @JsonProperty("summary_response") DacKmResponse summary) {}

  @Builder
  public record KMOverAllRequest(@JsonProperty("boards") List<BoardsRequest> boards) {}

  @Builder
  public record BoardsRequest(String name, String slug, List<KMGradesRequest> grades) {}
}
