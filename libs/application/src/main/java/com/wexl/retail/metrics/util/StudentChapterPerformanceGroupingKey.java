package com.wexl.retail.metrics.util;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

public class StudentChapterPerformanceGroupingKey {

  public final String chapterSlug;
  public final String chapterName;
  public final String questionType;
  public final String complexity;
  public final List<String> questionTags;

  public StudentChapterPerformanceGroupingKey(
      String chapterSlug,
      String chapterName,
      String questionType,
      String complexity,
      List<String> questionTags) {
    this.chapterSlug = chapterSlug;
    this.chapterName = chapterName;
    this.questionType = questionType;
    this.complexity = complexity;
    this.questionTags = questionTags != null ? questionTags : Collections.emptyList();
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    StudentChapterPerformanceGroupingKey that = (StudentChapterPerformanceGroupingKey) o;
    return Objects.equals(chapterSlug, that.chapterSlug)
        && Objects.equals(chapterName, that.chapterName)
        && Objects.equals(questionType, that.questionType)
        && Objects.equals(complexity, that.complexity)
        && Objects.equals(questionTags, that.questionTags);
  }

  @Override
  public int hashCode() {
    return Objects.hash(chapterSlug, chapterName, questionType, complexity, questionTags);
  }
}
