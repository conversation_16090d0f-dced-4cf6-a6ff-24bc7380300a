package com.wexl.retail.mlp.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MlpSummary {
  private String title;
  private String subjectName;
  private String sectionName;
  private String gradeName;
  private String chapterName;
  private String teacherName;
  private String subTopicName;
  private Integer attempted;
  private Integer notAttempted;
  private Integer attemptedSynopsis;
  private Integer notAttemptedSynopsis;
  private Integer attemptedVideo;
  private Integer notAttemptedVideo;
  private int gradeId;
  private int subjectId;
  private int chapterId;
  private String subTopicSlug;
  private String synopsisSlug;
  private String synopsisName;
  private String videoSlug;
  private String altVideoSlug;
  private String videoSha;
  private String videoSource;
  private Integer questionCount;
  private Long createdAt;
  private String examRef;
  private String organizationName;
  private float average;
  private Integer totalStudents;
  private List<MlpStudentDetail> detailedStudentMlpResponses;
}
