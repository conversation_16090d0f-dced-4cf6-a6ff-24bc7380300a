package com.wexl.retail.student.registration.service;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.student.registration.dto.StudentRegistrationRequest;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class StudentRegistrationService {

  @Value("${app.orgs.allowRegistrationForCommerce}")
  private List<String> allowRegistrationForCommerceOrgs;

  private final SignupFactory signupFactory;

  @Transactional
  public String createStudent(
      String orgSlug, final StudentRegistrationRequest studentRegistrationRequest) {
    if (!allowRegistrationForCommerceOrgs.contains(orgSlug)) {
      log.error("This org is not allowed to register students");
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.MissingMandatoryFields");
    }

    if (StringUtils.isBlank(studentRegistrationRequest.getEmail())
        || StringUtils.isBlank(studentRegistrationRequest.getPhoneNumber())) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.MissingMandatoryFields");
    }

    final DefaultStudentSignup defaultStudentSignup = signupFactory.signupBean(orgSlug);
    return defaultStudentSignup.createStudent(orgSlug, studentRegistrationRequest);
  }
}
