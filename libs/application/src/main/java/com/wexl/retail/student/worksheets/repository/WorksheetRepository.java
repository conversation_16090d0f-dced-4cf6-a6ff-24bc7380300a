package com.wexl.retail.student.worksheets.repository;

import com.wexl.retail.student.worksheets.dto.WorksheetResponse;
import com.wexl.retail.student.worksheets.dto.WorksheetSummaryResponse;
import com.wexl.retail.test.school.domain.TestDefinition;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface WorksheetRepository extends JpaRepository<TestDefinition, Long> {

  @Query(
      value =
          """
                  with test_chapters as (
                                    select tq.chapter_slug, td.id as test_definition_id, td.test_name, td.message,
                                td.no_of_questions
                                     from test_definitions td
                                     inner join test_definition_sections tds on tds.test_definition_id=td.id
                                     inner join test_questions tq on tq.test_definition_section_id=tds.id
                                    where td.organization = :providerSlug and tq.chapter_slug = :chapterSlug
                                and td.type = 'WORKSHEET'
                                    group by tq.chapter_slug, td.id
                                )
                                select td.test_definition_id as testDefinitionId, td.test_name as testName,
                                td.message as message, td.no_of_questions as noOfQuestions,
                                CASE
                                when e.end_time is not null then e.id
                                 else null
                                end as examId ,
                                 e.correct_answers as correctAnswers,
                                       coalesce(round(max(cast((e.correct_answers * 100.0 / e.no_of_questions)
                                as float))), 0) as percentage
                                from test_chapters td
                                left join exams e on e.test_definition_id = td.test_definition_id and e.student_id =
                                :studentId and e.test_definition_id is not null
                                group by td.test_definition_id, td.test_name, td.message,
                                td.no_of_questions, e.id, e.correct_answers""",
      nativeQuery = true)
  List<WorksheetResponse> getChapterWorksheets(
      String providerSlug, String chapterSlug, long studentId);

  @Query(
      value =
          """
                      with test_chapters as (
                                        select tq.chapter_slug, td.id as test_definition_id
                                        from test_definitions td
                                        inner join test_questions tq on td.id = tq.test_definition_id
                                        where td.organization = :providerSlug and td.subject_slug = :subject
                                          and td.grade_slug = :grade and td.board_slug = :board and td.type = 'WORKSHEET'
                                        group by tq.chapter_slug, td.id
                                    )
                                    select tc.chapter_slug as chapterSlug, count(tc.test_definition_id) as worksheetCount,\s
                                    count(e.id) as noOfWorksheetsCompleted
                                    from test_chapters tc
                                    left join exams e on e.test_definition_id = tc.test_definition_id and e.student_id =\s
                                    :studentId and e.test_definition_id is not null
                                    group by tc.chapter_slug""",
      nativeQuery = true)
  List<WorksheetSummaryResponse> getChapterWiseWorksheetSummary(
      String providerSlug, String subject, String grade, String board, long studentId);
}
