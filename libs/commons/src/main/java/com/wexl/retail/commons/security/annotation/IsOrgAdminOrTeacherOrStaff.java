package com.wexl.retail.commons.security.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import org.springframework.security.access.prepost.PreAuthorize;

@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@PreAuthorize("hasAnyRole('ROLE_ORG_ADMIN', 'ROLE_TEACHER', 'ROLE_ITEACHER','ROLE_STAFF')")
public @interface IsOrgAdminOrTeacherOrStaff {}
