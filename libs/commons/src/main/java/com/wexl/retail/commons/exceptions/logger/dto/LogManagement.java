package com.wexl.retail.commons.exceptions.logger.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;

public record LogManagement() {

  @Builder
  public record Request(
      String id,
      String datetime,
      String message,
      String user,
      @JsonProperty("org_slug") String orgSlug,
      Integer status,
      String email,
      @JsonProperty("stack_trace") String stackTrace) {}
}
