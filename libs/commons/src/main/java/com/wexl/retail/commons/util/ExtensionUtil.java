package com.wexl.retail.commons.util;

import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import java.util.AbstractMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
@Slf4j
public class ExtensionUtil {

  public static final Map<String, String> CONTENT_TYPE_EXTENSIONS =
      Map.ofEntries(
          new AbstractMap.SimpleEntry<>("audio/mpeg", "mp3"),
          new AbstractMap.SimpleEntry<>("audio/mp3", "mp3"),
          new AbstractMap.SimpleEntry<>("audio/wav", "wav"),
          new AbstractMap.SimpleEntry<>("audio/x-wav", "wav"),
          new AbstractMap.SimpleEntry<>("image/png", "png"),
          new AbstractMap.SimpleEntry<>("image/jpeg", "jpg"),
          new AbstractMap.SimpleEntry<>("image/jpg", "jpg"),
          new AbstractMap.SimpleEntry<>("image/gif", "gif"),
          new AbstractMap.SimpleEntry<>("image/svg+xml", "svg"),
          new AbstractMap.SimpleEntry<>("video/mp4", "mp4"),
          new AbstractMap.SimpleEntry<>("video/x-msvideo", "avi"),
          new AbstractMap.SimpleEntry<>("video/mpeg", "mpeg"),
          new AbstractMap.SimpleEntry<>("video/quicktime", "mov"),
          new AbstractMap.SimpleEntry<>("video/x-flv", "flv"),
          new AbstractMap.SimpleEntry<>("video/x-ms-wmv", "wmv"),
          new AbstractMap.SimpleEntry<>("video/3gpp", "3gp"),
          new AbstractMap.SimpleEntry<>("video/x-matroska", "mkv"));

  public void validate(String fileName) {
    String extension = StringUtils.getFilenameExtension(fileName);
    extension = Objects.isNull(extension) ? "png" : extension;
    validateImageExtensions(Extensions.valueOf(extension.toUpperCase()));
  }

  private void validateImageExtensions(Extensions extension) {
    if (!List.of(Extensions.JPEG, Extensions.PNG, Extensions.JPG, Extensions.MP3)
        .contains(extension)) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.ImageExtensionUnsupported");
    }
  }

  public static String getExtension(String contentType) {
    if (CONTENT_TYPE_EXTENSIONS.containsKey(contentType)) {
      return "." + CONTENT_TYPE_EXTENSIONS.get(contentType);
    }

    return ".png";
  }
}
