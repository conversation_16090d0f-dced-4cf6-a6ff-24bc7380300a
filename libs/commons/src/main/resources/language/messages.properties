error.serverError=Could not process the request
error.serverError.1=Unable to perform action.  Error {0}
error.invalidInput=Invalid Input
error.couldNotFindTaskInst=Could not find task inst
#MLP
error.Strapi.Service=Could not process the request.Cannot call strapi service
error.KnowledgeSubjectDetailsNotFound=Could not process the request.Could not get Knowledge subject details for {0}
error.NoEnrolledStudent=Invalid Input.There are no students who had enrolled for this subject in this section
error.QuestionUUIDsNotBeNullOrEmpty=Invalid Input.QuestionUUIDs cannot be null or empty
error.TeacherNotFound=Invalid Input.Teacher not found
error.SectionNotFound=Invalid Input.Section {0} not found.
error.MLPNotFound=Invalid Input.MLP not found.
error.StudentsNotAssignedMlp=Invalid Input.Students {0} are not assigned MLP
error.MlpFind.MLPId=Invalid Input.No mlps found for this Mlp ID.
error.MlpNotFound.ForChildOrg=Invalid Input. mlp not found for that organization {0}
error.OrganizationNotFound=Invalid Input.Organization not found.
error.MLPResponseNotAuthorized=Unauthorized Access.Access to MLP not Authorized.
error.StudentFind.Organization=Invalid Input.Student with username {0} not found in org {1} 
error.UnableToUpdateItemStatus=Resources Not Found.Unable to update the status of item {0} with status {1}
error.MLPFind.ExamRef=InvalidInput.MLP not found for the ExamRef.
error.MLPMetaDataFind.MLPID=Resources Not Found.Unable to fetch metadata for mlp with id {0}
error.MLPMetaDataFind.Slugs=Resourcse Not Found.Unable to fetch metadata for these slugs:Chapter Slug {0} & SubjectSlug {1}
error.FetchQuestionsFind.SubjectSlug=InvalidInput.Unable to fetch questions for subject: {0}  
error.OrganizationChild.Parent=Unauthorized Access.This organization {0} is not a child of {1}
error.TeacherUuid.ChildOrg=Unauthorized Access.This teacher {0} is not there in this organisation of {1}
error.MLP.Questions=Invalid Input.No practice questions assigned for this Mlp {0}
error.ExpiredOrInvalidToken=Unauthorized Access.Invalid or expired token
error.User.Configure=Invalid Input.User not configured correctly and Unknown Organization.
error.UserLogin.UserName=Unauthorized Access.Unable to login the user with {0}
error.User.Disable=Invalid Input. Your user is disabled.Please contact your administrator   
error.AuthUserID.Exist=Unauthorized Access.User with authUserID {0} doesnot exist in system.
error.User.Token=Unauthorized Access.User belong to {0} and Token to {1}.
error.UserName.Status=Unauthorized Access.User {0} is not in Verified Status.
error.InvalidUserEmail=Unauthorized Access.User  email {0} is not verified.
error.ParsePhoneNumber=Could not process the request.Unable to parse the given phone number {0}.
error.PhoneNumber=Invalid Input.PhoneNumber {0} is invalid.
error.MobileNumber.Login=Unauthorized Access.Unable to login using mobile Number {0}.
error.OTP=InvalidInput.Invalid otp.
error.MobileNumber.Verify=Invalid Input.Mobile number not verified. Please enter Otp again!
error.WrongCredentials=Invalid Input.Credentials are wrong or Empty.
error.DeletedOrg=Your institute is inactive. Please contact your administrator. 
error.InvalidCredentials=Invalid username or password.
error.ParseToken=Unauthorized Access.Unable to parse the requested token.
error.MobileNumber.Users=InvalidInput.Exceeded users with this mobile number.Please try different number.
error.StudentCombination.Device=InvalidInput. Your account is registered to a different device,contact institute administrator.
error.ProcessingAuth=Unauthorized Access.Unexpected error while processing authorization.
error.User.Null=Invalid Input. User is NUll.
error.AccessDenied=Unauthorized Access.  Access denied
error.authUserID.Find=Invalid Input. User with id {0} not found.
error.SectionFind.Organization=Invalid Input. Section with name {0} doesn't exist in the Organization {1}.
error.UserFind.Organization=Invalid Input. User with name {0} doesn't exist in the Organization {1}.
error.TeacherEmailFind.Organization=Invalid Input. Teacher with {0} doesn't exists in the organization{1}.
error.EduboardFind.SectionAndGrade=Invalid Input. Eduboard not found for student belonging to section {0} and grade{1}.
error.SectionScheduleFind.MeetingID=Invalid Input. Section Schedule with id {0} is not present.
error.AttendanceRecordFind.AttendnaceId=Invalid Input.No attendance record found for attendanceId:{0}.
error.appContext.Null=Invalid Input. Please Try again.
error.InvalidAttendanceId=Invalid Attendance Id
error.InvalidId=Invalid Section Attendance Details Id. Not Found {0}
error.Teacher.NotFound=Invalid Teacher.Teacher Not Found
error.CannotGetDetailsOfOrganization=Unauthorized Access.Cannot retrieve details of organization {0}.
error.OrgAdmin.Org=Unauthorized Access.Org Admin not available for organization {0}.
error.Classroom.Exists=Invalid Input. Classroom already exists with name {0}.
error.TeacherUnauthorized.Org=InvalidInput.Unauthorized teacher/teachers found for organization {0}.
error.StudentUnauthorized.Org=Invalid Input.Unauthorized student/students found for organization {0}.
error.ClassroomValidity.Org=Invalid Input. Invalid classroom for organization {0}.
error.ClassRoomValidity.ClassID=Invalid Input.Invalid classroom.
error.ClassRoomValidity.ClassIdAndOrg=Invalid Input.Invalid Classroom for ClassID {0} and organization {1} 
error.ConflictsWithBusyTeacher=InvalidInput. There is problem with Teachers {0}.
error.MeetingRoomFind.Org=Invalid Input. Could not find meeting room for organization {0}.
error.ClassroomScheduleValidity.Org=Invalid Input.Could not find class room schedule for organization {0}.
error.StudentGet.Schedules=Invalid Input. Could not get student schedules.
error.ClassRoomUpdate.Schedule=Couldnot process the Request.Unable to update classroom schedule
error.CannotGetChapter=Invalid Input. Cannot get chapter {0},from strapi.
error.NoOfQuestionsNotFound=Invalid Input. Number of Questions not found with that classId.
error.ClassFind.Input=Invalid Input. Class Not Found for given input. 
error.QuestionCountGet.CLassID=Invalid input. Cannot get question Count for Class Id :{0}.
error.SubTopicNotFound=Invalid Input. Subtopic {0} not found/Unexpected error.
error.CannotGetSQLQuery=Invalid Input. Unable to retrieve sql query from Strapi.
error.InvalidSlug=Invalid Input. Invalid Slug {0}.
error.ApplicationConfigFind.Referer=Invalid Input. Application Config Not Found for referer {0},Unexpected Error Occured.
error.GradeGet.Id=Invalid Input. Unable to fetch the grade by id {0}.
error.EduBoardFind.EduBoardId=Invalid Input. EduBoard Not Found / Unexpected Error Occured
error.InvalidGradeSlug=Invalid Input. Grade {0} is invalid.
error.InvalidBoard=Invalid Input. Invalid Board.
error.EduBoardFind.Board=Invalid Input. EduBoard Not Found for board {0}/ Unexpected Error Occured.
error.InvalidChapter=Invalid Input. Invalid Chapter.
error.InvalidAcademicYear=Invalid Input. Invalid AcademicYear.
error.SubjectGet.Slug=InvalidInput. Unable to fetch the subject by slug {0}.
error.SubjectGet.Id=InvalidInput. Unable to fetch the subject by Id {0}.
error.CannotCallStrapiService=Invalid Input.Cannot call Strapi service.
error.InvalidAssertSlug=Invalid Input.Invalid Assert {0} provided.
error.InvalidSubTopic=Invalid Input. Invalid Subtopic.
error.InvalidReferer=Invalid Input. Invalid Referer.
error.CannotFindConfiguration=Invalid Input. Unable to find the configuration for {0}
error.OrganizationFind.Slug=Invalid Input. Organization {0} not found.
error.InstituteName=Invalid Input. An institute with the same name exists. Please try another name.
error.MessagePayload=Unable to process the message Payload.
error.directory.file=Invalid Input. File needs to be a directory.
error.FileNotFound=Invalid Input. No file found for given input/invalid path.
error.InvalidCategory=Invalid Input. Invalid Category passed.
error.ThreadNotFound=Invalid Input. Thread Not Found.
error.organization.grade=Invalid Input. The organization {0} is not configured for grade {1}. 
error.SubjectsFound.duplication=Invalid Input. Duplication found in the selected subjects,Please remove it and save again.
error.SubjectProfileFind.Id=Invalid Input.  Can't find subject profile with id {0}.
error.SubjectAdd.Profile=Invalid Input. Subject is already added,select different subject.
error.student.profile=Invalid Input. Cannot delete profile,it is associated to some students.
error.SubjectDelete.Profile=Invalid Input. Add another subject before deleting this, subject profile cannot be empty.
error.SubjectFind.Profile=Resources error.SubjectProfileNotFoundById not Found. Unable to find the subject in profile {0}.
error.StudentFind.StudentName=Invalid Input.  Student with user name {0} not found.
error.StudentFind.AcademicYear=Invalid Input. Student is not present in the Academic Year.
error.PasswordResetFailed=Couldnot process the request. Failed to reset the password
error.StudentDelete.Profile=Invalid Input.Please add another profile to the student before deleting this.
error.ProfileFind.Student=Invalid Input. Unable to find this profile for this student
error.Delete.Profile=Couldnot process the request. Failed to delete the profile.
error.BoardOrSubject.Null=Invalid Input. BoardSlug or SubjectSlug cannot be null.
error.TeacherSubject.Mapped=Invalid Input. Teacher with subject{0} is already mapped.
error.Metadata.Fetch=Couldnot process the request. Unable to fetch metadata. 
error.BoardSlug.Exists=Couldnot Process the request. Board {0} does not exists.
error.CSV.generate=Couldnot Process the request. Unable to generate CSV. 
error.InvalidAttendnaceId=Invalid Request. Invalid Attendance Id.
error.UnAuthorized=Unauthorized Access. Request is Unauthorized.
error.StudentsNone.Attendance=Invalid Input. No Students to mark the attendance
error.Student.Unauthorized=Unauthorized Access.UnAuthorized User{0}.
error.InvalidSection=invalid Input.Invalid section {0}.
error.CannotFindSection=invalid Input. Unable to find sections.
error.GradeNotFound=Invalid Input. Grade not found.
error.fee.grade=Invalid Input. This fee is already scheduled for grade {0}
error.StudentNone.Grade=Invalid Input. No Students found in this grade {0}
error.invoices.grade=Couldnot process the request.Failed to create invoices for this grade
error.InvalidFeeSchedle=Invalid Input. Invalid Fee schedule {0}.
error.feeSchedule.view=Unauthorized Access. Not authorized to view the details of this fee schedule {0}.
error.MeetingLink.exists=InvalidInput. Meeting link already exists {0}.
error.MeetingName.exists=InvalidInput. Meeting Room name already exists {0}.
error.MeetingRoom.Update=Invalid Input. No Meeting room {0} available to update .
error.MeetingRoom.Delete=Invalid Input. No Meeting room {0} available to delete.
error.Meeting.CreateRequest=Couldnot process request. Unable to create meeting request.
error.MetricProcessing=Invalid Input. Unable to process metric with name {0}.
error.MetricProcessing.Name=Couldnot Process Request. Unable to process metric.           
error.services.organization=Invalid Intput.Unable to get services used for this organization.
error.TimeBomb.Request=Invalid Request.Unable to create a timebomb request
error.StudentUsername.Exist=Invalid Input. Student with username {0} doesnot exist.
error.ConfigurationValidity.Roles=Invalid Input.Invalid Configuration for role.
error.ParentEmail=Invalid Input.This parent email is already taken. Please choose another one!.
error.EmailNotFound=Invalid Input. Email id not valid.
error.SubjectProfiles.Configuration=Invalid Input. The subject profiles are configured incorrectly,contact Administrator.
error.StudentFind.AuthUserID=Invalid Input. Student user with authUserId {0}  not found.
error.Teacher.AuthUserID=Invalid Input. Teacher user with authUserId {0}  not found.
error.StudentNotFound=Invalid Input.Student Not Found.
error.StudentsNotFound=Invalid Input.No Students Found in the section
error.ManyStudentsFound=Invalid Input.Many Students Found With RollNumber {0}.
error.StudentNotFoundWithRollNumber=Invalid Input.No Students Found With RollNumber {0}.
error.StudentNotScheduledExam=Student with RollNumber {0} is not scheduled this exam.
error.UserNotFound=Invalid Input. User Not Found.
error.UserAlreadyActivated=User Already Activated.
error.InvalidActivationKey=Invalid Input. Invalid activation Key.
error.ParentNotFound=Invalid Input. Parent Not Found.
error.InvalidPassword=Invalid Input. Invalid Password.
error.StudentCreation.Failed=Invalid Input. Failed To create student.
error.StudentsGet.Failed=Invalid Input. Failed to get students.
error.StudentsGetInfo.Failed=Invalid Input. Failed to get student Information.
error.StudentsUpdateInfo.Failed=Invalid Input. Failed to update Student Information.
error.StudentDelete.Failed=Invalid Input. Failed to Delete Student. 
error.StudentUndelete.Failed=Invalid Input. Failed to undelete student.
error.UsernameExists=Invalid Input. Username already exists.
error.InvalidInput=Invalid Input. May be duplicated input provided,check the input.
error.section.grade=Invalid Input.section {0} doesnot exists in grade {1}.
error.StudentRoleNotFound=invalid input. Student Role Not Found.
error.ParentRoleNotFound=invalid input. Parent Role Not Found.
error.Email.Registered=Invalid intput. Email Id already Registered.
error.Institute.Registered=Invalid intput. School name already exist.
error.Abbrevation.exists=Invalid Input. Abbrevation already Exists.Please choose another.
error.InvalidEmailOTP=Invalid Input. Invalid Email-OTP.
error.InvalidMobileOTP=Invalid Input. Invalid Mobile-OTP.
error.OrgAdminCreationFailed=Invalid Input. Failed to create OrgAdmin.
error.InstituteName.Characters=Invalid Input. Institute Name must be min 3 characters.
error.CaptchaCode.null=Invalid Input. Captcha Code is null
error.ReCaptcha.Invalid=Invalid Input. Invalid ReCaptcha.
error.OrganizationEdit.Failed=Invalid Input. Failed to edit organization in Content
error.Section.Exists=Invalid Input. Section with name {0} already exists.
error.StudentsRemove.Section=Invalid Input. There are some students in this section {0}.Please remove them before deleting it.
error.Sections.Retreival=Invalid Input. Cannot retrive the students.
error.ServicesGet.Organization=Invalid Input. Unable to get services used for this organization.
error.organizationUpdate.Failed=Invalid Input. Failed to update organization
error.User.Analytics=Couldnot Process Request. Could not get User Analytics
error.CouldnotGetDetails=Couldnot process Request. Could not get Details.
error.Organization.Services=Invalid Input. Unable to get services used for this organization.
error.ParentRole=Invalid Input. Parent role not found.
error.InvalidCode=Invalid Input. Invalid or expired verification code!
error.Child.Details=Invalid Input. Invalid Child Details.
error.ExamResult.Access=Unauthorized Access.Not authorized to acesss the exam results
error.ExamId=Resources Not Found.Unable to find exam {0}.
error.CannotFindUser=Invalid Input.Unable to find user with username{0}.
error.Assesment.Started=Invalid Request.Assessment Not Started.
error.Assesment.Completed=Invalid Input.Assessment is Completed.
error.Assesment.Grade=Invalid Input. This assessment is not for your grade.
error.Assesment.Taken=Invalid Input.Assessment is already taken.
error.TestDefinition=Invalid Input. TestDefinition is not assigned with questions {0}.
error.Exam.Access=Unauthorized Access.Not authorized to access the exam.
error.PDFAnnotations=Invalid Inut. Unable to process pdf annotations!
error.InsufficientPoints=Invalid Input. Insufficient points. Please buy more points.
error.Payment.Details=Invalid Input. Invalid payment details.
error.InvalidRequest=Invalid Input. Invalid Request.
error.NoSubscriptionFound=Invalid Input. No Subscription Details found
error.Payment.Discount=Invalid Input.Total Discount is greater than payment. Cannot proceed.
error.Payment.Zero=Invalid Input. Total Payment Amount cannot be Zero.
error.Plan.Subscription=Invalid Input. Unable to find subscription of plan Id {0}.
error.RazorPay.Order=Invalid Input. Unable to create RazorPay Order.
error.NoRecordFound=Invalid Input. No Records Found.
error.User.Email=Invalid Input. User not found by email {0}.
error.Student.SubscriptionDetails=Invalid Input.Error while adding the subscription details for student{0}.
error.InvalidSubscription=Invalid Input. Invalid Subscription.
error.Payment.Info=Invalid Input. Invalid payment information. Payment signature not matching.
error.SubscriptionDetails=Invalid Input. Please add subscription details.
error.Student.Subscription=Invalid Input. Error while adding subscription for student {0} 
error.GiftCardNotFound=Invalid Input. Gift Card Not Found.
error.InvalidSubjectSlug=Invalid SubjectSlug {0}
error.StudentTeacher.Conflict=Invalid Input. There are conflicts for {0}, Are you sure  you want to proceed ?
error.Meeting.MeetingID=Invalid Input. Problem in retrieving the meeting with id {0}.
error.ZoomConfig.Organization=Could Not Process the Request. Configuration Issue. Unable to find valid zoom configuration for org {0}.
error.User.Inactive=Invalid Input. User is inactive. Cannot join the meeting.
error.Organization.Config=Invalid Input. Zoom SDK Configuration issue for the organization {0}
error.RolesNotFound=Invalid Input. Teacher/Org admin/Student/Parent/Manager Role not found
error.InvalidOrganization=Invalid Input. Invalid role/organization.
error.RoleNotFound=Resources Not Found.Role not found
error.Permission.added=Invalid Input. .Some of the permissions already added.Please give new permission.
error.OrganizationNotFound1=Resources Not Found. organization{0} not found.
error.TargetDirectory=Entry is outside of the target directory.
error.InvalidFile=Invalid Input. Invalid File.
error.UnZipFolder=Invalid Input.Unable to create an unzip folder.
error.HMAC.Generation=Failed to generate HMAC {0}.
error.File.Uploaded=Invalid Input.File is not uploaded.
error.File.Available=Invalid Input.The file is not available yet.
error.Validation.Class=Invalid Input. Validation Class.
error.urls.generate=could not process request.Unable to generate proper urls for the courseSchedule
error.InvalidUrl=Invalid Input. Invalid url
error.TestSchedule=Invalid Input.Test Schedule Not Found for {0}.
error.Students.ScheduleTest=Invalid Input. No Students to schedule the test
error.Students.RollNumberExists=Invalid Input. RollNumber already exists with {0}.
error.Test.Unauthorized=Invalid Input.You are not authorized to schedule this test.
error.TestDefinition1=Invalid Input. Test Definition Not Found for {0].
error.ExamNotFound=Invalid Input. Exam Not Found.
error.TestSchedule1=Invalid Input. Test Schedule is Invalid.
error.Test.Delete=Invalid Input. Cannot perform delete operation. The test is in {0} status.
error.Test.Find=Resources Not Found. Unable to find a test with {0}
error.DeleteTest.Unauthorized=Invalid Input. You are not authorized to delete this test
error.Test.Definition=Invalid Input. There are some tests scheduled using this test definition.Please delete them and retry.
errror.UserNotFound=Invalid Input. User Not Found for {0}.
error.TimeTableNotFound=TimeTable Not Found for section {0}.
error.TeamName.Exists=Invalid Input.Team Name already exits
error.TeamNotFound=Invalid Input.Team not found
error.Persons.Team=Invalid Input. Persons in the team cannot be empty.
error.NewPerson.Schedule=Could Not Process Request.Failed to schedule cours for the new persons of this team.
error.Student.Invalid=Invalid Input. student invalid.
error.Username.Exists=Invalid Input. Username already exist
error.UnableToProcess=Invalid Input. Unable to process your request. 
error.TeacherCreate.Email=Invalid Input. Cannot create a teacher with email {0}.
error.Code.Expired=Invalid Input. Invalid or expired verification code.
error.RoleNotFound1=Invalid Input.Teacher/Org admin Role not found.
error.OrgsDelete.Teacher=could not process request.Unable to delete orgs for given teacher.
error.ChildOrgNotFound=Invalid Input. Child Organization not found.
errror.UserNotFound1=Invalid Input.User not found.
error.StudentEdit.Unauthorized=Invalid Input. Not authorised to edit the student.
error.StudentGet.Failed=could not process request.Failed to get students.
error.StudentCreate.Failed=Could Not Process Request.Failed to create student. 
error.StudentUpdate.Failed=Could Not Process Request. Failed to update student information.
error.InvalidClassroom=Invalid Input. Invalid classroom
error.Organization.Task=Invalid Input.Invalid task for org {0}.
error.TaskNotFound=Invalid Input. Task not found
error.InvalidTask=Invalid Input. Invalid Task for org {0}.
error.QuestionsFind.Task=Invalid Input.No Questions found for task name {0}
error.Ticket.Create=Couldnot Process Request.Unable to create ticket in the system
error.AccessToken.generate=Couldnot Process Request.Unable to generate access token.
error.Ticket.Id=Invalid Input. No Ticket available with id {0}
error.RecordFind.Updation=Invalid Input. No record found for updation.
error.DocId.Null=Invalid Input. docId cannot be null.
error.FailedToCreatePerson.InvalidOrgConfig=Invalid Input. Failed to create person because of invalid organization configuration.
error.SectionFind.Grade=Resources Not Found. No sections found for this grade.
error.PersonNotFound=Invalid Input.Oops!! Person not found.
error.PersonIsDeleted=Invalid Input.Oops!! This person is deleted.
error.PersonDoesntExist=Invalid Input. Person doesn't exist.
error.InvalidPerson=Invalid Input.Invalid Person.
error.InvalidCR=Invalid Input. Invalid CR.
error.InvalidPromoCard=Invalid Input.Invalid or expired promocode!!
error.PromocodeUser.NotActive=Invalid Input.This promocode is already redeemed by the configured number of user.It is not active anymore.
error.PromocodeUser.NotActive1=Invalid Input.Promocode {0} already availed by user.Cannot use again
error.PromocodeUser.NotActive2=Invalid Input. Promocode {0} already availed by user for the student {1}. Cannot use again
error.PromocodeNotAvailable=Invalid Input.Promocode {0} is not available. There are {1} entries configured in backend for {2} and plan {3}.
error.PromocodeValidity.Student=Invalid Input.This Promocode {0} is not valid for this scenario.  There are no students for this parent.
error.PromocodeValidity.Subscriptions=Invalid Input.Parent has different subscriptions for different children. This Promocode {0} is not valid for this scenario. 
error.InvalidQRCode=Invalid Input. Invalid QrCode.
error.InvalidOTPId=Invalid Input. Invalid OtpId.Please verify!
error.MobileNumberNotVerified=Invalid Input. Mobile number not verified. Please verify!
error.OrganizationFind.Slug1=Resources Not Found.Cannot find organization for this slug.
error.QRCodeGeneration=CouldNot Process Request.Error on generating QR code.
error.StudentCreate.Failed1=Invalid Input, Failed to create student
error.InvalidReferralToken=Invalid Input. Invalid referral Token {0}.
error.ReferralNotFound=Invalid Input. No Referral found.
error.ReportsNotAvailable=Invalid Input.Reports Not Available
error.UnknownRole=Invalid Input.Unknown Role
error.ScheduleFind.Id=Invalid Input. Schedule not found for Id.
error.UnauthorizedAccess=Unauthorized Access.
error.InvalidUser=Invalid Input. Invalid User.
error.InvalidScromDefId=Invalid Input. Invalid scormDefinitionID
error.InvalidDefId=Invalid Input.Invalid definitionId.
error.UnableToConfigure=Couldnot Process Request.Unable to configure.
error.IncorrectResponseHeaders=Couldnot Process Request.Unable to add correct response headers
error.SubjectDelete.Teacher=Couldnot Process Request.Unable to delete subjects for given teacher.
error.SectionAlredayExists=Invalid Input. {0} this section already exits.activate this section in section menu.
error.InvalidGoalID=Invalid Input.Invalid GoalId.
error.StudentFind.StudentId=Invalid Input.Student Not Found for {0}.
error.StudentFind.User=Unauthorized Access.Unable to find student for user.
error.ParentRole.Config=Couldnot Process Request.Parent role not configured correctly.
error.ValueCannotBeZero=Value cannot be zero!.
error.InvalidAnswerId=InvalidInput.Invalid AnswerId.
error.Username.Registered=Invalid Input.Username already registered
error.InvalidVerificationCode=Invalid Input. Invalid or expired verification code!
error.ClassRoom.Delete=Invalid Input. this Meetingroom cant be deleted as it has classrooms assigned {0}
error.InvalidInput1=Invalid Input. Invalid input type provided
error.ClassroomSchedule.Update=Couldnot Process Request.Unable to update classroom schedule
error.Teacher.Exist=Invalid Input. Teacher doesn't exist!!
error.InvalidStudentDetails=Invalid Input.Invalid student details sent in the request.
error.UnknownStudentDetails=Invalid Input.Unknown student detail sent in the request
error.Calendar=Couldnot Process Request. Calendar not populated correctly. Please contact admin
error.CouldntProcessRequest=Couldnot Process the Request.
error.RazorPay=Invalid Input. Something Went Wrong.
error.Test.Taken=Invalid Input. Test is already taken.
error.ScheduledTest.Unauthorized=Unauthorized Access.Not authorized to access the scheduled test!
error.Exam.Completed=Invalid Input. Exam Already Completed!
error.Exam.NotStarted=The exam has not yet started. Please attempt the exam at {0}.
error.Exam.scheduleTime=Exam can only attempt in stipulated time.
error.Exam.Config=Invalid Input.Improper examId,Neither the ScheduleTest or TestDefinition is configured in the exam
error.StudentSubmit.Questions=Invalid Input.Student needs to submit all questions.
error.Questions.Display=Invalid Input.Questions not displaying | No question was answered
error.StudentAssigned.Test=Invalid Input. Schedule Test {0} was not assigned to student {1}.
error.TestEventDetails.Empty=Invalid Input. Test Event Details Response Data is empty.
error.Test.Allowed=Unauthorized Access.You are not allowed to take this test
error.WoohooGet.Status=Invalid Input. Cannot call woohoo get order status {0}
error.GiftCardOrder.Request=Invalid Input. Error while processsing giftcard order request.
error.InsufficientPoint=Invalid Input. Insufficient point!
error.ProcessingRequest=Invalid Input. Unable to process your request!
error.InvalidRewardTransaction=Invalid Input. Invalid Reward Transaction Id.
error.InvalidPaymentDetails=Invalid Input.Invalid payment details.
error.Wallet.Exist=Invalid Input. Wallet doesn't exists.
error.Strapi.SubTopics=Invalid Input.Cannot call Strapi get Subtopics.
error.Question.Media=Couldnot Process Request.Failed to upload media for questions
error.ScheduledClassroom.Delete=This Classroom can not be deleted, it has active schedules.
error.StudentsBySections.Organization=Could not find students for organization {0}.
error.SubjectProfile.manageStudents=Subject Profile {0} not found in your institute
error.InvalidNotification=Invalid Notification for Organization {0}.
error.InvalidPackageName=Invalid Input. Invalid PackageName {0}.
error.EduboardFind.Organization=Invalid Input. Invalid Board {0} for Organization or Board is empty.
error.SendOtpError=Could not send OTP to the mobile number {0}.
error.mobileUpdateError=Failed to login.
error.errorWithToken=Error with token. {0}
error.ErrorVerifyOtp=Error Verifying OTP to the mobile number {0}.
error.InvalidOtp=Invalid OTP to the mobile number {0}.
error.InvalidAssignmentId=Invalid Input. Invalid Assignments {0}.
error.AssignmentStatus=Invalid Input. Cannot get Assignments Status.
error.FailedUnSubmitExam="Could not unsubmit the Assignment "
error.RestrictMutlipleMlpAttempts="Mlp already Attempted"
error.studentPromote="Unable to promote a student [ {0} ]"
error.StudentAlreadyPromoted="student already promoted to grade [ {0} ]"
error.CourseIdsNotBeNullOrEmpty=Invalid Input.CourseIds cannot be null or empty
error.InvalidCourseIds=Invalid CourseIds {0}.
error.InvalidImageExtensions=Only jpeg, jpg and png image extensions are allowed
error.InvalidEnrollment=Student is not enrolled to this courseSchedule {0}.
error.SignupError=Signup Not Supported
error.MissingFields=Please enter all the fields.
error.MissingMandatoryFields=Please enter mandatory fields
Error.read.questions=Cannot read pre-configured questions
error.TestDefinitionNotPublished=Test Definition is not published
error.cannotPromoteClassroomStudents=Student is associated to a classroom.  Cannot perform promotion
error.invalid.courseitemtype=Unknown Course Item Type
error.Delete.Question=Couldnot process the request. Failed to delete the Question.
error.Delete.TestDefinitionSection=Couldnot process the request. Failed to delete the TestDefinitionSection.
error.NoTestSectionFound=Invalid Input.could not publish test without adding sections or questions in test {0}.
error.StudentTestNotFound=Scheduled test not Found
error.TestDefinition.UnPublish = This test is already scheduled. Cannot unpublish now
error.cannotPromoteCourseEnrolledStudents=Student is Assosiated to a course. Cannot perform promotion.
error.TestDefinitionPublish.Conflict= Defined number of questions is not matching with selected number of questions.Are you sure you still want to proceed further?
error.UnsupportedQuestionType=Invalid Input. UnSupported question type
error.cannotfindParent=Invalid Request. Parent data doesnot exists.
error.InvalidGuardian =Invalid Request. GuardianId doesnot exists.
error.InvalidTaskInst =Invalid Request. Task not found !
error.completedTask = Invalid Request. Could not delete the completed task
error.MulitpleSections=Invalid Request. Multiple sections are present for a grade in Wexl-Internal.
error.Teacher/AdminNotFound=Could not process request. Couldnot find org-admin for wexl-internal.
error.scheduleTest.UnAuthorized= This {0} feature is not configured. Please contact administrator
error.Invalid.CountryCode = Invalid Input. Invalid Country Code
error.Invalid.ClassroomsNotFound = Invalid Input. Classrooms not found by Parent classroom
error.FeedBack.SubjectFeedbackAlreadyExist=Invalid Input.  Feedback for this subject already exists
error.FeedBack.MonthlyFeedbackAlreadyExist=Invalid Input.  Feedback for this month already exists
error.Invalid.MessageTemplate = Invalid Input. Invalid MessageTemplate Id
error.File.Processing=Unable to process the file
error.Invalid.Csv = Invalid Input. please upload valid file
error.Invalid.MessageTemplateCategory = Invalid Input. Invalid MessageTemplateCategory Id
error.Invalid.MessageTemplateExits= Invalid Input. In message template exits category {0}
error.LocaleFileNotFound=Locale file {0} not found
error.Invalid.TimesUploaded=Invalid Input. File cannot be uploaded more than 1 time.
error.Invalid.Document = Invalid Input.Invalid Document Id {0}
error.Invalid.Document.owner = {0} is not authorized to edit the document uploaded by other user
error.Invalid.OfflineTestDefinition = Invalid Input.Invalid Offline TestDefinition Id {0}
error.Invalid.OfflineTestSchedule = Invalid Input.Invalid Offline TestSchedule Id {0}
error.ImageExtensionUnsupported = Invalid Input. Image type extension not supported
error.InvalidTestDefinition = Invalid Input. Test not found
error.InvalidSetNo = Invalid Input. Invalid Set No
error.InvalidStudentAttributeId = Invalid Input. InvalidStudent Attribute Id
error.Relationship.organization = Invalid Input. Student doesnot belong to same organization
error.Invalid.Product = product name already exists.
error.Invalid.ProductNotFound=  product not found.
error.Invalid.AssociateProductClassroom=  Could not delete the associate product .
error.Invalid.Applicant = applicant not fount.
InvalidElpBoard= Elp board is not configured for this organization. Please contact administrator
error.Invalid.ApplicantClassroom= applicant is already added.
#Competitive Exam Validations
error.Invalid.Sections.IIT=IIT Exam should have 6 sections. Cannot Publish.
error.Invalid.SectionNames.IIT=Section Names should be Physics Section A,Physics Section B,Chemistry Section A,Chemistry Section B,Mathematics Section A,Mathematics Section B. Cannot Publish.
error.Invalid.SectionQuestionCount.IIT=Section Question Count should be 20,10,20,10,20,10 respectively. Cannot Publish.
error.Invalid.QuestionType.IIT=Question Type should be {1} for Section {0}. Cannot Publish.
error.Invalid.Sections.NEET=NEET Exam should have 8 sections. Cannot Publish.
error.Invalid.SectionNames.NEET=Section Names should be Physics Section A,Physics Section B,Chemistry Section A,Chemistry Section B,Botany Section A,Botany Section B,Zoology Section A,Zoology Section B. Cannot Publish. Error:[{0}]
error.Invalid.SectionQuestionCount.NEET=Section Question Count should be 35,15,35,15,35,15,35,15 respectively. Cannot Publish. Error:Questions[{0}] in [{1}]
error.Invalid.QuestionType.NEET=Question Type should be {1} for Section {0}. Cannot Publish.
error.Invalid.SectionNames.EAMCET=Section Names should be Mathematics,Physics,Chemistry. Cannot Publish.
error.Invalid.Sections.EAMCET=Eamcet Exam should have 3 sections with counts - Mathematics(80) Physics(40) and Chemistry(40). Cannot Publish.
error.Invalid.SectionQuestionCount.EAMCET=Section Question Count should be 160. Cannot Publish.
error.Invalid.QuestionType.EAMCET=Question Type should be {1} for Section {0}. Cannot Publish.
error.Invalid.ClassroomSchedule= Selected classroom don't have schedules
error.Invalid.Sections.ELP=Elp Exam should have 5 section. Cannot Publish.
error.Invalid.SectionNames.ELP=Section Names should be Listening,Reading,Vocabulary,Speaking,Grammar. Cannot Publish.
error.Invalid.ELP.TestName=Test Name:{0} already exists in wexl-internal
error.Invalid.Elp.Question.Org=Question with uuid:{0} does not belong to Wexl-Internal.
error.speechGenericServerError=Speech Generic Server Error
error.speechError=Error: {0}
error.speechQuestionUnanswered=The answer to this question is not submitted
error.Invalid.GlobalProfile= Profile Name Already Exists.
error.InvalidGlobalProfile= Global Profile Not Found
error.Invalid.QuestionMarks= Marks for questions are not as per the standard of {0} in section {1}
error.Invalid.LiveClassSelected = please select valid schedule class
error.MeetingRoom.NotFound= No Meeting room available
error.classTeacherSection = Class Teacher Already exist for this section.
error.ClassTeacher = Teacher is already assigned as a class Teacher for other section.
error.MeetingRoomEmpty = Invalid meeting room.
error.GlobalFeature.exists = feature already exists.
error.Mobile.Registered=Invalid Input.Mobile number already registered
error.Mobile.NotMatching=Invalid Input.Mobile number not matching
error.Subject.Mapped = Subject is already mapped.
error.Invalid.RoleTemplate= RoleTemplate Already Exists.
error.Invalid.GradesByOrg= Could not get grades for this organization.
error.InvalidOrg.SelfSignup= This organization is  not mapped  for self signup,Please contact administrator
error.RoleTemplates.NotFound = Configure Admin,Teacher and Student role templates for the globalProfile name {0}.
error.RoleTemplate.Configure = Configure {0} role templates for the globalProfile name {1}.
error.InvalidScratchCode = Invalid scratch code
error.ScratchCode.AlreadyUsed= This scratch code already used
error.InvalidPasscode= Invalid Input. Invalid passcode
error.Send.Notification = Failed to send notification,{0} is not a class teacher for {1}
error.OrganizationUiConfig.Slug= Could not get ui config for organization {0}.
error.ThreadId=Invalid ThreadId : {0}
error.ThreadReplyId=Invalid Thread ReplyId : {0}
error.ThreadReplyComment=Invalid Thread Reply Comment Id: {0}
error.DeleteGlobalFeature=Deleting Global Feature is not allowed
error.DeleteGlobalProfile=Deleting Global Profile is not allowed
error.DeleteRoleTemplate=Deleting Role Template is not allowed
error.invalidRoleTemplate=Invalid Role Template
error.InvalidParameter=Invalid Parameter: {0}
error.Invalid.DiscountCode = Invalid DiscountCode
error.Invalid.TestDefinition = Invalid TestDefinition Id : {0}
error.SyllabusTrackingNotFound =Syllabus tracking not found
error.SyllabusTrackingNotInitiated=Syllabus trackings not initiated,Please contact administrator
error.InitiateSyllabusTrackings=Please click the refresh button to show chapters and subtopics
error.SyllabusTrackingAlreadyInitiated=Syllabus tracking already initiated
error.SyllabusTrackingError = Please initialize syllabus tracker.
error.ZeroDigitalNotFound= Invalid Input.Zero digital not found
error.ItemGroup= Invalid Input.ItemGroup not found
error.PdfGenerationIssue = Error while generating PDF: {0}
error.ZeroDigitalNotInitiated=Zero digital has not been initiated for this chapter
error.TestNotAssociated=Test has not been associated
error.Invalid.OfflineTestAssessmentSlug = Invalid Input. Invalid Assessment Type: {0}
error.Invalid.OfflineTestDefinition.Term = Assessment {0} already exist for the section {1}
error.InvalidStudentClassRollNumber=student already exist with this class rollnumber
error.subtopicsNotFound= Subtopics not found.
error.chapterNotFound = Chapter not found. Please upload them to initialize.
error.subtopicNotFound = Subtopic/Chapter not found. Please upload them to initialize.
error.AoAlreadyExists = Assessment objective  already exists
error.AssessmentNotFound = Assessment objective not found
error.InvalidTerm = Term not found
error.InvalidAoDetail = Invalid assessment objective detail
error.AprIdNotFound  = Academic performance id not found
error.AssignClassTeacher = No class teacher selected section
error.BluePrint.Id=Invalid Input. BluePrint Id {0} not found.
error.BluePrintSection.Id=Invalid Input. BluePrint Section Id {0} not found.
error.Invalid.QpGenProId=Invalid QpGen pro Id {0}
error.Empty.AoDetails = Add Assessment Objectives for the Selected AO.
error.AssessmentDetailNotFound = Invalid Assessment Objective
error.Invalid.Question.Uuid=Invalid Question UUID
error.unAuthorisedToPublish=You are not authorised to publish
error.TermAssessmentNotFound= Invalid Input. Term Assessment Id {0} not found.
error.AssessmentCategoryNotFound = Invalid Input. Term Assessment Category Id {0} not found.
error.DeleteBluePrint= Cannot Delete. This BluePrintId {0} is being used by QpGenPro.
error.DeleteBlueSection= Cannot Delete. This BluePrint Section {0} is being used by QpGenPro.
error.QPGen.QuestionCount = The number of questions requested in {0} is insufficient. Please contact administration to add more questions.
error.OfflineTestDefinitionId=Please generate the report card before downloading the overall report.
error.Invalid.InternalQuestion=Invalid Internal Question
error.ReportTemplateNotFound=Invalid Input.Report template not found
error.FeeNotPaid = Please contact the administration as your fee payment is due.
error.Invalid.HallTicket = {0} does not have any schedules. Cannot download hall ticket.
error.CouldNotGenerateAdmitCard= Unable to generate admit card.
error.InvalidReportJob=Report card job not found.
error.reportDataExists=Could not delete config detail as report data generated
error.ReportCardConfigDetailNotFound=Report config detail not found
error.noSmsDltIdFound = No Sms Template Found
error.DataNotUploaded=Report card cannot be downloaded. Data not uploaded for the respective grade.
error.lmrDataNotFound=Learning Milestones Report data not added.
error.sectionQuestionNotFound=No questions found in the section.
error.questionUuidNotFound=Question UUID Not found in the section.
error.SectionUuidNull=Section UUID cannot be null or empty.
error.downloadReportCard=Couldn't download the report without complete the test.
error.TestStudentNotFound=Student schedule test not found
error.cartNotFound=Cart not found
error.productNotFound=Product not found
error.cartItemNotFound=Cart item not found
error.invalid.cartItem=Cart item does not belong to the specified cart
error.invalidDiscountCode=Invalid Discount Code or expired.
error.OrderNotFound=Invalid Order
error.invalidDiscountAmount=The discount amount cannot exceed. Please provide a valid discount.
error.orderNotInitiated=Order not {0}.
error.productAlreadyAdded=Product already added in cart.
error.InvalidOrderItem=Invalid order item
error.Invalid.Bet.UserTypeId=Bet UserType Id is null
error.Bet.UserTypeId.NotFound=Invalid Bet UserType Id {0}
error.QpGenProNotFound=Invalid QpGenId {0}
error.ReportCardError=Unable to download Report Card
error.Bet.SectionId.NotFound=Invalid Bet Section Id {0}
error.Bet.SectionInstId.NotFound=Invalid Bet Section Inst Id {0}
error.Bet.SectionUnitInstId.NotFound=Invalid Bet Section Unit Inst Id {0}
error.Bet.SectionUnitLessonInstId.NotFound=Invalid Bet Section Unit Lesson Inst Id {0}
error.Bet.SectionUnitLessonId.NotFound=Invalid Bet Section Unit Lesson Id {0}
error.Bet.SectionUnitId.NotFound=Invalid Bet Section Unit Id {0}
error.Bet.Category.NotFound=Invalid Bet Category {0}
error.NoTestDefinition = No scheduled tests available. Please check back later
error.BetSections.NotFound = Bet Sections not found. Please contact your administrator.
error.CouldNotStartProctoringSession= Could not start proctoring session.
error.ProctoringSessionNotFound= Proctoring session not found
error.Refresh = Please refresh to download report
error.TeacherTimeTableNotFound = Teacher Time Table not found.
error.TeacherTimeTableDetailNotFound = Teacher Time Table detail not found.
error.InvalidTemplate = Invalid Message Template.
error.FeeMaster.NotFound = Fee master not found with id:{0}
error.Fee.InvalidScopeType = Invalid Scope Type:{0}