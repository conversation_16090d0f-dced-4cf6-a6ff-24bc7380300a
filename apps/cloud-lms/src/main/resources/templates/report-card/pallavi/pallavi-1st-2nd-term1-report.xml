<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="invoice">
            <fo:region-body margin="12mm" />
        </fo:simple-page-master>
    </fo:layout-master-set>
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="0cm" border="2pt solid black" padding="4mm">
                <fo:block-container absolute-position="absolute" top="30%" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="300%" content-height="300%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="25"/> <!-- Adjust the slope value to change brightness -->
                                            <feFuncG type="linear" slope="25"/>
                                            <feFuncB type="linear" slope="25"/>
                                        </feComponentTransfer>
                                    </filter>
                                </defs>
                                <image filter="url(#brightnessFilter)" x="0" y="0" width="100%" height="100%" xlink:href="https://dpsgurugram84.com/wp-content/uploads/2019/07/logo_dps.png"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:block text-align="center" font-size="18pt" font-weight="bold" space-before="50pt" font-family="Arial">
                    PALLAVI MODEL SCHOOL
                </fo:block>
                <fo:block text-align="center" font-size="11pt" font-weight="bold" space-before="20pt" font-family="Arial">
                    BOWENPALLY / ALWAL / GANDIPET / SAROOR NAGAR / TIRUMALAGIRI
                </fo:block>
                <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial" space-before="10pt">
                    <fo:external-graphic src='url("https://dpsgurugram84.com/wp-content/uploads/2019/07/logo_dps.png")'
                                         content-width="25mm" content-height="scale-to-fit"  />
                </fo:block>
                <fo:block text-align="center" font-size="16pt" font-weight="bold" space-before="20pt" font-family="Arial">
                    My Achievement Profile
                </fo:block>
                <fo:table border="none">
                    <fo:table-column column-width="50%" />
                    <fo:table-column column-width="50%" />
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell font-weight="bold" padding-left="50mm" padding-top="10mm">
                                <fo:block>Name </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding-top="10mm">
                                <fo:block font-weight="bold" >:  <fo:inline th:text="${model.body.name}"></fo:inline></fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row>
                            <fo:table-cell font-weight="bold" padding-left="50mm" padding-top="10mm">
                                <fo:block>Class / Section </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding-top="10mm">
                                <fo:block font-weight="bold" >:  <fo:inline th:text="${model.body.class}"></fo:inline></fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row>
                            <fo:table-cell font-weight="bold" padding-left="50mm" padding-top="10mm">
                                <fo:block>Student Id </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding-top="10mm">
                                <fo:block font-weight="bold" >:  <fo:inline th:text="${model.header.studentId}"></fo:inline></fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row>
                            <fo:table-cell font-weight="bold" padding-left="50mm" padding-top="10mm">
                                <fo:block>Admission No </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding-top="10mm">
                                <fo:block font-weight="bold" >:  <fo:inline th:text="${model.body.admissionNumber}"></fo:inline></fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row>
                            <fo:table-cell font-weight="bold" padding-left="50mm" padding-top="10mm">
                                <fo:block>Address </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding-top="10mm">
                                <fo:block font-weight="bold" >:  <fo:inline th:text="${model.body.address}"></fo:inline></fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row>
                            <fo:table-cell font-weight="bold" padding-left="50mm" padding-top="10mm">
                                <fo:block>Contact No. </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding-top="10mm">
                                <fo:block font-weight="bold" >:  <fo:inline th:text="${model.body.contactNumber}"></fo:inline></fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%"  margin-top="0cm" border="2pt solid black" padding="4mm">
                <fo:block border-width="1mm" font-size="10pt" font-family="Times New Roman, serif">
                    <fo:table border="1pt solid black" margin="0cm">
                        <fo:table-column column-width="127mm" />
                        <fo:table-column column-width="60mm" />
                        <fo:table-header>
                            <fo:table-row >
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left" number-rows-spanned="2">
                                    <fo:block>Social Emotional Learning</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>Reflective Practices</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row font-size="9pt">
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  >
                                    <fo:block>TERM - I</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body font-size="9pt">
                            <fo:table-row th:each="marks : ${model.body.firstTable.marks}">
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${marks.socialemotionalLearning}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.term1}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block border-width="1mm" font-size="9pt" space-before="5pt" font-family="Times New Roman, serif" th:each="table : ${model.body.reportTables}">
                    <fo:block th:text="${table.title}" font-size="8" font-weight="bold" font-family="Times New Roman, serif"></fo:block>
                    <fo:table border="1pt solid black" margin="0cm" text-align="center">
                        <fo:table-column column-width="74mm" />
                        <fo:table-column column-width="28mm" />
                        <fo:table-column column-width="28mm" />
                        <fo:table-column column-width="28mm" />
                        <fo:table-column column-width="28mm" />
                        <fo:table-header>
                            <fo:table-row >
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block>Learning Outcomes</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>PT1</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>PT2</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>PT3</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>PT4</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body font-size="9pt">
                            <fo:table-row th:each="marks : ${table.marks}">
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${marks.learningOutcomes}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.pt1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.pt2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.pt3}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.pt4}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block border-width="1mm" font-size="9pt"  space-before="5pt" font-family="Times New Roman, serif" text-align="center" >
                    <fo:block th:text="${model.body.secondTable.title}" text-align="center" font-size="9" font-weight="bold" font-family="Times New Roman, serif"></fo:block>
                    <fo:table border="1pt solid black">
                        <fo:table-column  column-width="20mm" />
                        <fo:table-column column-width="42mm" />
                        <fo:table-column column-width="15mm"  />
                        <fo:table-column column-width="15mm"  />
                        <fo:table-column column-width="16mm"  />
                        <fo:table-column column-width="15mm"  />
                        <fo:table-column column-width="15mm"  />
                        <fo:table-column column-width="15mm"  />
                        <fo:table-column column-width="18mm"  />
                        <fo:table-column column-width="15mm"  />
                        <fo:table-header font-size="9pt" >
                            <fo:table-row >
                                <fo:table-cell border="none" font-weight="bold" padding="2mm" text-align="left" number-rows-spanned="2">
                                    <fo:block>S.NO.</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="2mm" text-align="left" number-rows-spanned="2">
                                    <fo:block>SUBJECT</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  number-columns-spanned="6" >
                                    <fo:block >TERM-I</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  number-columns-spanned="2" >
                                    <fo:block >OVERALL</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row font-size="7pt">
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block th:text="${model.body.secondTable.column.pt1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block th:text="${model.body.secondTable.column.ma1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block th:text="${model.body.secondTable.column.cwHw1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block th:text="${model.body.secondTable.column.pt2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block >TOTAL (100)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block >Grade</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block >OVER ALL TOTAL (100)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block >Grade</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body font-size="7pt">
                            <fo:table-row th:each="marks : ${model.body.secondTable.marks}" >
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left" font-weight="bold">
                                    <fo:block th:text="${marksStat.index + 1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left" font-weight="bold">
                                    <fo:block th:text="${marks.subject}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.pt1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.ma1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.cwHw1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.pt2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${marks.term1total}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.term1Grade}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.overallMarks}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.overallGrade}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block border-width="1mm" font-size="9pt" space-before="5" text-align="center" font-family="Times New Roman, serif" >
                    <fo:table border="1pt solid black" >
                        <fo:table-column column-width="13mm" />
                        <fo:table-column column-width="61mm" />
                        <fo:table-column column-width="28mm" />
                        <fo:table-column column-width="28mm" />
                        <fo:table-column column-width="28mm" />
                        <fo:table-column column-width="28mm" />
                        <fo:table-header>
                            <fo:table-row >
                                <fo:table-cell border="none" font-weight="bold" padding="2mm" text-align="left" number-rows-spanned="2">
                                    <fo:block>S.NO.</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="2mm" text-align="left"  number-rows-spanned="2">
                                    <fo:block>SUBJECT</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" number-columns-spanned="2">
                                    <fo:block>TERM-I</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm"  number-columns-spanned="2">
                                    <fo:block >OVERALL</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row font-size="7pt">
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>MARKS(100)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  >
                                    <fo:block>GRADE</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>MARKS(100)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  >
                                    <fo:block>GRADE</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body font-size="7pt">
                            <fo:table-row th:each="marks : ${model.body.thirdTable.marks}">
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left" font-weight="bold">
                                    <fo:block th:text="${marksStat.index + 1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left" font-weight="bold">
                                    <fo:block th:text="${marks.subjectName}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.term1Marks}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.term1Grade}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.overallMarks}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.overallGrade}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block border-width="1mm" font-size="9pt"  space-before="15"  font-family="Times New Roman, serif">
                    <fo:block th:text="${model.body.fourthTable.title}" font-size="9" text-align="center" font-weight="bold" font-family="Times New Roman, serif"></fo:block>
                    <fo:table border="1pt solid black" text-align="left"  >
                        <fo:table-column column-width="50%" />
                        <fo:table-column column-width="50%" />
                        <fo:table-header>
                            <fo:table-row >
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>Subject</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  >
                                    <fo:block>Term 1</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body  font-size="7pt">
                            <fo:table-row th:each="marks : ${model.body.fourthTable.marks}" font-weight="bold">
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${marks.subject}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.term1}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block border-width="1mm" font-size="9pt"  space-before="15"  font-family="Times New Roman, serif">
                    <fo:block th:text="${model.body.fourthTable.title}" font-size="9" text-align="center" font-weight="bold" font-family="Times New Roman, serif"></fo:block>
                    <fo:table border="1pt solid black" margin="0cm" text-align="center">
                        <fo:table-column column-width="74mm" />
                        <fo:table-column column-width="28mm" />
                        <fo:table-column column-width="28mm" />
                        <fo:table-column column-width="28mm" />
                        <fo:table-column column-width="28mm" />
                        <fo:table-header>
                            <fo:table-row >
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block>Global Perspective </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>PT1</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>PT2</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>PT3</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>PT4</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body font-size="9pt">
                            <fo:table-row th:each="marks : ${model.body.fifthTable.marks}">
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${marks.globalPerspective}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.pt1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.pt2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.pt3}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.pt4}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block border-width="1mm" font-size="9pt" space-before="5" text-align="center" font-family="Times New Roman, serif" >
                    <fo:table border="1pt solid black" >
                        <fo:table-column  column-width="60mm" />
                        <fo:table-column column-width="25mm" />
                        <fo:table-column column-width="25mm" />
                        <fo:table-column column-width="25mm" />
                        <fo:table-column column-width="25mm" />
                        <fo:table-column column-width="25mm" />
                        <fo:table-header>
                            <fo:table-row >
                                <fo:table-cell border="none" font-weight="bold" padding="2mm" text-align="left" number-rows-spanned="3">
                                    <fo:block>Theme</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="2mm" text-align="center"  number-columns-spanned="5">
                                    <fo:block>Check for Understand Assessment</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row font-size="8pt" text-align="left">
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>Factual</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  >
                                    <fo:block>Understanding</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>Application</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  >
                                    <fo:block>Critical Thinking</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>Creativity</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row font-size="7pt" text-align="left">
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>Students can answer questions beginning with what/where/who and when</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  >
                                    <fo:block>Students can compare/describe</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>Students can solve/modify Students can compare/describe</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  border="1pt solid black" font-weight="bold" padding="1mm"  >
                                    <fo:block>Students can point out/discuss pros and cons/state opinion/suggest alternate/predict/imp</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>Students can suggest,plan and design</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body font-size="7pt" text-align="left">
                            <fo:table-row th:each="marks : ${model.body.sixthTable.marks}">
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left" font-weight="bold">
                                    <fo:block th:text="${marks.theme}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.factual}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.understanding}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.application}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.criticalThinking}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.creativity}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block font-size="9" text-align="center"  space-before="5" font-weight="bold" font-family="Times New Roman, serif">TERM-I Observation</fo:block>
                <fo:block border="1pt solid black" margin="2mm" padding="2mm" font-size="10pt" font-family="Times New Roman, serif">
                    <fo:block th:text="${model.body.term1Observation}">
                    </fo:block>
                </fo:block>
                <fo:block border-width="1mm" font-size="9pt"  space-before="15"  font-family="Times New Roman, serif">
                    <fo:block th:text="${model.body.seventhTable.title}" font-size="9" text-align="center" font-weight="bold" font-family="Times New Roman, serif"></fo:block>
                    <fo:table border="1pt solid black" margin="0cm" text-align="center">
                        <fo:table-column column-width="30mm" />
                        <fo:table-column column-width="13mm" />
                        <fo:table-column column-width="13mm" />
                        <fo:table-column column-width="13mm" />
                        <fo:table-column column-width="13mm" />
                        <fo:table-column column-width="13mm" />
                        <fo:table-column column-width="13mm" />
                        <fo:table-column column-width="13mm" />
                        <fo:table-column column-width="13mm" />
                        <fo:table-column column-width="13mm" />
                        <fo:table-column column-width="13mm" />
                        <fo:table-column column-width="13mm" />
                        <fo:table-column column-width="13mm" />
                        <fo:table-header>
                            <fo:table-row >
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block>Month</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>Mar</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>Apr</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>May</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>Jun</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>Jul</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>Aug</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>Sept</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>Oct</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>Nov</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>Dec</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>Jan</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>Feb</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body font-size="9pt">
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block>Total No. of workign days</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${model.body.seventhTable.months.mar.totalWorkingDays}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${model.body.seventhTable.months.apr.totalWorkingDays}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${model.body.seventhTable.months.may.totalWorkingDays}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${model.body.seventhTable.months.jun.totalWorkingDays}">Jun</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black"  padding="1mm">
                                    <fo:block th:text="${model.body.seventhTable.months.jul.totalWorkingDays}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${model.body.seventhTable.months.aug.totalWorkingDays}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${model.body.seventhTable.months.sept.totalWorkingDays}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${model.body.seventhTable.months.oct.totalWorkingDays}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${model.body.seventhTable.months.nov.totalWorkingDays}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${model.body.seventhTable.months.dec.totalWorkingDays}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${model.body.seventhTable.months.jan.totalWorkingDays}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black"  padding="1mm">
                                    <fo:block th:text="${model.body.seventhTable.months.feb.totalWorkingDays}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block>No. of Present days</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${model.body.seventhTable.months.mar.totalPresentDays}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${model.body.seventhTable.months.apr.totalPresentDays}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${model.body.seventhTable.months.may.totalPresentDays}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${model.body.seventhTable.months.jun.totalWorkingDays}">Jun</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${model.body.seventhTable.months.jul.totalPresentDays}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${model.body.seventhTable.months.aug.totalPresentDays}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${model.body.seventhTable.months.sept.totalPresentDays}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${model.body.seventhTable.months.oct.totalWorkingDays}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${model.body.seventhTable.months.nov.totalWorkingDays}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${model.body.seventhTable.months.dec.totalPresentDays}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${model.body.seventhTable.months.jan.totalPresentDays}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${model.body.seventhTable.months.feb.totalPresentDays}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block border-width="1mm" font-size="9pt"  space-before="15"  font-family="Times New Roman, serif">
                    <fo:block font-size="9" text-align="center" font-weight="bold" font-family="Times New Roman, serif">Grading Scale</fo:block>
                    <fo:table border="1pt solid black" margin="0cm" text-align="center">
                        <fo:table-column column-width="30mm" />
                        <fo:table-column column-width="30mm" />
                        <fo:table-column column-width="30mm" />
                        <fo:table-column column-width="30mm" />
                        <fo:table-column column-width="30mm" />
                        <fo:table-column column-width="35mm" />
                        <fo:table-body font-size="10pt">
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block>O - Outstanding</fo:block>
                                    <fo:block>(81 - 100)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block>E - Excelling</fo:block>
                                    <fo:block>(61 - 80.99)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block>A - Achieving</fo:block>
                                    <fo:block>(51 - 60.99)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block>D - Developing</fo:block>
                                    <fo:block> (41 - 50.99)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block>B - Beginning </fo:block>
                                    <fo:block>(0 - 40.99)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding-left="1mm" padding="2mm 0" text-align="left">
                                    <fo:block>NA - Not Applicable</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block border-width="1mm" font-size="9pt"  space-before="25" space-after="25" font-family="Times New Roman, serif">
                    <fo:table>
                        <fo:table-column />
                        <fo:table-column />
                        <fo:table-column />
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell padding="0 2mm" font-weight="bold">
                                    <fo:block>Class Teacher</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="center" font-weight="bold">
                                    <fo:block>H.M</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding-left="5mm" text-align="right" font-weight="bold">
                                    <fo:block>Principal</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
</fo:root>