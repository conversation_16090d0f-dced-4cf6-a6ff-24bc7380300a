<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="invoice">
            <fo:region-body margin="18mm" />
        </fo:simple-page-master>
    </fo:layout-master-set>

    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="0cm" border="2pt solid black"
                                padding="6mm">
                <fo:block-container absolute-position="absolute" top="30%" left="50%" width="0%"
                                    height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="300%" content-height="300%">
                            <svg xmlns="http://www.w3.org/2000/svg"
                                 xmlns:xlink="http://www.w3.org/1999/xlink" width="100%"
                                 height="100%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="25" />
                                            <feFuncG type="linear" slope="25" />
                                            <feFuncB type="linear" slope="25" />
                                        </feComponentTransfer>
                                    </filter>
                                </defs>
                                <image filter="url(#brightnessFilter)" x="0" y="0" width="100%"
                                       height="100%"
                                       xlink:href="" />
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:table >
                    <fo:table-column column-width="20mm" />
                    <fo:table-column column-width="140mm" />
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block>
                                    <fo:external-graphic
                                            src='url("https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/saisenior_logo.jpeg")'
                                            content-width="15mm" content-height="scale-to-fit" />
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block font-size="14pt" font-weight="bold"
                                          font-family="Times New Roman, serif" text-align="center"
                                          space-after="3pt"
                                          th:text="${#strings.toUpperCase(model.header.schoolName)}">
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold"
                                          font-family="Times New Roman, serif" text-align="center"
                                          space-after="5pt"
                                          th:text="${#strings.toUpperCase(model.header.reportName)}">
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold"
                                          font-family="Times New Roman, serif" text-align="center"
                                          space-after="5pt"
                                          th:text="${#strings.toUpperCase(model.header.academicYear)}">
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block border="0.3mm solid black" border-left="6mm solid black" border-right="6mm solid black" ></fo:block>
                <fo:block space-before="5" font-size="8pt"
                          font-family="Times New Roman, serif"
                          space-after="5pt">
                    <fo:table border="1pt solid black" >
                        <fo:table-column column-width="20%" />
                        <fo:table-column column-width="33%" />
                        <fo:table-column column-width="20%" />
                        <fo:table-column column-width="27%" />
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold" border-bottom="1pt solid black"
                                               padding="2mm 1mm">
                                    <fo:block>STUDENT'S NAME</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border-right="1pt solid black"
                                               border-bottom="1pt solid black" padding="2mm 1mm">
                                    <fo:block>: <fo:inline
                                            th:text="${model.body.name}"></fo:inline></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" border-bottom="1pt solid black"
                                               padding="2mm 1mm">
                                    <fo:block>EDU ID</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border-right="1pt solid black"
                                               border-bottom="1pt solid black" padding="2mm 1mm">
                                    <fo:block >: <fo:inline
                                            th:text="${model.body.rollNumber}"></fo:inline></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold" border-bottom="1pt solid black"
                                               padding="2mm 1mm">
                                    <fo:block>FATHER'S NAME</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border-right="1pt solid black"
                                               border-bottom="1pt solid black" padding="2mm 1mm">
                                    <fo:block >: <fo:inline
                                            th:text="${model.body.fathersName}"></fo:inline></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" border-bottom="1pt solid black"
                                               padding="2mm 1mm">
                                    <fo:block>CLASS</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border-right="1pt solid black"
                                               border-bottom="1pt solid black" padding="2mm 1mm">
                                    <fo:block >: <fo:inline
                                            th:text="${model.body.className}"></fo:inline></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold" border-bottom="1pt solid black"
                                               padding="2mm 1mm">
                                    <fo:block>MOTHER'S NAME</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border-right="1pt solid black"
                                               border-bottom="1pt solid black" padding="2mm 1mm">
                                    <fo:block >: <fo:inline
                                            th:text="${model.body.mothersName}"></fo:inline></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" border-bottom="1pt solid black"
                                               padding="2mm 1mm">
                                    <fo:block>DATE OF BIRTH</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border-right="1pt solid black"
                                               border-bottom="1pt solid black" padding="2mm 1mm">
                                    <fo:block>: <fo:inline
                                            th:text="${model.body.dateOfBirth}"></fo:inline></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block border-width="1mm" space-before="8" font-size="9pt" font-family="Times New Roman, serif"
                          text-align="center">
                    <fo:table border="1pt solid black">
                        <fo:table-column column-width="59mm" />
                        <fo:table-column column-width="23mm" />
                        <fo:table-column column-width="23mm" />
                        <fo:table-column column-width="23mm" />
                        <fo:table-column column-width="23mm" />
                        <fo:table-column column-width="23mm" />
                        <fo:table-header font-size="9pt">
                            <fo:table-row>
                                <fo:table-cell border="none" font-weight="bold" padding="1mm">
                                    <fo:block>SCHOLASTIC AREAS</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold"
                                               padding="1mm" number-columns-spanned="5">
                                    <fo:block>TERM-I</fo:block>
                                </fo:table-cell>

                            </fo:table-row>
                            <fo:table-row font-size="8pt">
                                <fo:table-cell border="1pt solid black" font-weight="bold"
                                               padding="1mm" font-size="9pt">
                                    <fo:block>SUBJECT</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold"
                                               padding="1mm">
                                    <fo:block th:text="${model.body.firstTable.column1}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold"
                                               padding="1mm">
                                    <fo:block th:text="${model.body.firstTable.column2}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold"
                                               padding="1mm">
                                    <fo:block th:text="${model.body.firstTable.column3}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold"
                                               padding="1mm">
                                    <fo:block>ACADEMICS</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold"
                                               padding="1mm">
                                    <fo:block>OVERALL%</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body font-size="9pt">
                            <fo:table-row th:each="marks : ${model.body.firstTable.subject1}">
                                <fo:table-cell  padding="1mm"
                                                text-align="left" font-weight="bold" border-right="1pt solid black">
                                    <fo:block th:text="${marks.subjectName}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  padding="1mm" border-right="1pt solid black">
                                    <fo:block th:text="${marks.term1FA}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  padding="1mm" border-right="1pt solid black">
                                    <fo:block th:text="${marks.term1SA}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  padding="1mm" border-right="1pt solid black">
                                    <fo:block th:text="${marks.term1IA}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  padding="1mm" border-right="1pt solid black">
                                    <fo:block th:text="${marks.grade == 0.0 ? '' : marks.grade}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  padding="1mm" border-right="1pt solid black">
                                    <fo:block th:text="${marks.percentage == 0.0 ? '' : marks.percentage}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell padding="1mm" border-bottom="1pt solid black" number-columns-spanned="6" padding-top="-1mm">
                                    <fo:block></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row th:each="marks : ${model.body.firstTable.subject2}">
                                <fo:table-cell  padding="1mm"
                                                text-align="left" font-weight="bold" border-right="1pt solid black">
                                    <fo:block th:text="${marks.subjectName}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  padding="1mm" border-right="1pt solid black">
                                    <fo:block th:text="${marks.term1FA}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  padding="1mm" border-right="1pt solid black">
                                    <fo:block th:text="${marks.term1SA}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  padding="1mm" border-right="1pt solid black">
                                    <fo:block th:text="${marks.term1IA}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  padding="1mm" border-right="1pt solid black">
                                    <fo:block th:text="${marks.grade == 0.0 ? '' : marks.grade}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  padding="1mm" border-right="1pt solid black">
                                    <fo:block th:text="${marks.percentage == 0.0 ? '' : marks.percentage}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell padding="1mm" border-bottom="1pt solid black" number-columns-spanned="6" padding-top="-1mm">
                                    <fo:block></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row th:each="marks : ${model.body.firstTable.subject3}">
                                <fo:table-cell  padding="1mm"
                                                text-align="left" font-weight="bold" border-right="1pt solid black">
                                    <fo:block th:text="${marks.subjectName}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  padding="1mm" border-right="1pt solid black">
                                    <fo:block th:text="${marks.term1FA}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  padding="1mm" border-right="1pt solid black">
                                    <fo:block th:text="${marks.term1SA}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  padding="1mm" border-right="1pt solid black">
                                    <fo:block th:text="${marks.term1IA}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  padding="1mm" border-right="1pt solid black">
                                    <fo:block th:text="${marks.grade == 0.0 ? '' : marks.grade}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  padding="1mm" border-right="1pt solid black">
                                    <fo:block th:text="${marks.percentage == 0.0 ? '' : marks.percentage}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell padding="1mm" border-bottom="1pt solid black" number-columns-spanned="6" padding-top="-1mm">
                                    <fo:block></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row th:each="marks : ${model.body.firstTable.subject4}">
                                <fo:table-cell  padding="1mm"
                                                text-align="left" font-weight="bold" border-right="1pt solid black">
                                    <fo:block th:text="${marks.subjectName}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  padding="1mm" border-right="1pt solid black">
                                    <fo:block th:text="${marks.term1FA}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  padding="1mm" border-right="1pt solid black">
                                    <fo:block th:text="${marks.term1SA}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  padding="1mm" border-right="1pt solid black">
                                    <fo:block th:text="${marks.term1IA}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  padding="1mm" border-right="1pt solid black">
                                    <fo:block th:text="${marks.grade == 0.0 ? '' : marks.grade}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  padding="1mm" border-right="1pt solid black">
                                    <fo:block th:text="${marks.percentage == 0.0 ? '' : marks.percentage}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell padding="1mm" border-bottom="1pt solid black" number-columns-spanned="6" padding-top="-1mm">
                                    <fo:block></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row th:each="marks : ${model.body.firstTable.subject5}">
                                <fo:table-cell  padding="1mm"
                                                text-align="left" font-weight="bold" border-right="1pt solid black">
                                    <fo:block th:text="${marks.subjectName}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  padding="1mm" border-right="1pt solid black">
                                    <fo:block th:text="${marks.term1FA}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  padding="1mm" border-right="1pt solid black">
                                    <fo:block th:text="${marks.term1SA}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  padding="1mm" border-right="1pt solid black">
                                    <fo:block th:text="${marks.term1IA}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  padding="1mm" border-right="1pt solid black">
                                    <fo:block th:text="${marks.grade == 0.0 ? '' : marks.grade}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell  padding="1mm" border-right="1pt solid black">
                                    <fo:block th:text="${marks.percentage == 0.0 ? '' : marks.percentage}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block border-width="1mm" font-size="9pt" space-before="8" font-family="Times New Roman, serif"
                          th:if="${model.body.secondTable.marks != null and #lists.size(model.body.secondTable.marks) > 0}">
                    <fo:table border="1pt solid black" text-align="center">
                        <fo:table-column column-width="50%" />
                        <fo:table-column column-width="50%" />
                        <fo:table-header>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" font-weight="bold"
                                               padding="1mm">
                                    <fo:block >CO-SCHOLASTIC AREAS</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold"
                                               padding="1mm">
                                    <fo:block>GRADE</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body font-size="9pt">
                            <fo:table-row th:each="marks : ${model.body.secondTable.marks}">
                                <fo:table-cell border="1pt solid black" padding="1mm"
                                               text-align="left" font-weight="bold">
                                    <fo:block th:text="${marks.name}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.grade}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block border-width="1mm" font-size="9pt" space-before="20" font-family="Times New Roman, serif" space-after="5pt">
                    <fo:table border="1pt solid black">
                        <fo:table-column column-width="15%" />
                        <fo:table-column column-width="18%" />
                        <fo:table-column column-width="15%" />
                        <fo:table-column column-width="18%" />
                        <fo:table-column column-width="15%" />
                        <fo:table-column column-width="19%" />
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold" padding="1mm" border-left="1pt solid black" border-bottom="1pt solid black">
                                    <fo:block>RESULT</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border-bottom="1pt solid black">
                                    <fo:block>: <fo:inline th:text="${model.body.thirdTable.result}"></fo:inline></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" padding="1mm" border-left="1pt solid black" border-bottom="1pt solid black">
                                    <fo:block>ACADEMIC%</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm" border-bottom="1pt solid black">
                                    <fo:block>: <fo:inline th:text="${model.body.thirdTable.academic}"></fo:inline></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" padding-left="1mm"  border-left="1pt solid black" border-bottom="1pt solid black">
                                    <fo:block>OVERALL%</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border-bottom="1pt solid black" >
                                    <fo:block>: <fo:inline th:text="${model.body.thirdTable.overall}"></fo:inline></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold" padding="1mm" border-left="1pt solid black" border-bottom="1pt solid black">
                                    <fo:block>RANK</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm">
                                    <fo:block>: <fo:inline th:text="${model.body.thirdTable.rank}"></fo:inline></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" border-left="1pt solid black" padding="1mm">
                                    <fo:block>ATTENDANCE</fo:block>
                                </fo:table-cell>
                                <fo:table-cell padding="1mm">
                                    <fo:block>: <fo:inline th:text="${model.body.thirdTable.attendance}"></fo:inline></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold" padding-left="1mm"  border-left="1pt solid black" border-bottom="1pt solid black">
                                    <fo:block>DATE</fo:block>
                                </fo:table-cell>
                                <fo:table-cell  >
                                    <fo:block>: <fo:inline >05/10/2024</fo:inline></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>


                <fo:block border-width="1mm" font-size="9pt" space-before="8pt" >
                    <fo:table border="1pt solid black">
                        <fo:table-column column-width="100%"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell padding="2mm" padding-bottom="10mm" >
                                    <fo:block font-weight="bold" font-family="Times New Roman, serif">
                                        GENERAL REMARK:
                                        <fo:inline font-weight="normal" th:text="${model.body.generalRemark}"/>
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block border-width="1mm" font-size="10pt" space-before="68pt"
                          font-family="Times New Roman, serif">
                    <fo:table border="none">
                        <fo:table-column column-width="30%" />
                        <fo:table-column column-width="40%" />
                        <fo:table-column column-width="30%" />
                        <fo:table-body font-family="Times New Roman, serif" font-size="7pt">
                            <fo:table-row>
                                <fo:table-cell text-align="center" font-weight="bold"
                                               padding-top="2mm" border-top="1pt solid black">
                                    <fo:block>CLASS TEACHER'S SIGN</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="left" font-weight="bold">
                                    <fo:block></fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="center" font-weight="bold"
                                               padding-top="2mm" border-top="1pt solid black">
                                    <fo:block>PRINCIPAL'S SIGN</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block border="0.3mm solid black" border-left="6mm solid black" border-right="6mm solid black"  space-before="3mm"></fo:block>
                <fo:block  text-align="center" font-size="9" space-before="3mm"
                           font-weight="bold" font-family="Times New Roman, serif">
                    GRADING SCALE (5 Points of Grading scale are follows)
                </fo:block>
                <fo:block border-width="1mm" font-family="Times New Roman, serif"  space-before="2pt" >
                    <fo:table border="none">
                        <fo:table-column column-width="29mm" />
                        <fo:table-column column-width="29mm" />
                        <fo:table-column column-width="29mm" />
                        <fo:table-column column-width="29mm" />
                        <fo:table-column column-width="29mm" />
                        <fo:table-column column-width="29mm" />
                        <fo:table-body font-family="Times New Roman, serif" font-size="7pt"
                                       font-weight="bold">
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" padding="1mm"
                                               font-weight="bold">
                                    <fo:block text-align="center">Marks Range</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block text-align="center">86-100</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block text-align="center">66-85</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block text-align="center">46-65</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block text-align="center">35-45</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block text-align="center">BELOW 35</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" padding="1mm"
                                               font-weight="bold">
                                    <fo:block text-align="center">Grade</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block text-align="center">A</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block text-align="center">B</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block text-align="center">C</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block text-align="center">D</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block text-align="center">E</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <fo:block border-width="1mm" font-size="7pt" space-before="7pt" font-weight="bold"
                          font-family="Times New Roman, serif" text-align="center"  wrap-option="no-wrap">
                    NOTE: INTERNAL EVALUATION DIVISION: COMP. ACTIVITIES (10M), ATT. (10M), PTM (5M),HOLIDAY ASS (15M), VIVA (10M)
                </fo:block>
                <fo:block border-width="1mm" font-size="7pt" space-before="8pt" font-weight="bold"
                          font-family="Times New Roman, serif" text-align="center" wrap-option="no-wrap" padding-top="-2mm">
                    *F.A:FORMATIVE ASSESSMENT,S.A:SUMMATIVE ASSESSMENT,I.A:INTERNAL ASSESSMENT
                </fo:block>
                <fo:block border-width="1mm" font-size="7pt" space-before="8pt" font-weight="bold"
                          font-family="Times New Roman, serif" text-align="center" wrap-option="no-wrap" padding-top="-2mm">
                    ML - MEDICAL LEAVE,PL - PERMITTED LEAVE,AB - ABSENT
                </fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
</fo:root>