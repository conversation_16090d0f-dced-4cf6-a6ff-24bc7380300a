<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="invoice" page-height="110mm" page-width="200mm">
            <fo:region-body margin="0mm" />
            <fo:region-before margin="0mm" />
        </fo:simple-page-master>
    </fo:layout-master-set>
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <!-- WaterMark Image -->
            <fo:block-container absolute-position="absolute" >
                <fo:block>
                    <fo:external-graphic src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/2.png')"
                                         content-width="200mm"
                                         content-height="200mm"/>
                </fo:block>
            </fo:block-container>
            <!-- logo -->
            <fo:block-container position="absolute" top="6mm" left="165mm" >
                <fo:block>
                    <fo:external-graphic src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/30+Years+Logo+White.png')"
                                         content-width="20%" content-height="20%"/>
                </fo:block>
            </fo:block-container>
            <fo:block-container position="absolute" top="6mm" left="23mm" th:with="orgSlug=${model.body.orgSlug}">
                <fo:block th:if="${orgSlug == 'pal988947' || orgSlug == 'pal556078' || orgSlug == 'pal233196'}">
                    <fo:external-graphic src="url('https://images.wexledu.com/live-worksheets/wexl-internal/20240901043131721.png')"
                                         content-width="10%" content-height="10%"/>
                </fo:block>
                <fo:block th:if="${orgSlug == 'pal454783' || orgSlug == 'pal174599' || orgSlug == 'pal332908'}">
                    <fo:external-graphic src="url('https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/PIS.png')"
                                         content-width="10%" content-height="10%"/>
                </fo:block>
            </fo:block-container>
            <fo:block-container absolute-position="absolute" width="100%" height="100%">
                <fo:table border="none">
                    <fo:table-column column-width="35mm" />
                    <fo:table-column column-width="130mm" />
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding-top="6mm" th:with="orgSlug=${model.body.orgSlug}">
                                <fo:block th:if="${orgSlug == 'dps688668' || orgSlug == 'del909850' || orgSlug == 'del189476' || orgSlug == 'del765517' || orgSlug == 'del217242'}"
                                          font-size="19pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center">
                                    <fo:instream-foreign-object content-width="240%" content-height="23px">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="240%" height="23px">
                                            <text x="10.5" y="21" font-family="PT Serif" font-size="20" fill="gray" opacity="1">
                                                DELHI PUBLIC SCHOOL
                                            </text>
                                            <text x="10" y="20" font-family="PT Serif" font-size="20" fill="black">
                                                DELHI PUBLIC SCHOOL
                                            </text>
                                        </svg>
                                    </fo:instream-foreign-object>
                                </fo:block>

                                <fo:block th:if="${orgSlug == 'pal988947' || orgSlug == 'pal454783' || orgSlug == 'pal556078' || orgSlug == 'pal174599' || orgSlug == 'pal332908' || orgSlug == 'pal233196'}"
                                          font-size="19pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center">
                                    <fo:instream-foreign-object content-width="295%" content-height="23px">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="295%" height="23px">
                                            <text x="10.5" y="21" font-family="Times New Roman, serif" font-size="20" fill="gray" opacity="1">
                                                PALLAVI GROUP OF SCHOOLS
                                            </text>
                                            <text x="10" y="20" font-family="Times New Roman, serif" font-size="20" fill="black">
                                                PALLAVI GROUP OF SCHOOLS
                                            </text>
                                        </svg>
                                    </fo:instream-foreign-object>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding-bottom="3mm">
                                <fo:block font-size="8pt" font-weight="bold"   font-family="Times New Roman, serif" text-align="center" >
                                    <fo:inline th:replace="report-card/dps/fragment.xml :: dps688668Participation (orgSlug=${model.body.orgSlug}, participationcertificate=${model.body.className})"></fo:inline>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block font-size="22pt" font-weight="bold" font-family="Helvetica" text-align="center" space-after="6pt" color="#38733c" space-before="4mm">
                    CERTIFICATE
                </fo:block>
                <fo:block text-align="center" margin-left="80mm" margin-right="50mm" space-after="5pt">
                    <fo:instream-foreign-object content-width="80%" content-height="80%">
                        <svg width="350" height="35" xmlns="http://www.w3.org/2000/svg">
                            <polygon points="30,50 270,50 230,0 0,0" fill="#38733c" stroke="#138808" stroke-width="1"/>
                            <text x="36%" y="68%" font-size="20pt" font-family="Helvetica" font-weight="900" fill="white" text-anchor="middle">
                                OF PARTICIPATION
                            </text>
                        </svg>
                    </fo:instream-foreign-object>
                </fo:block>
                <fo:block space-after="9pt" margin-left="65mm" margin-right="60mm" border-bottom="2pt solid black"> </fo:block>

                <fo:block font-size="13pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="9pt" >
                    THIS CERTIFICATE IS AWARDED TO
                </fo:block>


                <fo:table space-after="8pt">
                    <fo:table-column column-width="55mm" />
                    <fo:table-column column-width="90mm" />
                    <fo:table-column column-width="50mm"/>
                    <fo:table-body>
                        <fo:table-row>

                            <fo:table-cell>
                                <fo:block> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block text-align="center" border-bottom="0.5pt solid black">
                                    <fo:inline font-weight="bold" font-style="italic" font-family="Dancing Script"
                                               padding-top="7pt" padding-bottom="1mm" color="#38733c" font-size="17.8pt"
                                               th:text="${#strings.toUpperCase(model.body.name)}" >
                                    </fo:inline>
                                </fo:block>
                            </fo:table-cell>

                            <fo:table-cell>
                                <fo:block text-align="left">
                                    <fo:inline font-size="10pt" font-family="Helvetica" text-align="right" color="#ff914d" th:text="${model.body.gradeSlug}"> </fo:inline>
                                </fo:block>
                            </fo:table-cell>

                        </fo:table-row>
                    </fo:table-body>
                </fo:table>



                <fo:block font-size="10pt" font-family="Times New Roman, serif" text-align="center" space-after="7pt" color="#38733c">
                    ON THE SUCCESSFUL COMPLETION OF
                    <fo:inline font-weight="bold" th:text="${model.body.rollNumber}"></fo:inline>
                </fo:block>
                <fo:block font-size="10pt"  font-family="Times New Roman, serif" text-align="center" space-after="22pt" color="#38733c">
                    HELD ON
                    <fo:inline font-weight="bold" font-size="10pt" th:text="${model.body.startDate}">
                    </fo:inline>
                </fo:block>
                <fo:table space-after="8pt">
                    <fo:table-column column-width="140mm" />
                    <fo:table-column column-width="40mm" />
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell font-size="12pt" font-style="italic" padding-left="5mm">
                                <fo:block th:text="${#strings.toUpperCase(model.header.schoolName)}">
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block text-align="center" font-size="10pt">PRINCIPAL</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
</fo:root>