<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format" xmlns:xi="http://www.w3.org/2001/XInclude" >
    <fo:layout-master-set>
        <fo:simple-page-master master-name="invoice">
            <fo:region-body margin="18mm" />
        </fo:simple-page-master>
    </fo:layout-master-set>
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body" >
            <fo:block-container width="100%" height="100%" margin-top="0cm" border="2pt solid black" padding="6mm">
                <fo:block-container absolute-position="absolute" top="30%" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="300%" content-height="300%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 100 100">
                                <defs>
                                    <filter id="brightnessFilter">
                                        <feComponentTransfer>
                                            <feFuncR type="linear" slope="25"/> <!-- Adjust the slope value to change brightness -->
                                            <feFuncG type="linear" slope="25"/>
                                            <feFuncB type="linear" slope="25"/>
                                        </feComponentTransfer>
                                    </filter>
                                </defs>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
                <fo:table border="none">
                    <fo:table-column column-width="20mm" />
                    <fo:table-column column-width="120mm" />
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block >
                                    <fo:external-graphic src='url("https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/saisenior_logo.jpeg")'
                                                         content-width="15mm" content-height="scale-to-fit"  />
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block font-size="18pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="3pt"
                                          th:text="${model.header.schoolName}">
                                </fo:block>
                                <fo:block font-size="10pt" font-weight="bold"  font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:text="${model.header.address}">
                                </fo:block>
                                <fo:block font-size="8pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt"
                                          th:text="${model.header.isoData}">
                                </fo:block>
                                <fo:block font-size="14pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="5pt">Record
                                    of
                                    Academic Performance
                                </fo:block>
                                <fo:block font-size="10pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="2pt"
                                          th:text="${model.header.testName}">
                                </fo:block>
                                <fo:block font-size="10pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="2pt"
                                          th:text="${model.header.testType}">
                                </fo:block>
                                <fo:block font-size="10pt" font-weight="bold" font-family="Times New Roman, serif" text-align="center" space-after="10pt"
                                          th:text="${model.header.academicYear}">
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif" space-before="5mm" space-after="2pt">
                    <fo:table border="none">
                        <fo:table-column column-width="33mm" />
                        <fo:table-column column-width="85mm" />
                        <fo:table-column column-width="26mm" />
                        <fo:table-column column-width="30mm" />
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="2mm">Name of the Student&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" margin-bottom="2mm" th:text="${model.body.name}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="2mm">Class &amp; Section &#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" margin-bottom="2mm" th:text="${model.body.className}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="2mm">Student Id&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" margin-bottom="2mm" th:text="${model.body.rollNumber}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="2mm">Roll No&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" margin-bottom="2mm" th:text="${model.header.studentId}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="2mm">Mother's Name&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" margin-bottom="2mm" th:text="${model.body.mothersName}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block margin-bottom="2mm">Date of Birth&#160;&#160;&#160;&#160;&#160;&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block font-weight="bold" margin-bottom="2mm" th:text="${model.body.dateOfBirth}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>

                    </fo:table>
                </fo:block>
                <!-- Report Card Table -->
                <fo:block border-width="1mm" font-size="9pt" font-family="Times New Roman, serif" text-align="center" >
                    <fo:block th:text="${model.body.firstTable.title}"  font-size="9" font-weight="bold" font-family="Times New Roman, serif"></fo:block>
                    <fo:table border="1pt solid black">
                        <fo:table-column  column-width="9mm"  />
                        <fo:table-column column-width="70mm" />
                        <fo:table-column column-width="31mm"  />
                        <fo:table-column column-width="31mm"  />
                        <fo:table-column column-width="31mm"  />
                        <fo:table-header font-size="9pt" >
                            <fo:table-row >
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm">
                                    <fo:block>S.NO</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="left">
                                    <fo:block>SUBJECT</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                    <fo:block>Maximum Marks</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                    <fo:block>Marks Obtained</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                    <fo:block>Grade Obtained</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body>
                            <fo:table-row th:each="marks : ${model.body.firstTable.marks}">
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.sno}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="left">
                                    <fo:block th:text="${marks.subject}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.maximumMarks}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.marksObtained}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm">
                                    <fo:block th:text="${marks.gradeObtained}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" padding-left="10mm" padding="1mm" font-family="Times New Roman, serif"
                                               number-columns-spanned="2" text-align="left"  font-weight="bold" >
                                    <fo:block>TOTAL</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" font-family="Times New Roman, serif">
                                    <fo:block th:text="${model.body.firstTable.totals.overallMarks}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" font-family="Times New Roman, serif">
                                    <fo:block th:text="${model.body.firstTable.totals.obtainedMarks}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" font-family="Times New Roman, serif">
                                    <fo:block th:text="${model.body.firstTable.totals.overallPercentage}"></fo:block>
                                </fo:table-cell>

                            </fo:table-row>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" padding-left="10mm" padding="1mm" font-family="Times New Roman, serif"
                                               number-columns-spanned="4" text-align="left"  font-weight="bold" >
                                    <fo:block>OVERALL PERCENTAGE/GRADE</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" font-family="Times New Roman, serif">
                                    <fo:block th:text="${model.body.firstTable.totals.overallPercentage}"></fo:block>
                                </fo:table-cell>

                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>



                <!-- Report Card Result Table -->

                <fo:block border-width="1mm" font-size="10pt" space-before="5mm" space-after="35pt">
                    <fo:block font-weight="bold" font-family="Times New Roman, serif" >Remarks: </fo:block>
                </fo:block>
                <fo:block border-width="1mm" font-size="10pt" space-after="3pt" font-family="Times New Roman, serif" space-before="5mm">
                    <fo:block font-weight="bold" space-after="5pt" font-family="Times New Roman, serif">Attendance&#160;:</fo:block>
                    <fo:table border="none">
                        <fo:table-column column-width="52mm" />
                        <fo:table-column column-width="60mm" />
                        <fo:table-column column-width="41mm" />
                        <fo:table-column column-width="39mm" />
                        <fo:table-body font-family="Times New Roman, serif" >
                            <fo:table-row space-after="5pt">
                                <fo:table-cell font-weight="bold">
                                    <fo:block>Total Number of Working Days&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${model.body.attendance.workingDays}"></fo:block>
                                </fo:table-cell>
                                <fo:table-cell font-weight="bold">
                                    <fo:block>Number of Days Present&#160;:</fo:block>
                                </fo:table-cell>
                                <fo:table-cell>
                                    <fo:block th:text="${model.body.attendance.daysPresent}"></fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <!-- Signature Block-->
                <fo:block border-width="1mm" font-size="10pt" space-before="40pt" font-family="Times New Roman, serif">
                    <fo:table border="none">
                        <fo:table-column column-width="50mm" />
                        <fo:table-column column-width="65mm" />
                        <fo:table-column column-width="55mm" />
                        <fo:table-body font-family="Times New Roman, serif">
                            <fo:table-row >
                                <fo:table-cell text-align="left" font-weight="bold">
                                    <fo:block>Sr.Principal</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="center" font-weight="bold">
                                    <fo:block>Head Mistress</fo:block>
                                </fo:table-cell>
                                <fo:table-cell text-align="right" font-weight="bold">
                                    <fo:block>Class Teacher</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
                <!-- Report Card Result Table -->
                <fo:block border-width="1mm" font-size="10pt" space-before="8mm" font-family="Times New Roman, serif" >
                    <fo:block   font-size="10" font-weight="bold" font-family="Times New Roman, serif" space-after="1mm">Grading Scale&#160;:</fo:block>

                    <fo:table border="1pt solid black">
                        <fo:table-column column-width="21.5mm" />
                        <fo:table-column column-width="21.5mm" />
                        <fo:table-column column-width="21.5mm" />
                        <fo:table-column column-width="21.5mm" />
                        <fo:table-column column-width="21.5mm" />
                        <fo:table-column column-width="21.5mm" />
                        <fo:table-column column-width="21.5mm" />
                        <fo:table-column column-width="21.5mm" />

                        <fo:table-header>
                            <fo:table-row>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                    <fo:block>91-100</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" >
                                    <fo:block>81-90</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center">
                                    <fo:block>71-80</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" >
                                    <fo:block>61-70</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" >
                                    <fo:block>51-60</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" >
                                    <fo:block>41-50</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" >
                                    <fo:block>33-40</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" font-weight="bold" padding="1mm" text-align="center" >
                                    <fo:block>32  Below</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body>
                            <fo:table-row >
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                    <fo:block >A1</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                    <fo:block >A2</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                    <fo:block >B1</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                    <fo:block >B2</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                    <fo:block >C1</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                    <fo:block >C2</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                    <fo:block >D</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding="1mm" text-align="center">
                                    <fo:block >E(Failed)</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>
</fo:root>