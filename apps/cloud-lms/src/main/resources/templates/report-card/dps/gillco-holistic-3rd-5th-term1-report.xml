<?xml version="1.0" encoding="UTF-8"?>
<fo:root xmlns:fo="http://www.w3.org/1999/XSL/Format">
    <fo:layout-master-set>
        <fo:simple-page-master master-name="invoice">
            <fo:region-body margin-top="18mm"  margin-left="5mm" />
        </fo:simple-page-master>
    </fo:layout-master-set>

    <!-- 1st Page -->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="-1cm" padding="6mm">
                <fo:block-container absolute-position="absolute" top="-10%" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="278%" content-height="278%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="350%" height="350%" viewBox="0 0 100 100">
                                <image x="0" y="0" width="100%" height="100%" xlink:href="https://s3.ap-southeast-1.wasabisys.com/wexl-student-info-wasabi-nonprod/holistic_progress_report/gillco-background-image.png"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>

                <fo:table border="none" width="100%">
                    <fo:table-column column-width="100%" />
                    <fo:table-body>
                        <fo:table-row>
                            <fo:table-cell>
                                <fo:block padding-top="0.5cm" text-align="center">
                                    <fo:external-graphic
                                            src='url("https://wexl-strapi-images.s3.ap-south-1.amazonaws.com/GILLCO_INTERNATIONAL_SCHOOL_LOGO_New.png")'
                                            content-width="150px"
                                            content-height="auto"
                                            scaling="uniform" />
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <fo:block font-size="22pt" font-weight="bold" text-align="center" space-before="3mm">
                    GILLCO INTERNATIONAL SCHOOL,
                    <fo:block>MOHALI</fo:block>
                </fo:block>

                <fo:block font-size="14pt" font-family="Times New Roman"
                          text-align="center" color="#811c22" font-weight="bold" padding-top="1cm" space-after="3mm">
                    HOLISTIC PROGRESS REPORT
                </fo:block>



                <fo:table margin-left="9mm">
                    <fo:table-column column-width="70mm"/>
                    <fo:table-column column-width="100%"/>
                    <fo:table-column column-width="0mm"/>
                    <fo:table-body>
                        <fo:table-row>
                            <!-- Empty left spacer -->
                            <fo:table-cell>
                                <fo:block/>
                            </fo:table-cell>

                            <!-- Centered SVG Image -->
                            <fo:table-cell>
                                <fo:block text-align="center" padding-bottom="5mm">
                                    <fo:block-container width="34mm" height="38mm"
                                                        border="5pt solid #d5e6e8"
                                                        background-color="white"
                                                        display-align="center"
                                                        text-align="center"
                                                        margin-left="3mm"
                                                        margin-right="auto"
                                                        padding="1mm">
                                        <fo:block margin-right="5mm" font-size="10pt" font-family="Arial, sans-serif" text-align="center">
                                            <th:block th:if="${model.body.allAboutMe != null and model.body.allAboutMe.studentPhoto != null}">
                                                <fo:instream-foreign-object content-width="100%" content-height="100%">
                                                    <svg xmlns="http://www.w3.org/2000/svg"
                                                         xmlns:xlink="http://www.w3.org/1999/xlink"
                                                         width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid meet">
                                                        <defs>
                                                            <filter id="brightnessFilter">
                                                                <feComponentTransfer>
                                                                    <feFuncR type="linear" slope="1"/>
                                                                    <feFuncG type="linear" slope="1"/>
                                                                    <feFuncB type="linear" slope="1"/>
                                                                </feComponentTransfer>
                                                            </filter>
                                                        </defs>
                                                        <image filter="url(#brightnessFilter)"
                                                               x="0" y="0" width="100%" height="100%"
                                                               th:xlink:href="@{${model.body.allAboutMe.studentPhoto}}"/>
                                                    </svg>
                                                </fo:instream-foreign-object>
                                            </th:block>
                                        </fo:block>
                                    </fo:block-container>
                                </fo:block>
                            </fo:table-cell>


                            <fo:table-cell>
                                <fo:block/>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>


                <fo:block-container border="1pt solid black"  background-color="#fdf2e9" padding="5mm"  margin-top="12pt"
                                    margin-bottom="12pt" display-align="center" margin-left="10mm" width="170mm">
                    <fo:block font-size="13pt" line-height="16pt">

                        <fo:table border="none" margin-bottom="5mm" margin-left="-15mm">
                            <fo:table-column column-width="50mm"/>
                            <fo:table-column column-width="115mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">Name of the student :</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                        <fo:block >
                                            <fo:inline  th:text="${model.body.name}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>


                        <fo:table border="none" margin-bottom="5mm" margin-left="-15mm">
                            <fo:table-column column-width="15mm"/>
                            <fo:table-column column-width="35mm"/>
                            <fo:table-column column-width="18mm"/>
                            <fo:table-column column-width="38mm"/>
                            <fo:table-column column-width="20mm"/>
                            <fo:table-column column-width="39mm"/>
                            <fo:table-body >
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">Class</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                        <fo:block text-align="center">
                                            <fo:inline  th:text="${model.body.className}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">Section</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                        <fo:block text-align="center">
                                            <fo:inline  th:text="${model.body.sectionName}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">Roll No.</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                        <fo:block text-align="center">
                                            <fo:inline  th:text="${model.body.rollNo}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>


                        <fo:table border="none" margin-bottom="5mm" margin-left="-15mm">
                            <fo:table-column column-width="33mm"/>
                            <fo:table-column column-width="35mm"/>
                            <fo:table-column column-width="16mm"/>
                            <fo:table-column column-width="28mm"/>
                            <fo:table-column column-width="12mm"/>
                            <fo:table-column column-width="41mm"/>
                            <fo:table-body >
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">Admission No.</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                        <fo:block text-align="left">
                                            <fo:inline  th:text="${model.body.admissionNumber}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">House</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                        <fo:block text-align="left">
                                            <fo:inline  th:text="${model.body.house}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">DOB</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                        <fo:block text-align="left">
                                            <fo:inline  th:text="${model.body.dateOfBirth}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>

                        <fo:table border="none" margin-bottom="5mm" margin-left="-15mm">
                            <fo:table-column column-width="20mm"/>
                            <fo:table-column column-width="145mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">Address</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                        <fo:block >
                                            <fo:inline  th:text="${model.body.address}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>

                        <fo:table border="none" margin-bottom="5mm" margin-left="-15mm">
                            <fo:table-column column-width="33mm"/>
                            <fo:table-column column-width="132mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">Father's Name</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                        <fo:block >
                                            <fo:inline  th:text="${model.body.fatherName}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>

                        <fo:table border="none" margin-bottom="5mm" margin-left="-15mm">
                            <fo:table-column column-width="35mm"/>
                            <fo:table-column column-width="130mm"/>
                            <fo:table-body>
                                <fo:table-row>
                                    <fo:table-cell height="5mm">
                                        <fo:block font-weight="bold">Mother's Name</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell height="5mm" border-bottom="1pt solid black">
                                        <fo:block >
                                            <fo:inline  th:text="${model.body.motherName}"></fo:inline>
                                        </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block-container>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

    <!-- PAGE 2 -->

    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="-1cm" padding="6mm">
                <fo:block-container absolute-position="absolute" top="-17mm" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="262%" content-height="262%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="327%" height="327%" viewBox="0 0 100 100">

                                <image  x="0" y="0" width="100%" height="100%" xlink:href="https://images-ext-1.discordapp.net/external/xAFK3a9LiLSqjG5R6QZuvwaYecT9UHt_QpUp9lnwqFs/https/s3.ap-southeast-1.wasabisys.com/wexl-student-info-wasabi-nonprod/holistic_progress_report/Holistic%2520Progress%2520Report%25202nd%2520page-1.png"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

    <!-- PAGE 3 -->

    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">

            <!-- Background Image -->
            <fo:block-container absolute-position="absolute" top="-10%" left="50%" width="0" height="0">
                <fo:block margin-left="-5mm" text-align="center">
                    <fo:instream-foreign-object content-width="244%" content-height="244%">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="350%" height="350%" viewBox="0 0 100 100">
                            <image x="0" y="0" width="100%" height="100%" xlink:href="https://s3.ap-southeast-1.wasabisys.com/wexl-student-info-wasabi-nonprod/holistic_progress_report/3rd%20page.png"/>
                        </svg>
                    </fo:instream-foreign-object>
                </fo:block>
            </fo:block-container>

            <!-- Student Photo -->
            <fo:block-container absolute-position="absolute" top="38mm" left="45mm" width="28mm" height="38mm"
                                th:if="${model.body.allAboutMe != null and model.body.allAboutMe.studentPhoto != null}">
                <fo:block display-align="center" text-align="center">
                    <fo:instream-foreign-object content-width="100%" content-height="100%">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                             width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid meet">
                            <image x="0" y="0" width="100" height="100" preserveAspectRatio="none"
                                   th:xlink:href="@{${model.body.allAboutMe.studentPhoto}}"/>
                        </svg>
                    </fo:instream-foreign-object>
                </fo:block>
            </fo:block-container>

            <!-- Student age -->
            <fo:block-container absolute-position="absolute" top="28mm" left="45mm" font-weight="bold" font-size="15pt"
                                padding="1mm" >
                <fo:block text-align="center" th:text="${model.body.age}"> </fo:block>
            </fo:block-container>

            <!-- Student birthday -->
            <fo:block-container absolute-position="absolute" top="46mm" left="127mm" width="80mm" height="25mm">
                <fo:block text-align="center">
                    <fo:instream-foreign-object content-width="100%" content-height="100%">
                        <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%">
                            <text x="40" y="20" transform="rotate(14, 40, 20)" font-size="14pt" font-weight="bold" fill="black" text-anchor="middle">
                                <tspan th:text="${model.body.dateOfBirth}"> </tspan>
                            </text>
                        </svg>
                    </fo:instream-foreign-object>
                </fo:block>
            </fo:block-container>

            <!-- Student address -->
            <fo:block-container absolute-position="absolute" top="68.5mm" left="108mm" font-weight="bold" font-size="13pt"
                                padding="1mm" th:if="${model.body.allAboutMe != null and model.body.allAboutMe.liveIn != null}">
                <fo:block text-align="left" th:text="${model.body.allAboutMe.liveIn}"> </fo:block>
            </fo:block-container>

            <!-- Student Family Photo -->
            <fo:block-container absolute-position="absolute" top="95mm" left="35mm" width="50mm" height="55mm"
                                th:if="${model.body.allAboutMe != null and model.body.allAboutMe.familyPhoto != null}">
                <fo:block display-align="center" text-align="center">
                    <fo:instream-foreign-object content-width="140%" content-height="140%">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                             width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="xMidYMid meet">
                            <image x="0" y="0" width="100%" height="100%" preserveAspectRatio="xMidYMid meet"
                                   th:xlink:href="@{${model.body.allAboutMe.familyPhoto}}"/>
                        </svg>
                    </fo:instream-foreign-object>
                </fo:block>
            </fo:block-container>

            <!-- Student friends -->
            <fo:block-container absolute-position="absolute" top="128mm" left="130mm" width="60mm" font-weight="bold"
                                th:if="${model.body.allAboutMe != null and model.body.allAboutMe.friends != null}">

                <th:block th:each="friend, iterStat : ${#strings.arraySplit(model.body.allAboutMe.friends, ',')}">
                    <fo:block-container height="9mm">
                        <fo:block text-align="left" font-family="Arial" padding-top="1mm" font-size="15pt" line-height="9mm">
                            <fo:inline th:text="${friend.trim()}"> </fo:inline>
                        </fo:block>
                    </fo:block-container>
                </th:block>
            </fo:block-container>


            <!-- Student aim -->
            <fo:block-container absolute-position="absolute" top="176mm" left="39mm" font-weight="bold" font-size="15pt"
                                padding="1mm" th:if="${model.body.allAboutMe != null and model.body.allAboutMe.aim != null}">
                <fo:block text-align="left" th:text="${model.body.allAboutMe.aim}"> </fo:block>
            </fo:block-container>

            <!-- Student favColours -->
            <fo:block-container absolute-position="absolute" top="222mm" left="68mm" font-weight="bold" font-size="15pt"
                                padding="1mm" th:if="${model.body.allAboutMe != null and model.body.allAboutMe.favColours != null}">
                <fo:block text-align="left" th:text="${model.body.allAboutMe.favColours}"> </fo:block>
            </fo:block-container>

            <!-- Student favFoods -->
            <fo:block-container absolute-position="absolute" top="234mm" left="68mm" font-weight="bold" font-size="15pt"
                                padding="1mm" th:if="${model.body.allAboutMe != null and model.body.allAboutMe.favFoods != null}">
                <fo:block text-align="left" th:text="${model.body.allAboutMe.favFoods}"> </fo:block>
            </fo:block-container>

            <!-- Student favAnimals -->
            <fo:block-container absolute-position="absolute" top="246mm" left="68mm" font-weight="bold" font-size="15pt"
                                padding="1mm" th:if="${model.body.allAboutMe != null and model.body.allAboutMe.favAnimals != null}">
                <fo:block text-align="left" th:text="${model.body.allAboutMe.favAnimals}"> </fo:block>
            </fo:block-container>

            <!-- Student favFlower -->
            <fo:block-container absolute-position="absolute" top="222mm" left="155mm" font-weight="bold" font-size="15pt"
                                padding="1mm" th:if="${model.body.allAboutMe != null and model.body.allAboutMe.favFlower != null}">
                <fo:block text-align="left" th:text="${model.body.allAboutMe.favFlower}"> </fo:block>
            </fo:block-container>

            <!-- Student favSports -->
            <fo:block-container absolute-position="absolute" top="234mm" left="155mm" font-weight="bold" font-size="15pt" padding="1mm"
                                th:if="${model.body.allAboutMe != null and model.body.allAboutMe.favSports != null}">
                <fo:block text-align="left" th:text="${model.body.allAboutMe.favSports}"> </fo:block>
            </fo:block-container>

            <!-- Student favSubject -->
            <fo:block-container absolute-position="absolute" top="246mm" left="155mm" font-weight="bold" font-size="15pt"
                                padding="1mm" th:if="${model.body.allAboutMe != null and model.body.allAboutMe.favSubject != null}">
                <fo:block text-align="left" th:text="${model.body.allAboutMe.favSubject}"> </fo:block>
            </fo:block-container>

        </fo:flow>
    </fo:page-sequence>

    <!-- PAGE 4 -->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="-1cm">
                <fo:block-container >
                    <fo:block border-width="2mm" font-size="10pt" space-before="2mm" padding-left="30mm">
                        <fo:table table-layout="fixed" width="100%" border-collapse="collapse" >
                            <fo:table-column column-width="37mm"/>
                            <fo:table-column column-width="13.5mm"/>
                            <fo:table-column column-width="13.5mm"/>
                            <fo:table-column column-width="13.5mm"/>
                            <fo:table-column column-width="13.5mm"/>
                            <fo:table-column column-width="13.5mm"/>
                            <fo:table-column column-width="13.5mm"/>
                            <fo:table-column column-width="13.5mm"/>
                            <fo:table-column column-width="13.5mm"/>
                            <fo:table-column column-width="13.5mm"/>
                            <fo:table-column column-width="13.5mm"/>
                            <fo:table-column column-width="13.5mm"/>
                            <fo:table-column column-width="13.5mm"/>

                            <fo:table-header font-weight="bold"  >
                                <fo:table-row>
                                    <fo:table-cell border="1pt solid black" height="7mm" text-align="center" number-columns-spanned="13"
                                                   background-color="#66FF66">
                                        <fo:block padding-top="2.5mm">ATTENDANCE</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-header>
                            <fo:table-body border="1pt solid black">
                                <fo:table-row border="1pt solid black" height="7mm" text-align="center" font-weight="bold">
                                    <fo:table-cell border="1pt solid black" padding-top="2mm">
                                        <fo:block >MONTHS</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="2mm">
                                        <fo:block >APR</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="2mm">
                                        <fo:block >MAY</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="2mm">
                                        <fo:block >JUN</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="2mm">
                                        <fo:block >JUL</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="2mm">
                                        <fo:block >AUG</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="2mm">
                                        <fo:block >SEP</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="2mm">
                                        <fo:block >OCT</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="2mm">
                                        <fo:block >NOV</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="2mm">
                                        <fo:block >DEC</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="2mm">
                                        <fo:block >JAN</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="2mm">
                                        <fo:block >FEB</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="2mm">
                                        <fo:block >MAR</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>

                                <fo:table-row border="1pt solid black" height="10mm" font-weight="bold" text-align="center"
                                              th:if="${model.body.attendance.workingDays != null}">
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.workingDays.title}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.workingDays.apr}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.workingDays.may}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.workingDays.jun}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.workingDays.july}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.workingDays.aug}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.workingDays.sep}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.workingDays.oct}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.workingDays.nov}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.workingDays.dec}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.workingDays.jan}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.workingDays.feb}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.workingDays.mar}"> </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>

                                <fo:table-row border="1pt solid black" height="8mm" font-weight="bold" text-align="center"
                                              th:if="${model.body.attendance.attendingDays != null}">
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.attendingDays.title}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.attendingDays.apr}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.attendingDays.may}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.attendingDays.jun}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.attendingDays.july}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.attendingDays.aug}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.attendingDays.sep}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.attendingDays.oct}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.attendingDays.nov}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.attendingDays.dec}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.attendingDays.jan}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.attendingDays.feb}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block th:text="${model.body.attendance.attendingDays.mar}"> </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block-container>

                <fo:block-container>
                    <fo:block border-width="2mm" font-size="10pt" padding-left="30mm" space-before="3mm">
                        <fo:table table-layout="fixed" width="100%" border-collapse="collapse" border="1pt solid black" font-weight="bold">
                            <fo:table-column column-width="35mm"/>
                            <fo:table-column column-width="10mm"/>
                            <fo:table-column column-width="21.2mm"/>
                            <fo:table-column column-width="10mm"/>
                            <fo:table-column column-width="21.2mm"/>
                            <fo:table-column column-width="10mm"/>
                            <fo:table-column column-width="41.2mm"/>
                            <fo:table-column column-width="10mm"/>
                            <fo:table-column column-width="29mm"/>
                            <fo:table-column column-width="9mm"/>
                            <fo:table-column column-width="2mm"/>

                            <fo:table-header font-weight="bold"  >
                                <fo:table-row>
                                    <fo:table-cell border="1pt solid black" height="7mm" text-align="center" number-columns-spanned="11"
                                                   background-color="#66FF66">
                                        <fo:block padding-top="2.5mm">INTERESTED (I am Interested in)</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-header>
                            <fo:table-body >
                                <fo:table-row  height="3mm" >
                                    <fo:table-cell number-columns-spanned="11">
                                        <fo:block />
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row  text-align="center">
                                    <fo:table-cell padding-top="4mm">
                                        <fo:block >Reading</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell padding-top="2mm">
                                        <fo:block>
                                            <fo:table table-layout="fixed" width="100%">
                                                <fo:table-column column-width="10mm"/>
                                                <fo:table-body>
                                                    <fo:table-row>
                                                        <fo:table-cell border="1pt solid black" height="6mm" text-align="center" vertical-align="middle">
                                                            <fo:block  font-size="15pt" th:text="${model.body.interests.reading ? '✔' : ''}"/>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell padding-top="4mm">
                                        <fo:block >Dancing</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell padding-top="2mm">
                                        <fo:block>
                                            <fo:table table-layout="fixed" width="100%">
                                                <fo:table-column column-width="10mm"/>
                                                <fo:table-body>
                                                    <fo:table-row>
                                                        <fo:table-cell border="1pt solid black" height="6mm" text-align="center" vertical-align="middle">
                                                            <fo:block  font-size="15pt" th:text="${model.body.interests.dancing ? '✔' : ''}"/>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding-top="4mm">
                                        <fo:block >Singing</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell padding-top="2mm">
                                        <fo:block>
                                            <fo:table table-layout="fixed" width="100%">
                                                <fo:table-column column-width="10mm"/>
                                                <fo:table-body>
                                                    <fo:table-row>
                                                        <fo:table-cell border="1pt solid black" height="6mm" text-align="center" vertical-align="middle">
                                                            <fo:block  font-size="15pt" th:text="${model.body.interests.singing ? '✔' : ''}"/>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding-top="2mm">
                                        <fo:block >Playing a Musical instrument</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell padding-top="2mm">
                                        <fo:block>
                                            <fo:table table-layout="fixed" width="100%">
                                                <fo:table-column column-width="10mm"/>
                                                <fo:table-body>
                                                    <fo:table-row>
                                                        <fo:table-cell border="1pt solid black" height="6mm" text-align="center" vertical-align="middle">
                                                            <fo:block  font-size="15pt" th:text="${model.body.interests.musicalInstrument ? '✔' : ''}"/>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding-top="4mm">
                                        <fo:block >Sport or Games</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell padding-top="2mm">
                                        <fo:block>
                                            <fo:table table-layout="fixed" width="100%">
                                                <fo:table-column column-width="10mm"/>
                                                <fo:table-body>
                                                    <fo:table-row>
                                                        <fo:table-cell border="1pt solid black" height="6mm" text-align="center" vertical-align="middle">
                                                            <fo:block  font-size="15pt" th:text="${model.body.interests.sportsOrGames ? '✔' : ''}"/>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell>
                                        <fo:block/>
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row  height="5mm" >
                                    <fo:table-cell number-columns-spanned="11">
                                        <fo:block />
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row  text-align="center">
                                    <fo:table-cell padding-top="2mm">
                                        <fo:block >Creative Writing</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell >
                                        <fo:block>
                                            <fo:table table-layout="fixed" width="100%">
                                                <fo:table-column column-width="10mm"/>
                                                <fo:table-body>
                                                    <fo:table-row>
                                                        <fo:table-cell border="1pt solid black" height="6mm" text-align="center" vertical-align="middle">
                                                            <fo:block  font-size="15pt" th:text="${model.body.interests.writing ? '✔' : ''}"/>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell padding-top="2mm">
                                        <fo:block >Gardening</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell >
                                        <fo:block>
                                            <fo:table table-layout="fixed" width="100%">
                                                <fo:table-column column-width="10mm"/>
                                                <fo:table-body>
                                                    <fo:table-row>
                                                        <fo:table-cell border="1pt solid black" height="6mm" text-align="center" vertical-align="middle">
                                                            <fo:block  font-size="15pt" th:text="${model.body.interests.gardening ? '✔' : ''}"/>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding-top="2mm">
                                        <fo:block >Yoga</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell >
                                        <fo:block>
                                            <fo:table table-layout="fixed" width="100%">
                                                <fo:table-column column-width="10mm"/>
                                                <fo:table-body>
                                                    <fo:table-row>
                                                        <fo:table-cell border="1pt solid black" height="6mm" text-align="center" vertical-align="middle">
                                                            <fo:block  font-size="15pt" th:text="${model.body.interests.yoga ? '✔' : ''}"/>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding-top="2mm">
                                        <fo:block >Art</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell>
                                        <fo:block>
                                            <fo:table table-layout="fixed" width="100%">
                                                <fo:table-column column-width="10mm"/>
                                                <fo:table-body>
                                                    <fo:table-row>
                                                        <fo:table-cell border="1pt solid black" height="6mm" text-align="center" vertical-align="middle">
                                                            <fo:block  font-size="15pt" th:text="${model.body.interests.art ? '✔' : ''}"/>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  padding-top="2mm">
                                        <fo:block >Craft</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell >
                                        <fo:block>
                                            <fo:table table-layout="fixed" width="100%">
                                                <fo:table-column column-width="10mm"/>
                                                <fo:table-body>
                                                    <fo:table-row>
                                                        <fo:table-cell border="1pt solid black" height="6mm" text-align="center" vertical-align="middle">
                                                            <fo:block  font-size="15pt" th:text="${model.body.interests.craft ? '✔' : ''}"/>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell>
                                        <fo:block/>
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row  height="5mm" >
                                    <fo:table-cell number-columns-spanned="11">
                                        <fo:block />
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row  text-align="center">
                                    <fo:table-cell padding-top="2mm">
                                        <fo:block >Cooking</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell>
                                        <fo:block>
                                            <fo:table table-layout="fixed" width="100%">
                                                <fo:table-column column-width="10mm"/>
                                                <fo:table-body>
                                                    <fo:table-row>
                                                        <fo:table-cell border="1pt solid black" height="6mm" text-align="center" vertical-align="middle">
                                                            <fo:block  font-size="15pt" th:text="${model.body.interests.cooking ? '✔' : ''}"/>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell padding-top="2mm">
                                        <fo:block >Others</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell >
                                        <fo:block>
                                            <fo:table table-layout="fixed" width="100%">
                                                <fo:table-column column-width="10mm"/>
                                                <fo:table-body>
                                                    <fo:table-row>
                                                        <fo:table-cell border="1pt solid black" height="6mm" text-align="center" vertical-align="middle">
                                                            <fo:block  font-size="15pt" th:text="${model.body.interests.others ? '✔' : ''}"/>
                                                        </fo:table-cell>
                                                    </fo:table-row>
                                                </fo:table-body>
                                            </fo:table>
                                        </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell padding-top="2mm" number-columns-spanned="2">
                                        <fo:block >Please Specify</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell  border-bottom="1pt solid black" number-columns-spanned="4">
                                        <fo:block th:text="${model.body.interests.specify}" text-align="left" padding-top="2mm"> </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row  height="5mm" >
                                    <fo:table-cell number-columns-spanned="11">
                                        <fo:block />
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row  height="5mm" >
                                    <fo:table-cell number-columns-spanned="10" border-bottom="1pt solid black">
                                        <fo:block />
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row  height="5mm" >
                                    <fo:table-cell number-columns-spanned="11">
                                        <fo:block />
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block-container>
                <fo:block-container >
                    <fo:block border-width="2mm" font-size="10pt" space-before="2mm" padding-left="30mm">
                        <fo:table table-layout="fixed" width="100%" border-collapse="collapse" border="1pt solid black">
                            <fo:table-column column-width="26mm"/>
                            <fo:table-column column-width="41mm"/>
                            <fo:table-column column-width="27mm"/>
                            <fo:table-column column-width="39mm"/>
                            <fo:table-column column-width="25mm"/>
                            <fo:table-column column-width="41mm"/>

                            <fo:table-header font-weight="bold"  >
                                <fo:table-row>
                                    <fo:table-cell  height="7mm" text-align="center" number-columns-spanned="6"
                                                    background-color="#66FF66">
                                        <fo:block padding-top="2.5mm">STUDENT'S MEDICAL PROFILE</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-header>
                            <fo:table-body border="1pt solid black" text-align="right">
                                <fo:table-row th:if="${model.body.medicalProfile == null}">
                                    <fo:table-cell number-columns-spanned="6">
                                        <fo:block> </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row height="10mm" th:if="${model.body.medicalProfile != null}">
                                    <fo:table-cell >
                                        <fo:block font-weight="bold" padding-top="8mm">Blood Group </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border-bottom="1pt solid black" >
                                        <fo:block text-align="center" padding-top="8mm" th:text="${model.body.medicalProfile.bloodGroup}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell >
                                        <fo:block font-weight="bold" padding-top="8mm" text-align="left">Height(im cms)</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border-bottom="1pt solid black">
                                        <fo:block text-align="center" padding-top="8mm" th:text="${model.body.medicalProfile.height}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell >
                                        <fo:block font-weight="bold" padding-top="8mm">Weight(in kg)</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border-bottom="1pt solid black">
                                        <fo:block text-align="center" padding-top="8mm" th:text="${model.body.medicalProfile.weight}"> </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row height="10mm" text-align="right">
                                    <fo:table-cell>
                                        <fo:block font-weight="bold" padding-top="8mm">Dental</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border-bottom="1pt solid black">
                                        <fo:block text-align="center" padding-top="8mm" th:text="${model.body.medicalProfile.dental}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell >
                                        <fo:block font-weight="bold" padding-top="8mm">Eye Sight(R)</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border-bottom="1pt solid black">
                                        <fo:block text-align="center" padding-top="8mm" th:text="${model.body.medicalProfile.rightEyeSight}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell >
                                        <fo:block font-weight="bold" padding-top="8mm">(L)</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border-bottom="1pt solid black">
                                        <fo:block text-align="center" padding-top="8mm" th:text="${model.body.medicalProfile.leftEyeSight}"> </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row  height="5mm" >
                                    <fo:table-cell number-columns-spanned="6">
                                        <fo:block />
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block-container>
                <fo:block-container >
                    <fo:block border-width="2mm" font-size="10pt" space-before="2mm" padding-left="30mm">
                        <fo:table table-layout="fixed" width="100%" border-collapse="collapse" border="1pt solid black">
                            <fo:table-column column-width="101mm"/>
                            <fo:table-column column-width="49mm"/>
                            <fo:table-column column-width="49mm"/>

                            <fo:table-header font-weight="bold"  border="1pt solid black">
                                <fo:table-row>
                                    <fo:table-cell  height="7mm" text-align="center" number-columns-spanned="3"
                                                    background-color="#66FF66">
                                        <fo:block padding-top="2.5mm">SELF &amp; PEER ASSESSMENT</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row height="6mm">
                                    <fo:table-cell border="1pt solid black" number-rows-spanned="2">
                                        <fo:block font-weight="bold" padding-top="8mm" text-align="center">Life Skills</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block text-align="center">Self Assessment</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="4mm">
                                        <fo:block font-weight="bold" text-align="center" >Peer Assessment</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                                <fo:table-row height="6mm">
                                    <fo:table-cell  border="1pt solid black" padding-top="4mm" text-align="center">
                                        <fo:block >Term 1</fo:block>
                                    </fo:table-cell>

                                    <fo:table-cell  border="1pt solid black" padding-top="4mm" text-align="center">
                                        <fo:block >Term 1</fo:block>
                                    </fo:table-cell>

                                </fo:table-row>
                            </fo:table-header>
                            <fo:table-body border="1pt solid black" text-align="left" >

                                <fo:table-row th:if="${model.body.selfAndPeerAssessments == null or #lists.isEmpty(model.body.selfAndPeerAssessments)}">
                                    <fo:table-cell number-columns-spanned="3" border="1pt solid black" padding="2mm">
                                        <fo:block> </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>

                                <fo:table-row
                                        height="7mm"
                                        th:each="data : ${model.body.selfAndPeerAssessments}"
                                        th:if="${data.skillName != null}">

                                    <fo:table-cell border="1pt solid black" padding-top="2mm">
                                        <fo:block font-weight="bold" th:text="${data.skillName}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="2mm">
                                        <fo:block text-align="center" th:text="${data.saTerm1}"> </fo:block>
                                    </fo:table-cell>

                                    <fo:table-cell border="1pt solid black" padding-top="2mm">
                                        <fo:block text-align="center" th:text="${data.paTerm1}"> </fo:block>
                                    </fo:table-cell>

                                </fo:table-row>

                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block-container>
                <fo:table table-layout="fixed" width="100%" border-collapse="collapse" space-before="2mm">
                    <fo:table-column column-width="40mm"/>
                    <fo:table-column column-width="120mm"/>
                    <fo:table-column column-width="40mm"/>
                    <fo:table-body>
                        <fo:table-row height="8mm" font-weight="bold">
                            <fo:table-cell>
                                <fo:block/>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black" padding-top="3mm" padding-left="5mm">
                                <fo:block>Descriptors:[STRONGLY AGREE,AGREE,DISAGREE]</fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block/>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

    <!-- FIFTH PAGE -->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="-1cm">
                <fo:table table-layout="fixed" width="100%" border-collapse="collapse" space-before="2mm">
                    <fo:table-column column-width="200mm"/>
                    <fo:table-body >
                        <fo:table-row height="8mm" font-weight="bold" background-color="orange">
                            <fo:table-cell border="1pt solid black" padding-top="2mm" text-align="center">
                                <fo:block font-weight="bold">How can I know your child better</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row height="8mm" text-align="left" border="1pt solid black" background-color="#EBA99A">
                            <fo:table-cell padding-top="2mm">
                                <fo:block font-weight="bold">Circle the most appropriate option for each statement </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:table table-layout="fixed" width="100%" border-collapse="collapse" space-before="2mm">
                    <fo:table-column column-width="150mm"/>
                    <fo:table-column column-width="50mm"/>
                    <fo:table-body border="1pt solid black">
                        <fo:table-row height="10mm" text-align="left">
                            <fo:table-cell padding-top="2mm">
                                <fo:block >My child finds the classroom and school a welcoming and safe space</fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding-top="2mm">
                                <fo:block> </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:table table-layout="fixed" width="100%" border-collapse="collapse" space-before="2mm">
                    <fo:table-column column-width="150mm"/>
                    <fo:table-column column-width="50mm"/>
                    <fo:table-body border="1pt solid black">
                        <fo:table-row height="10mm" text-align="left">
                            <fo:table-cell padding-top="2mm">
                                <fo:block >My child Participate in academic and co-cultural activities in school</fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding-top="2mm">
                                <fo:block> </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
                <fo:table table-layout="fixed" width="100%" border-collapse="collapse" space-before="2mm">
                    <fo:table-column column-width="150mm"/>
                    <fo:table-column column-width="50mm"/>
                    <fo:table-body border="1pt solid black">
                        <fo:table-row height="10mm" text-align="left">
                            <fo:table-cell padding-top="2mm">
                                <fo:block >My child finds the grade-level curriculum difficult</fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding-top="2mm">
                                <fo:block >
                                </fo:block>

                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

    <!-- SIXTH PAGE -->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="-1cm">
                <fo:table table-layout="fixed" width="100%" border-collapse="collapse" >
                    <fo:table-column column-width="80mm"/>
                    <fo:table-column column-width="8mm"/>
                    <fo:table-column column-width="80mm"/>
                    <fo:table-column column-width="8mm"/>
                    <fo:table-column column-width="23mm"/>
                    <fo:table-body border="1pt solid black">
                        <fo:table-row height="8mm" font-weight="bold">
                            <fo:table-cell number-columns-spanned="5" border="1pt solid black" padding-top="2mm">
                                <fo:block >
                                    <fo:inline/>
                                    <fo:inline padding-left="23mm" >
                                        My child needs support with</fo:inline></fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row  height="2mm" >
                            <fo:table-cell number-columns-spanned="5">
                                <fo:block />
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row height="6mm" font-weight="bold" text-align="center">
                            <fo:table-cell padding-top="1mm">
                                <fo:block>English Reading</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black">
                                <fo:block padding-top="1mm" font-size="15pt" th:text="${model.body.support.englishReading ? '✔' : ''}"> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding-top="1mm">
                                <fo:block>English oral communication</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black">
                                <fo:block padding-top="1mm" font-size="15pt" th:text="${model.body.support.englishCommunication ? '✔' : ''}"> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block/>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row  height="2mm" >
                            <fo:table-cell number-columns-spanned="5">
                                <fo:block />
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row height="6mm" font-weight="bold" text-align="center">
                            <fo:table-cell padding-top="1mm">
                                <fo:block>Hindi Reading</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black">
                                <fo:block padding-top="1mm" font-size="15pt" th:text="${model.body.support.hindiReading ? '✔' : ''}"> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding-top="1mm">
                                <fo:block>Hindi oral communication</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black">
                                <fo:block padding-top="1mm" font-size="15pt" th:text="${model.body.support.hindiCommunication ? '✔' : ''}"> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block/>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row  height="2mm" >
                            <fo:table-cell number-columns-spanned="5">
                                <fo:block />
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row height="6mm" font-weight="bold" text-align="center">
                            <fo:table-cell padding-top="1mm">
                                <fo:block>Punjabi Reading</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black">
                                <fo:block padding-top="1mm" font-size="15pt" th:text="${model.body.support.punjabiReading ? '✔' : ''}"> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding-top="1mm">
                                <fo:block>Punjabi oral communication</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black">
                                <fo:block padding-top="1mm" font-size="15pt" th:text="${model.body.support.punjabiCommunication ? '✔' : ''}"> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block/>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row  height="2mm" >
                            <fo:table-cell number-columns-spanned="5">
                                <fo:block />
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row height="6mm" font-weight="bold" text-align="center">
                            <fo:table-cell padding-top="1mm">
                                <fo:block>Reading</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black">
                                <fo:block padding-top="1mm" font-size="15pt" th:text="${model.body.support.reading ? '✔' : ''}"> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding-top="1mm">
                                <fo:block>Number and Math</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black">
                                <fo:block padding-top="1mm" font-size="15pt" th:text="${model.body.support.numberAndMath ? '✔' : ''}"> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block/>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row  height="2mm" >
                            <fo:table-cell number-columns-spanned="5">
                                <fo:block />
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row height="6mm" font-weight="bold" text-align="center">
                            <fo:table-cell padding-top="1mm">
                                <fo:block>Self Confidence</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black">
                                <fo:block padding-top="1mm" font-size="15pt" th:text="${model.body.support.selfConfidence ? '✔' : ''}"> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding-top="1mm">
                                <fo:block>Working with other Children</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black">
                                <fo:block padding-top="1mm" font-size="15pt" th:text="${model.body.support.workingWithChildren ? '✔' : ''}"> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block/>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row  height="2mm" >
                            <fo:table-cell number-columns-spanned="5">
                                <fo:block />
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row height="6mm" font-weight="bold" text-align="center">
                            <fo:table-cell padding-top="1mm">
                                <fo:block>Working independently at home</fo:block>
                            </fo:table-cell>
                            <fo:table-cell >
                                <fo:block border="1pt solid black" padding-top="1mm" font-size="15pt" th:text="${model.body.support.workingIndependently ? '✔' : ''}"> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding-top="1mm">
                                <fo:block>Any other subjects area (specify)</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border-bottom="1pt solid black" number-columns-spanned="2">
                                <fo:block padding-top="1mm" text-align="left" th:text="${model.body.support.subjectAreas}"> </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row  height="1mm" >
                            <fo:table-cell number-columns-spanned="5">
                                <fo:block />
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <fo:block-container width="199mm" space-before="2mm" border="1pt solid black">
                    <fo:block background-color="#66FF66" padding-top="2mm" padding-bottom="2mm" text-align="center"
                              font-size="medium">SCHOLASTIC SCORE</fo:block>
                </fo:block-container>
                <fo:block border-width="2mm" font-size="8pt" space-before="2mm" padding-left="30mm">
                    <fo:table table-layout="fixed" width="100%" border-collapse="collapse" >
                        <fo:table-column column-width="54mm"/>
                        <fo:table-column column-width="33mm"/>
                        <fo:table-column column-width="33mm"/>
                        <fo:table-column column-width="33mm"/>
                        <fo:table-column column-width="23mm"/>
                        <fo:table-column column-width="23mm"/>

                        <fo:table-header font-weight="bold"  >
                            <fo:table-row border="1pt solid black" height="4mm" text-align="center">
                                <fo:table-cell border="1pt solid black" padding-top="9mm" number-rows-spanned="2">
                                    <fo:block >SUBJECTS</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding-top="1mm" number-columns-spanned="5">
                                    <fo:block >TERM-1</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row border="1pt solid black" height="7mm" text-align="center">
                                <fo:table-cell border="1pt solid black" padding-top="2mm">
                                    <fo:block  font-size="9pt">Periodic Assessment (10)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding-top="2mm">
                                    <fo:block font-size="9pt">Notebook Assessment/ Portfolio </fo:block>
                                    <fo:block>(5)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding-top="2mm">
                                    <fo:block font-size="9pt">Subject Enrichment/ ASL </fo:block>
                                    <fo:block>(5)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding-top="2mm">
                                    <fo:block font-size="9pt">Term-1</fo:block>
                                    <fo:block>(80)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding-top="2mm">
                                    <fo:block font-size="9pt">TOTAL</fo:block>
                                    <fo:block>(100)</fo:block>
                                </fo:table-cell>

                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body>
                            <fo:table-row border="1pt solid black" height="5mm" text-align="center"
                                          th:if="${model.body.scholosticMandatory == null or #lists.isEmpty(model.body.scholosticMandatory)}">
                                <fo:table-cell border="1pt solid black" padding-top="2mm">
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding-top="2mm">
                                    <fo:block />
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding-top="2mm">
                                    <fo:block />
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding-top="2mm">
                                    <fo:block />
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding-top="2mm">
                                    <fo:block />
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding-top="2mm">
                                    <fo:block />
                                </fo:table-cell>

                            </fo:table-row>

                            <fo:table-row border="1pt solid black" height="4mm" text-align="center"
                                          th:each="data : ${model.body.scholosticMandatory}"
                                          th:if="${model.body.scholosticMandatory != null and not #lists.isEmpty(model.body.scholosticMandatory)}">>
                                <fo:table-cell border="1pt solid black" padding-top="1mm">
                                    <fo:block th:text="${data.subject}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding-top="1mm">
                                    <fo:block th:text="${data.term1Pa}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding-top="1mm">
                                    <fo:block th:text="${data.term1NAP}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding-top="1mm">
                                    <fo:block th:text="${data.term1Sa}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding-top="1mm">
                                    <fo:block th:text="${data.term1Marks}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding-top="1mm">
                                    <fo:block th:text="${data.term1Total}"> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block border-width="2mm" font-size="8pt" space-before="2mm" padding-left="30mm">
                    <fo:table table-layout="fixed" width="100%" border-collapse="collapse" >
                        <fo:table-column column-width="70mm"/>
                        <fo:table-column column-width="43mm"/>
                        <fo:table-column column-width="43mm"/>
                        <fo:table-column column-width="43mm"/>

                        <fo:table-header font-weight="bold"  >
                            <fo:table-row border="1pt solid black" height="4mm" text-align="center">
                                <fo:table-cell border="1pt solid black" padding-top="6mm" number-rows-spanned="2">
                                    <fo:block >SUBJECTS</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding-top="1mm" number-columns-spanned="3">
                                    <fo:block >TERM-1</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                            <fo:table-row border="1pt solid black" height="5.5mm" text-align="center">
                                <fo:table-cell border="1pt solid black" padding-top="2mm">
                                    <fo:block  font-size="9pt">Periodic Assessment (20) </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding-top="2mm">
                                    <fo:block font-size="9pt">Theory (30)</fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding-top="2mm">
                                    <fo:block font-size="9pt">Practical (20)</fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-header>
                        <fo:table-body>
                            <fo:table-row border="1pt solid black" height="4mm" text-align="center"
                                          th:if="${model.body.scholosticOptional == null or #lists.isEmpty(model.body.scholosticOptional)}">
                                <fo:table-cell border="1pt solid black" padding-top="2mm">
                                    <fo:block > </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding-top="2mm">
                                    <fo:block />
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding-top="2mm">
                                    <fo:block />
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding-top="2mm">
                                    <fo:block />
                                </fo:table-cell>
                            </fo:table-row>

                            <fo:table-row border="1pt solid black" height="4mm" text-align="center"
                                          th:each="data : ${model.body.scholosticOptional}"
                                          th:if="${model.body.scholosticOptional != null and not #lists.isEmpty(model.body.scholosticOptional)}">
                                <fo:table-cell border="1pt solid black" padding-top="1mm">
                                    <fo:block th:text="${data.subject}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding-top="1mm">
                                    <fo:block th:text="${data.term1Pa}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding-top="1mm">
                                    <fo:block th:text="${data.term1Theory}"> </fo:block>
                                </fo:table-cell>
                                <fo:table-cell border="1pt solid black" padding-top="1mm">
                                    <fo:block th:text="${data.term1Practical}"> </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>
                </fo:block>

                <fo:block text-align="center" space-before="3mm" margin-left="0mm" margin-right="0mm">
                    <fo:instream-foreign-object content-width="90%" content-height="80mm">
                        <svg xmlns="http://www.w3.org/2000/svg" width="500" height="280" viewBox="0 0 500 280"
                             xmlns:th="http://www.thymeleaf.org">

                            <!-- Chart Title -->
                            <text x="250" y="20" font-size="12" text-anchor="middle" font-weight="bold">SUBJECT-WISE MARKS COMPARISON</text>

                            <!-- Y-Axis -->
                            <line x1="40" y1="40" x2="40" y2="230" stroke="black" stroke-width="1.5"/>

                            <!-- X-Axis -->
                            <line x1="40" y1="230" x2="460" y2="230" stroke="black" stroke-width="1.5"/>

                            <!-- Grid Lines -->
                            <g stroke="#E0E0E0" stroke-width="0.5">
                                <line x1="40" y1="192" x2="460" y2="192"/>
                                <line x1="40" y1="155" x2="460" y2="155"/>
                                <line x1="40" y1="116" x2="460" y2="116"/>
                                <line x1="40" y1="79" x2="460" y2="79"/>
                                <line x1="40" y1="40" x2="460" y2="40"/>
                            </g>

                            <!-- Y-Axis Labels -->
                            <g font-size="8" text-anchor="end">
                                <text x="35" y="235">0</text>
                                <text x="35" y="193">20</text>
                                <text x="35" y="157">40</text>
                                <text x="35" y="120">60</text>
                                <text x="35" y="80">80</text>
                                <text x="35" y="40">100</text>
                            </g>

                            <!-- Bars -->
                            <g th:each="item, iterStat : ${model.body.scholosticMandatory}">
                                <g th:with="
        baseY=230,
        barWidth=20,
        barSpacing=60,
        xStart=60,
        scalingFactor=1.9,
        term1Height=${T(java.lang.Double).parseDouble(item.term1Total) * 1.9},
        x1=${60 + iterStat.index * 60},
        y1=${230 - (T(java.lang.Double).parseDouble(item.term1Total) * 1.9)}">
                                    <!-- Term-1 Bar -->
                                    <rect th:attr="x=${x1}, y=${y1}, width=${barWidth}, height=${term1Height}" fill="#FF8C00"/>
                                    <text th:attr="x=${x1 + 10}, y=${y1 - 5}" font-size="8" text-anchor="middle" th:text="${item.term1Total}"/>

                                    <!-- Subject Label -->
                                    <text th:attr="x=${x1 + 10}, y=245" font-size="8" text-anchor="middle"
                                          th:text="${#strings.substring(item.subject, 0, 3).toUpperCase()}"/>
                                </g>
                            </g>

                            <!-- Legend -->
                            <rect x="180" y="255" width="15" height="12" fill="#FF8C00"/>
                            <text x="200" y="265" font-size="8">Term-1</text>

                        </svg>
                    </fo:instream-foreign-object>
                </fo:block>

            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

    <!-- SEVENTH PAGE -->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="-1cm">
                <fo:block-container width="200mm" >
                    <fo:block background-color="#66FF66" padding-top="2mm"  text-align="center" border="1pt solid black"
                              font-size="medium">SCHOLASTIC AREAS(SKILL BASED ASSESSMENT GRADING)</fo:block>
                </fo:block-container>

                <fo:block th:if="${model.body.scholosticSkillDetails != null and !#lists.isEmpty(model.body.scholosticSkillDetails)}">

                    <fo:block th:each="subjectSkill : ${model.body.scholosticSkillDetails}" space-before="3mm">
                        <fo:table table-layout="fixed" font-size="9pt" width="100%" border-collapse="collapse">
                            <fo:table-column column-width="140mm"/>
                            <fo:table-column column-width="60mm"/>

                            <fo:table-body border="1pt solid black">

                                <!-- Subject Header -->
                                <fo:table-row height="5mm" font-weight="bold">
                                    <fo:table-cell number-columns-spanned="2" border="1pt solid black" padding-top="1mm">
                                        <fo:block text-align="center" color="#A020F0" font-weight="bold" text-transform="uppercase"
                                                  th:text="${subjectSkill.subject}"> </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>

                                <!-- Table Header -->
                                <fo:table-row height="5mm" font-weight="bold">
                                    <fo:table-cell border="1pt solid black" padding-top="1mm">
                                        <fo:block>SKILLS</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="1mm">
                                        <fo:block text-align="center">TERM-I</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>

                                <!-- Skill Rows -->
                                <fo:table-row th:each="skill : ${subjectSkill.skills}" height="5mm">
                                    <fo:table-cell border="1pt solid black" padding-top="1mm">
                                        <fo:block th:text="${skill.skillName}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="1mm">
                                        <fo:block text-align="center" th:text="${skill.term1Value}"> </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block>

                <fo:table table-layout="fixed" width="100%" border-collapse="collapse" space-before="2mm" margin-left="15mm">
                    <fo:table-column column-width="40mm"/>
                    <fo:table-column column-width="130mm"/>
                    <fo:table-body border="1pt solid black">
                        <fo:table-row height="7mm" font-weight="bold" text-align="center" background-color="lightyellow">
                            <fo:table-cell number-columns-spanned="2" border="1pt solid black" padding-top="2mm">
                                <fo:block margin-left="-14mm" color="lightskyblue">DESCRIPTORS</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row border="1pt solid black" text-align="left">
                            <fo:table-cell padding-top="2mm" border="1pt solid black" text-align="left">
                                <fo:block margin-left="-14mm" font-weight="bold">Exemplary</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black" padding-top="1mm">
                                <fo:block margin-left="-14mm">Demonstrates exceptional skills, leadership,
                                    active participation and significant contributions
                                    in Co-curricular activities</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row border="1pt solid black" text-align="left">
                            <fo:table-cell padding-top="2mm" border="1pt solid black" text-align="left">
                                <fo:block margin-left="-14mm" font-weight="bold">Proficient</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black" padding-top="1mm">
                                <fo:block margin-left="-14mm">Demonstrates strong skills,
                                    active participation and valuable contributions
                                    in Co-curricular activities</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row border="1pt solid black" text-align="left">
                            <fo:table-cell padding-top="2mm" border="1pt solid black" text-align="left">
                                <fo:block font-weight="bold" margin-left="-14mm">Progressing</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black" padding-top="1mm">
                                <fo:block margin-left="-14mm">Shows progress in developing skills,
                                    participates moderate, and makes some contributions
                                    in Co-curricular activities</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row border="1pt solid black" text-align="left">
                            <fo:table-cell padding-top="2mm" border="1pt solid black" text-align="left">
                                <fo:block margin-left="-14mm" font-weight="bold">Special needs</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black" padding-top="1mm">
                                <fo:block margin-left="-14mm">Displays limited skills, minimal participation,
                                    and makes little or no contributions in Co-curricular
                                    activities</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

    <!-- EIGHT PAGE -->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="-1cm">
                <fo:block-container width="200mm" space-before="3mm" >
                    <fo:block background-color="#66FF66" padding-top="2mm" padding-bottom="2mm" text-align="center" border="1pt solid black"
                              font-size="medium">CO SCHOLASTIC AREAS(SKILL BASED ASSESSMENT GRADING)</fo:block>
                </fo:block-container>

                <fo:block th:if="${model.body.coScholosticSkillDetails != null and !#lists.isEmpty(model.body.coScholosticSkillDetails)}">

                    <fo:block th:each="subjectSkill : ${model.body.coScholosticSkillDetails}" space-before="3mm">
                        <fo:table table-layout="fixed" font-size="9pt" width="100%" border-collapse="collapse">
                            <fo:table-column column-width="140mm"/>
                            <fo:table-column column-width="60mm"/>

                            <fo:table-body border="1pt solid black">

                                <!-- Subject Header -->
                                <fo:table-row height="5mm" font-weight="bold">
                                    <fo:table-cell number-columns-spanned="2" border="1pt solid black" padding-top="1mm">
                                        <fo:block text-align="center" color="#A020F0" font-weight="bold" text-transform="uppercase"
                                                  th:text="${subjectSkill.subject}"> </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>

                                <!-- Table Header -->
                                <fo:table-row height="5mm" font-weight="bold">
                                    <fo:table-cell border="1pt solid black" padding-top="1mm">
                                        <fo:block>SKILLS</fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="1mm">
                                        <fo:block text-align="center">TERM-I</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>

                                <!-- Skill Rows -->
                                <fo:table-row th:each="skill : ${subjectSkill.skills}" height="5mm">
                                    <fo:table-cell border="1pt solid black" padding-top="1mm">
                                        <fo:block th:text="${skill.skillName}"> </fo:block>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" padding-top="1mm">
                                        <fo:block text-align="center" th:text="${skill.term1Value}"> </fo:block>
                                    </fo:table-cell>
                                </fo:table-row>
                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block>

                <fo:table table-layout="fixed" width="100%" border-collapse="collapse" space-before="2mm" margin-left="15mm">
                    <fo:table-column column-width="50mm"/>
                    <fo:table-column column-width="120mm"/>
                    <fo:table-body border="1pt solid black">
                        <fo:table-row height="8mm" font-weight="bold" text-align="center" background-color="lightyellow">
                            <fo:table-cell number-columns-spanned="2" border="1pt solid black" padding-top="1mm">
                                <fo:block color="lightskyblue" margin-left="-14mm">DESCRIPTORS</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row border="1pt solid black" text-align="left">
                            <fo:table-cell padding-top="1mm" border="1pt solid black" text-align="left">
                                <fo:block font-weight="bold" margin-left="-14mm">Advanced</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black" padding-top="1mm">
                                <fo:block margin-left="-14mm">Demonstrates exceptional skills, leadership,
                                    active participation and significant contributions
                                    in Co-curricular activities</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row  border="1pt solid black" text-align="left">
                            <fo:table-cell padding-top="1mm" border="1pt solid black" text-align="left">
                                <fo:block font-weight="bold" margin-left="-14mm">Proficient</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black" padding-top="1mm">
                                <fo:block margin-left="-14mm">Demonstrates strong skills,
                                    active participation and valuable contributions
                                    in Co-curricular activities</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row border="1pt solid black" text-align="left">
                            <fo:table-cell padding-top="1mm" border="1pt solid black" text-align="left">
                                <fo:block font-weight="bold" margin-left="-14mm">Beginner</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black" padding-top="1mm">
                                <fo:block margin-left="-14mm">Shows progress in developing skills,
                                    participates moderate, and makes some contributions
                                    in Co-curricular activities</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row border="1pt solid black" text-align="left">
                            <fo:table-cell padding-top="1mm" border="1pt solid black" text-align="left">
                                <fo:block font-weight="bold" margin-left="-14mm">Special needs</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black" padding-top="1mm">
                                <fo:block margin-left="-14mm">Displays limited skills, minimal participation,
                                    and makes little or no contributions in Co-curricular
                                    activities</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

    <!-- NINTH PAGE -->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="-1cm">
                <fo:table table-layout="fixed" width="100%" border-collapse="collapse" space-before="1mm" font-size="10pt">
                    <fo:table-column column-width="140mm"/>
                    <fo:table-column column-width="60mm"/>
                    <fo:table-body border="1pt solid black">
                        <fo:table-row font-weight="bold">
                            <fo:table-cell number-columns-spanned="2" border="1pt solid black" padding-top="1mm" background-color="#66FF66">
                                <fo:block text-align="center">SUMMARY SHEET</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row font-weight="bold"  border="1pt solid black">
                            <fo:table-cell padding-top="1mm" padding-left="1mm">
                                <fo:block text-align="left">DOMAINS</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black" padding-top="1mm">
                                <fo:block text-align="center">TERM-1</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row text-align="left" border="1pt solid black"
                                      th:each="data : ${model.body.summarySheetDetails}"
                                      th:if="${model.body.summarySheetDetails != null and not #lists.isEmpty(model.body.summarySheetDetails)}">
                            <fo:table-cell padding-top="1mm" padding-left="1mm">
                                <fo:block th:text="${data.domainName}"> </fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black" padding-top="1mm" padding-left="1mm" text-align="center">
                                <fo:block th:text="${data.term1Value}"> </fo:block>
                            </fo:table-cell>
                        </fo:table-row>

                    </fo:table-body>
                </fo:table>
                <fo:table table-layout="fixed" width="100%" font-size="10pt" border-collapse="collapse" space-before="2mm" space-after="2mm" margin-left="15mm">
                    <fo:table-column column-width="50mm"/>
                    <fo:table-column column-width="120mm"/>
                    <fo:table-body border="1pt solid black">
                        <fo:table-row font-weight="bold" text-align="center" background-color="lightyellow">
                            <fo:table-cell number-columns-spanned="2" border="1pt solid black" padding-top="1mm">
                                <fo:block color="lightskyblue" margin-left="-14mm">DESCRIPTORS</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row border="1pt solid black" text-align="left">
                            <fo:table-cell padding-top="1mm" border="1pt solid black" text-align="left">
                                <fo:block font-weight="bold" margin-left="-14mm">Exemplary</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black" padding-top="1mm">
                                <fo:block margin-left="-14mm">Student’s independent achievement meets
                                    and goes beyond grade level expectations.</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row  border="1pt solid black" text-align="left">
                            <fo:table-cell padding-top="1mm" border="1pt solid black" text-align="left">
                                <fo:block font-weight="bold" margin-left="-14mm">Proficient</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black" padding-top="1mm">
                                <fo:block margin-left="-14mm">Student’s independent achievement
                                    consistently meets grade level expectations</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row  border="1pt solid black" text-align="left">
                            <fo:table-cell padding-top="1mm" border="1pt solid black" text-align="left">
                                <fo:block font-weight="bold" margin-left="-14mm">Progressing</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black" padding-top="1mm">
                                <fo:block margin-left="-14mm">Student’s independent achievement shows inconsistent
                                    application of skills but is progressing towards
                                    meeting grade level expectations.</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row border="1pt solid black" text-align="left">
                            <fo:table-cell padding-top="1mm" border="1pt solid black" text-align="left">
                                <fo:block font-weight="bold" margin-left="-14mm">Special needs</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black" padding-top="1mm">
                                <fo:block margin-left="-14mm">Student’s achievement needs continual
                                    support on grade-level expectations.</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <fo:block th:if="${model.body.classFacilitatorGroupedSkills != null and !#lists.isEmpty(model.body.classFacilitatorGroupedSkills)}">

                    <fo:table table-layout="fixed" width="100%" border-collapse="collapse" font-size="10pt">
                        <fo:table-column column-width="200mm"/>
                        <fo:table-body>
                            <fo:table-row>
                                <fo:table-cell background-color="#CCFF00" border="1pt solid black">
                                    <fo:block font-size="10pt" font-weight="bold" text-align="center" padding="3mm">
                                        CLASS FACILITATOR’S FEEDBACK
                                    </fo:block>
                                </fo:table-cell>
                            </fo:table-row>
                        </fo:table-body>
                    </fo:table>

                    <fo:block th:each="group : ${model.body.classFacilitatorGroupedSkills}" space-before="3mm">
                        <fo:table table-layout="fixed" width="100%" border-collapse="collapse" font-size="10pt">
                            <fo:table-column column-width="140mm"/>
                            <fo:table-column column-width="60mm"/>

                            <fo:table-body border="1pt solid black">

                                <fo:table-row>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" padding="2mm">
                                        <fo:block th:text="${group.subject}"/>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" font-weight="bold" text-align="center" padding="2mm">
                                        <fo:block>TERM-I</fo:block>
                                    </fo:table-cell>
                                </fo:table-row>

                                <!-- Skill Rows -->
                                <fo:table-row th:each="skill : ${group.skills}">
                                    <fo:table-cell border="1pt solid black" padding="2mm">
                                        <fo:block th:text="${skill.skillName}"/>
                                    </fo:table-cell>
                                    <fo:table-cell border="1pt solid black" text-align="center" padding="2mm">
                                        <fo:block th:text="${skill.term1Value}"/>
                                    </fo:table-cell>
                                </fo:table-row>

                            </fo:table-body>
                        </fo:table>
                    </fo:block>
                </fo:block>

                <fo:table table-layout="fixed" width="100%" border-collapse="collapse" space-before="4mm" margin-left="15mm">
                    <fo:table-column column-width="50mm"/>
                    <fo:table-column column-width="120mm"/>
                    <fo:table-body border="1pt solid black">
                        <fo:table-row font-weight="bold" text-align="center" background-color="lightyellow">
                            <fo:table-cell number-columns-spanned="2" border="1pt solid black" padding-top="1mm">
                                <fo:block color="lightskyblue" margin-left="-14mm">DESCRIPTORS</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row border="1pt solid black" text-align="left">
                            <fo:table-cell padding-top="1mm" border="1pt solid black" text-align="left">
                                <fo:block font-weight="bold" margin-left="-14mm">Consistently</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black" padding-top="1mm">
                                <fo:block margin-left="-14mm">Almost all or all of the time</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row  border="1pt solid black" text-align="left">
                            <fo:table-cell padding-top="1mm" border="1pt solid black" text-align="left">
                                <fo:block font-weight="bold" margin-left="-14mm">Usually</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black" padding-top="1mm">
                                <fo:block margin-left="-14mm">More than half of the time</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row  border="1pt solid black" text-align="left">
                            <fo:table-cell padding-top="1mm" border="1pt solid black" text-align="left">
                                <fo:block font-weight="bold" margin-left="-14mm">Sometimes</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black" padding-top="1mm">
                                <fo:block margin-left="-14mm">Less than half of the time</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row border="1pt solid black" text-align="left">
                            <fo:table-cell padding-top="1mm" border="1pt solid black" text-align="left">
                                <fo:block font-weight="bold" margin-left="-14mm">Rarely</fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black" padding-top="1mm">
                                <fo:block margin-left="-14mm">Almost never ot never</fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

    <!-- TENTH PAGE -->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="-1cm">

                <fo:table table-layout="fixed" width="100%" border-collapse="collapse" space-before="2mm" font-size="10pt">
                    <fo:table-column column-width="44mm"/>
                    <fo:table-column column-width="156mm"/>

                    <fo:table-body border="1pt solid black">

                        <fo:table-row>
                            <fo:table-cell number-columns-spanned="2" border="1pt solid black" background-color="#CCFF00" padding="2mm">
                                <fo:block font-weight="bold" text-align="center">PARTICIPATIONS / ACHIEVEMENTS</fo:block>
                            </fo:table-cell>
                        </fo:table-row>

                        <fo:table-row>
                            <fo:table-cell padding="2mm" font-weight="bold">
                                <fo:block>Specific Participations:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding="2mm" >
                                <fo:block th:text="${model.body.participationAchievements.specificParticipation}">
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row>
                            <fo:table-cell number-columns-spanned="2">
                                <fo:block border-bottom="1pt solid black"> </fo:block>
                            </fo:table-cell>
                        </fo:table-row>

                        <fo:table-row>
                            <fo:table-cell padding="2mm" font-weight="bold">
                                <fo:block>Specific Achievements:</fo:block>
                            </fo:table-cell>
                            <fo:table-cell padding="2mm">
                                <fo:block th:text="${model.body.participationAchievements.specificAchievements}">
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <fo:table table-layout="fixed" width="100%" font-size="10pt" border-collapse="collapse" space-before="3mm">
                    <fo:table-column column-width="70mm"/>
                    <fo:table-column column-width="80mm"/>
                    <fo:table-column column-width="50mm"/>
                    <fo:table-body border="ipt solid black">
                        <fo:table-row font-weight="bold">
                            <fo:table-cell number-columns-spanned="3" border="1pt solid black" padding="2mm" background-color="#66FF66" text-align="center">
                                <fo:block>FACILITATORS OBSERVATION AND RECOMMENDATIONS
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row>
                            <fo:table-cell border="1pt solid black">
                                <fo:block>
                                    <fo:table table-layout="fixed" width="100%" border-collapse="collapse" space-before="3mm">
                                        <fo:table-column column-width="5mm"/>
                                        <fo:table-column column-width="8mm"/>
                                        <fo:table-column column-width="5mm"/>
                                        <fo:table-column column-width="52mm"/>
                                        <fo:table-body font-size="11pt">
                                            <fo:table-row height="7mm">
                                                <fo:table-cell number-columns-spanned="4">
                                                    <fo:block text-align="left" padding-left="2mm" font-weight="bold">Area of strength()</fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="7mm">
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell border="1pt solid black">
                                                    <fo:block margin-left="1mm" font-size="15pt" th:text="${model.body.facilitators.areaOfStrength.fb ? '✔' : ''}"> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block padding-top="2mm">Take Feedback Positively</fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="2mm">
                                                <fo:table-cell number-columns-spanned="4">
                                                    <fo:block/>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="7mm">
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell border="1pt solid black" >
                                                    <fo:block margin-left="1mm" font-size="15pt" th:text="${model.body.facilitators.areaOfStrength.wi ? '✔' : ''}"> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block padding-top="2mm">Work independently</fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="2mm">
                                                <fo:table-cell number-columns-spanned="4">
                                                    <fo:block/>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="7mm">
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell border="1pt solid black" >
                                                    <fo:block margin-left="1mm" font-size="15pt" th:text="${model.body.facilitators.areaOfStrength.ec ? '✔' : ''}"> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block padding-top="2mm">Effective communication</fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="2mm">
                                                <fo:table-cell number-columns-spanned="4">
                                                    <fo:block/>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="7mm">
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell border="1pt solid black" >
                                                    <fo:block margin-left="1mm" font-size="15pt" th:text="${model.body.facilitators.areaOfStrength.sft ? '✔' : ''}"> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block padding-top="2mm">Solutions-focused Thinking</fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="2mm">
                                                <fo:table-cell number-columns-spanned="4">
                                                    <fo:block/>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="7mm">
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell border="1pt solid black" >
                                                    <fo:block margin-left="1mm" font-size="15pt" th:text="${model.body.facilitators.areaOfStrength.em ? '✔' : ''}"> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block padding-top="2mm">Empathetic</fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="2mm">
                                                <fo:table-cell number-columns-spanned="4">
                                                    <fo:block/>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="7mm">
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell border="1pt solid black" >
                                                    <fo:block margin-left="1mm" font-size="15pt" th:text="${model.body.facilitators.areaOfStrength.op ? '✔' : ''}"> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block padding-top="2mm">Organization &amp; Prioritization</fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="2mm">
                                                <fo:table-cell number-columns-spanned="4">
                                                    <fo:block/>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="7mm">
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell border="1pt solid black" >
                                                    <fo:block margin-left="1mm" font-size="15pt" th:text="${model.body.facilitators.areaOfStrength.cs ? '✔' : ''}"> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block padding-top="2mm">Collaborative skills</fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="2mm">
                                                <fo:table-cell number-columns-spanned="4">
                                                    <fo:block/>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="7mm">
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell border="1pt solid black" >
                                                    <fo:block margin-left="1mm" font-size="15pt" th:text="${model.body.facilitators.areaOfStrength.re ? '✔' : ''}"> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block padding-top="2mm">Responsible</fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="2mm">
                                                <fo:table-cell number-columns-spanned="4">
                                                    <fo:block/>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="7mm">
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell border="1pt solid black" >
                                                    <fo:block margin-left="1mm" font-size="15pt" th:text="${model.body.facilitators.areaOfStrength.cre ? '✔' : ''}"> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block padding-top="2mm">Creative</fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="2mm">
                                                <fo:table-cell number-columns-spanned="4">
                                                    <fo:block/>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="7mm">
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell border="1pt solid black" >
                                                    <fo:block margin-left="1mm" font-size="15pt" th:text="${model.body.facilitators.areaOfStrength.con ? '✔' : ''}"> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block padding-top="2mm">Concentration</fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="2mm">
                                                <fo:table-cell number-columns-spanned="4">
                                                    <fo:block/>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="7mm">
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell border="1pt solid black" >
                                                    <fo:block margin-left="1mm" font-size="15pt" th:text="${model.body.facilitators.areaOfStrength.ao ? '✔' : ''}"> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block padding-top="2mm">
                                                        Any Other
                                                        <fo:inline padding-left="2mm" border-bottom="1pt solid black" th:text="${model.body.facilitators.areaOfStrength.aoValue}">
                                                        </fo:inline>
                                                    </fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="2mm">
                                                <fo:table-cell number-columns-spanned="4">
                                                    <fo:block/>
                                                </fo:table-cell>
                                            </fo:table-row>
                                        </fo:table-body>
                                    </fo:table>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black">
                                <fo:block>
                                    <fo:table table-layout="fixed" width="100%" border-collapse="collapse" space-before="3mm">
                                        <fo:table-column column-width="3mm"/>
                                        <fo:table-column column-width="8mm"/>
                                        <fo:table-column column-width="3mm"/>
                                        <fo:table-column column-width="66mm"/>
                                        <fo:table-body font-size="11pt">
                                            <fo:table-row height="7mm">
                                                <fo:table-cell number-columns-spanned="4">
                                                    <fo:block text-align="center" font-weight="bold">Barrier of success</fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="7mm">
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell border="1pt solid black" >
                                                    <fo:block margin-left="1mm" font-size="15pt" th:text="${model.body.facilitators.barrierOfSuccess.loa ? '✔' : ''}"> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block padding-top="2mm">Lack of Attention</fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="2mm">
                                                <fo:table-cell number-columns-spanned="4">
                                                    <fo:block/>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="7mm">
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell border="1pt solid black" >
                                                    <fo:block margin-left="1mm" font-size="15pt" th:text="${model.body.facilitators.barrierOfSuccess.lom ? '✔' : ''}"> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block padding-top="2mm">Lack of Motivation</fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="2mm">
                                                <fo:table-cell number-columns-spanned="4">
                                                    <fo:block/>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="7mm">
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell border="1pt solid black" >
                                                    <fo:block margin-left="1mm" font-size="15pt" th:text="${model.body.facilitators.barrierOfSuccess.lop ? '✔' : ''}"> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block padding-top="2mm">Lack of Preparation</fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="2mm">
                                                <fo:table-cell number-columns-spanned="4">
                                                    <fo:block/>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="7mm">
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell border="1pt solid black" >
                                                    <fo:block margin-left="1mm" font-size="15pt" th:text="${model.body.facilitators.barrierOfSuccess.pp ? '✔' : ''}"> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block padding-top="2mm">Peer Pressure</fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="2mm">
                                                <fo:table-cell number-columns-spanned="4">
                                                    <fo:block/>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="7mm">
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell border="1pt solid black" >
                                                    <fo:block margin-left="1mm" font-size="15pt" th:text="${model.body.facilitators.barrierOfSuccess.ug ? '✔' : ''}"> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block padding-top="2mm">Undefined Goals</fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="2mm">
                                                <fo:table-cell number-columns-spanned="4">
                                                    <fo:block/>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="7mm">
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell border="1pt solid black" >
                                                    <fo:block margin-left="1mm" font-size="15pt" th:text="${model.body.facilitators.barrierOfSuccess.di ? '✔' : ''}"> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block padding-top="2mm">Domestic Issues</fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="2mm">
                                                <fo:table-cell number-columns-spanned="4">
                                                    <fo:block/>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="7mm">
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell border="1pt solid black" >
                                                    <fo:block margin-left="1mm" font-size="15pt" th:text="${model.body.facilitators.barrierOfSuccess.ibc ? '✔' : ''}"> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block padding-top="2mm">Inappropriate behaviour in class room</fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="2mm">
                                                <fo:table-cell number-columns-spanned="4">
                                                    <fo:block/>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="7mm">
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell border="1pt solid black" >
                                                    <fo:block margin-left="1mm" font-size="15pt" th:text="${model.body.facilitators.barrierOfSuccess.sii ? '✔' : ''}"> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block padding-top="2mm">Severe illness of injury</fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="2mm">
                                                <fo:table-cell number-columns-spanned="4">
                                                    <fo:block/>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="7mm">
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell border="1pt solid black" >
                                                    <fo:block margin-left="1mm" font-size="15pt" th:text="${model.body.facilitators.barrierOfSuccess.none ? '✔' : ''}"> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block padding-top="2mm">None</fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="2mm">
                                                <fo:table-cell number-columns-spanned="4">
                                                    <fo:block/>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="7mm">
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell border="1pt solid black" >
                                                    <fo:block margin-left="1mm" font-size="15pt" th:text="${model.body.facilitators.barrierOfSuccess.ao ? '✔' : ''}"> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell>
                                                    <fo:block padding-top="2mm">
                                                        Any Other
                                                        <fo:inline padding-left="2mm" border-bottom="1pt solid black" th:text="${model.body.facilitators.barrierOfSuccess.aoValue}">
                                                        </fo:inline>
                                                    </fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="2mm">
                                                <fo:table-cell number-columns-spanned="4">
                                                    <fo:block/>
                                                </fo:table-cell>
                                            </fo:table-row>
                                        </fo:table-body>
                                    </fo:table>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell border="1pt solid black">
                                <fo:block>
                                    <fo:table table-layout="fixed" width="100%" border-collapse="collapse" space-before="3mm">
                                        <fo:table-column column-width="2mm"/>
                                        <fo:table-column column-width="5mm"/>
                                        <fo:table-column column-width="8mm"/>
                                        <fo:table-column column-width="5mm"/>
                                        <fo:table-column column-width="8mm"/>
                                        <fo:table-column column-width="5mm"/>
                                        <fo:table-column column-width="17mm"/>
                                        <fo:table-body font-size="11pt">
                                            <fo:table-row height="7mm">
                                                <fo:table-cell number-columns-spanned="7">
                                                    <fo:block text-align="center" font-weight="bold">Can I help the student
                                                        progress further?</fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="4mm">
                                                <fo:table-cell number-columns-spanned="7">
                                                    <fo:block/>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="5mm">
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell border="1pt solid black">
                                                    <fo:block text-align="center" font-size="15pt"
                                                              th:text="${model.body.facilitators.studentProgress.help == 'yes' ? '✔' : ''}"/>
                                                </fo:table-cell>
                                                <fo:table-cell padding-left="0.5mm" padding-top="1mm">
                                                    <fo:block font-weight="bold">Yes</fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell border="1pt solid black">
                                                    <fo:block text-align="center" font-size="15pt"
                                                              th:text="${model.body.facilitators.studentProgress.help == 'no' ? '✔' : ''}"/>
                                                </fo:table-cell>
                                                <fo:table-cell padding-left="0.5mm" padding-top="1mm">
                                                    <fo:block font-weight="bold">No</fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell border="1pt solid black" >

                                                    <fo:block font-size="15pt" th:text="${model.body.facilitators.studentProgress.help == 'not_sure' ? '✔' : ''}"> </fo:block>
                                                </fo:table-cell>
                                                <fo:table-cell padding-top="1mm">
                                                    <fo:block font-weight="bold">Not sure</fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="2mm">
                                                <fo:table-cell number-columns-spanned="7">
                                                    <fo:block/>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="10mm">
                                                <fo:table-cell number-columns-spanned="7">
                                                    <fo:block/>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="7mm">
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell number-columns-spanned="6">
                                                    <fo:block text-align="left" font-weight="bold">if yes, future steps(s):</fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                            <fo:table-row height="10mm">
                                                <fo:table-cell>
                                                    <fo:block />
                                                </fo:table-cell>
                                                <fo:table-cell number-columns-spanned="6" border-bottom="1pt solid black">
                                                    <fo:block th:text="${model.body.facilitators.studentProgress.steps}"> </fo:block>
                                                </fo:table-cell>
                                            </fo:table-row>
                                        </fo:table-body>
                                    </fo:table>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <fo:table table-layout="fixed" width="100%" border-collapse="collapse" space-before="6mm">
                    <fo:table-column column-width="55mm"/>
                    <fo:table-column column-width="145mm"/>
                    <fo:table-body border="2pt solid red">
                        <fo:table-row >
                            <fo:table-cell>
                                <fo:block font-weight="bold" padding-top="5mm">
                                    Class Facilitator Remarks:
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell border-bottom="1pt solid black">
                                <fo:block padding-top="5mm" th:text="${model.body.classRemarks}"> </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row height="3mm">
                            <fo:table-cell>
                                <fo:block/>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <fo:table table-layout="fixed" width="100%" border-collapse="collapse" space-before="6mm">
                    <fo:table-column column-width="45mm"/>
                    <fo:table-column column-width="155mm"/>
                    <fo:table-body border="2pt solid red">
                        <fo:table-row >
                            <fo:table-cell>
                                <fo:block font-weight="bold" padding-top="5mm">
                                    Principal's Remarks:
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell border-bottom="1pt solid black">
                                <fo:block padding-top="5mm" th:text="${model.body.principleRemarks}"> </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row height="3mm">
                            <fo:table-cell>
                                <fo:block/>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>

                <fo:table table-layout="fixed" width="100%" border-collapse="collapse" space-before="18mm">
                    <fo:table-column column-width="50mm"/>
                    <fo:table-column column-width="100mm"/>
                    <fo:table-column column-width="50mm"/>
                    <fo:table-body>
                        <fo:table-row >
                            <fo:table-cell height="10mm" border-bottom="1pt solid black">
                                <fo:block />
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block />
                            </fo:table-cell>
                            <fo:table-cell height="10mm" border-bottom="1pt solid black">
                                <fo:block />
                            </fo:table-cell>
                        </fo:table-row>
                        <fo:table-row >
                            <fo:table-cell height="10mm" padding-top="2mm">
                                <fo:block >
                                    <fo:inline/>
                                    <fo:inline font-weight="bold"  padding-left="5mm">Class Facilitator</fo:inline>
                                </fo:block>
                            </fo:table-cell>
                            <fo:table-cell>
                                <fo:block />
                            </fo:table-cell>
                            <fo:table-cell height="10mm" padding-top="2mm">
                                <fo:block >
                                    <fo:inline/>
                                    <fo:inline font-weight="bold" padding-left="15mm">Principal</fo:inline>
                                </fo:block>
                            </fo:table-cell>
                        </fo:table-row>
                    </fo:table-body>
                </fo:table>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

    <!-- ELEVENTH PAGE -->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="-1cm" padding="6mm">
                <fo:block-container absolute-position="absolute" top="-17mm" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="250%" content-height="250%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="327%" height="327%" viewBox="0 0 100 100">

                                <image  x="0" y="0" width="100%" height="100%" xlink:href="https://s3.ap-southeast-1.wasabisys.com/wexl-student-info-wasabi-nonprod/holistic_progress_report/Gillco%2012th%20page-1.png"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>


    <!-- Last Page -->
    <fo:page-sequence master-reference="invoice">
        <fo:flow flow-name="xsl-region-body">
            <fo:block-container width="100%" height="100%" margin-top="-1cm" padding="6mm">
                <fo:block-container absolute-position="absolute" top="-17mm" left="50%" width="0%" height="0%">
                    <fo:block text-align="center" color="white" font-size="48pt" font-family="Arial">
                        <fo:instream-foreign-object content-width="262%" content-height="262%">
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="327%" height="327%" viewBox="0 0 100 100">

                                <image  x="0" y="0" width="100%" height="100%" xlink:href="https://images-ext-1.discordapp.net/external/stPH7f10-GZmY8gaTQ0jSXI_sLu8cb_XWrO48vY4sQk/https/s3.ap-southeast-1.wasabisys.com/wexl-student-info-wasabi-nonprod/holistic_progress_report/Holistic%2520Progress%2520Report%25208th%2520page-1.png"/>
                            </svg>
                        </fo:instream-foreign-object>
                    </fo:block>
                </fo:block-container>
            </fo:block-container>
        </fo:flow>
    </fo:page-sequence>

</fo:root>