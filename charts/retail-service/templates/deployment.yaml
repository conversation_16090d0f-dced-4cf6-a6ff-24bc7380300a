apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "retail-service.fullname" . }}
  labels:
    {{- include "retail-service.labels" . | nindent 4 }}
spec:
{{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
{{- end }}
  selector:
    matchLabels:
      {{- include "retail-service.selectorLabels" . | nindent 6 }}
  template:
    metadata:
    {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      labels:
        {{- include "retail-service.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "retail-service.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      volumes:
      - name: config-volume
        configMap:
          name: retail-service
      - name: i18n-volume
        configMap:
          name: i18n
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "dcr.wexledu.com/{{ .Values.image.project }}/{{ .Values.image.name }}:{{ .Chart.Version }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          volumeMounts:
          - name: config-volume
            mountPath: /config
            readOnly: true
          - name: i18n-volume
            mountPath: /i18n
            readOnly: true
          env:
            - name: SPRING_PROFILES_ACTIVE
              value: {{ .Values.spring.profile }}
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /api/actuator/health/liveness
              port: http
            initialDelaySeconds: 120
          readinessProbe:
            httpGet:
              path: /api/actuator/health/readiness
              port: http
            initialDelaySeconds: 70
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
